module.exports = {
  root: true,
  globals: { wx: true },
  parser: 'babel-eslint',
  parserOptions: {
    sourceType: 'module'
  },
  env: {
    browser: true
  },
  // https://github.com/feross/standard/blob/master/RULES.md#javascript-standard-style
  extends: 'standard',
  // required to lint *.wpy files
  plugins: [
    'html'
  ],
  settings: {
    'html/html-extensions': ['.html', '.wpy']
  },
  // add your custom rules here
  'rules': {
    // allow paren-less arrow functions
    'arrow-parens': 0,
    // allow async-await
    'generator-star-spacing': 0,
    // allow debugger during development
    'no-debugger': process.env.NODE_ENV === 'production' ? 2 : 0,
    'space-before-function-paren': 0,
    'camelcase': [0, {
      'properties': 'never'
    }],
    'no-extend-native': [0, { 'exceptions': ['Object'] }],
    'no-mixed-spaces-and-tabs': 'off',
    'no-prototype-builtins': 'off',
    'eqeqeq': 'off',
    'no-useless-escape': 'off',
    'no-new': 'off',
    'no-script-url': 0,
    'no-unused-vars': ['error', { 'vars': 'all', 'args': 'after-used', 'ignoreRestSiblings': false }],
    'no-undef': 'off',
    'no-dynamic-require': 0,
    'jsx-a11y/anchor-is-valid': ['off'],
    'react/no-array-index-key': [0],
    'react/sort-comp': [0],
    'no-nested-ternary': 'off',
    'required': {
      'some': ['nesting', 'id']
    },
    'no-restricted-properties': [2, {
      'object': 'disallowedObjectName',
      'property': 'disallowedPropertyName'
    }, {
        'object': 'disallowedObjectName',
        'property': 'anotherDisallowedPropertyName',
        'message': 'Please use allowedObjectName.allowedPropertyName.'
      }]
  }
}
