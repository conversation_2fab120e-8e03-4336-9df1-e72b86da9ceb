# WePY 到 Taro 项目重构计划

本文档用于追踪 `za-asclepius-applet` (WePY) 项目到 `applet-taro` (Taro) 项目的重构进度。

## 一、 重构核心原则与关键决策

在我们合作过程中，我们总结并确认了以下核心开发规范，这将作为后续所有组件重构的统一标准。

1.  **资源与配置导入**:
    *   **规则**: 统一使用绝对路径 `@/` 从 `src` 目录开始引入。
    *   **本地图片**: 使用 `import` 引入。
        *   **示例**: `import closeIcon from '@/images/icon_close.png';`
    *   **配置变量**: 从 `@/utils/config` 中具名导入 (Named Import)。
        *   **示例**: `import { webviewUrl } from '@/utils/config';`
    *   **静态数据**: 从 `@/utils/staticData` 中具名导入。
        *   **示例**: `import { CDN_PREFIX } from '@/utils/staticData';`

2.  **工具与 API 函数导入**:
    *   **规则**: 同样使用绝对路径，确保来源清晰、统一。
    *   **网络请求**: `fetchJson` 函数从 `@/utils/fetch` 导入。
    *   **埋点 (Tracking)**:
        *   **原则**: 废弃 WePY 中 `getApp().za.customTrack` 形式的全局调用。
        *   **替代方案**: 统一使用从 `@/utils/pageTrack` 导入的 `xflowPushEvent` 函数进行埋点。
        *   **参数映射**: 注意 `app.za.customTrack` 的参数到 `xflowPushEvent` 的转换（如 `event_value` -> `eventTag`, `ZAHLWYY_CLICK_CONTENT` -> `attributes`）。

3.  **组件开发范式 (React Hooks)**:
    *   **父子组件通信**: 废弃 WePY 的 `$invoke` 命令式调用。子组件应通过 `useDidShow` 等生命周期 Hooks 自行管理数据初始化，实现高内聚。
    *   **状态管理**: 使用 `useState` 管理组件内部状态，使用 `useSelector` 从 Redux 获取全局共享状态。
    *   **Taro 特定注意点**: Taro 的 `<Image>` 组件不支持 `alt` 属性，在迁移 JSX 时需移除该属性。

4.  **路由跳转**:
    *   **规则**: 使用 `Taro.navigateTo` API。对于内嵌 H5 的场景，保持与原项目一致的 `url` 结构。
    *   **路径匹配**: 页面跳转的 `url` 必须与 `app.config.ts` 中 `pages` 数组内注册的路径**完全一致**。例如，如果页面注册为 `pages/webview/index`，那么跳转路径必须是 `'/pages/webview/index'`，而不能是 `'/pages/webview'`，因为框架不会自动补全 `/index`。
    *   **示例**: `Taro.navigateTo({ url: `/pages/webview/index?src=${encodeURIComponent('/hospital/doctorlist')}` });`

5.  **样式与单位**:
    *   **`rpx` 单位**: 规则是 `rpx` 单位在 Taro 中完全兼容，迁移样式时无需进行转换。
    *   **固定像素 `px`**: 对于不希望被 Taro 自动转换为 `rpx` 的固定像素值，必须使用大写的 `PX` (或 `Px`) 作为单位。这是为了避免编译工具根据 `designWidth` 进行非预期的缩放。
        *   **示例**: `font-size: 13px;` 必须写为 `font-size: 13PX;` 才能确保它在小程序中被渲染为 13 物理像素。

6.  **协作流程与开发策略**:
    *   **方案先行**: 我将首先提出代码修改方案供您审阅，在得到您的确认后，再执行文件写入操作。
    *   **忽略临时类型错误**: 在组件的功能迁移阶段，我们可以暂时忽略 TypeScript 的类型报错，优先保证逻辑的正确迁移。类型问题可在后续阶段统一处理。

7.  **工具库状态 (Utils)**:
    *   **重要提示**: 原 WePY 项目中的 `src/utils` 目录已整体迁移至 Taro 项目的 `apps/hy/src/utils`。所有工具函数（如 `fetch`, `storage`, `format` 等）均已可用，可直接通过绝对路径 `@/utils/...` 引入。

8.  **`event` 工具函数迁移策略**:
    *   **背景**: 原 WePY 项目中的 `src/utils/event.js` 提供了一个 `event` 高阶函数，用于在执行业务逻辑前进行前置权限检查（如登录、实名认证）。
    *   **问题**: 此函数的设计强依赖于 WePY 的 Class Component 的 `this` 上下文 (如调用 `this._showModal()`)。在 Taro 的函数式组件中，不存在 `this` 实例，也无法直接调用组件内的 State Hook (`setIsShowAuthModal`)。
    *   **决策**: 因此，迁移后的 `event` 函数无法直接在新项目中使用。所有需要前置权限检查的交互，碰到这个问题暂时跳过但是需要 添加一个 todo 方便后面优化。

9.  **全局对象与特殊埋点处理**:
    *   **背景**: 在 WePY 项目中，存在通过 `getApp().za.xxx` 形式调用的全局方法或属性，例如用于分享埋点的 `app.za.para.autoTrack.pageShare`。
    *   **决策**: 这类代码通常与旧的框架强绑定，无法直接迁移。
    *   **规则**: 在重构过程中，如果遇到这类代码，应暂时跳过其功能实现，但**必须**在其对应的位置（如 `useShareAppMessaeg` Hook 内部）添加明确的 `TODO` 注释，说明被遗漏的功能点和原始代码，以便后续统一处理或寻找替代方案。

10. **函数节流 (Throttle) 与防抖 (Debounce) 策略**:
    *   **背景**: 原 WePY 项目中使用了一个全局的 `throttle` 工具函数来防止用户重复点击。这个函数与 React Hooks 的工作方式不完全兼容。
    *   **决策**: 为了在整个项目中统一、可靠地实现节流与防抖功能，我们引入 `ahooks` 库。
    *   **规则**: 所有需要对事件处理函数进行节流或防抖的场景，都**必须**使用 `ahooks` 提供的 `useThrottleFn` 或 `useDebounceFn` Hooks。
    *   **示例 (`useThrottleFn`)**:
        ```typescript
        import { useThrottleFn } from 'ahooks';

        const MyComponent = () => {
          const [value, setValue] = useState(0);

          const expensiveLogic = (newValue) => {
            setValue(newValue);
          };

          const { run: throttledLogic } = useThrottleFn(expensiveLogic, { wait: 500 });

          return <Button onClick={() => throttledLogic(value + 1)}>Click Fast</Button>;
        };
        ```

## 二、 首页 (`index.wpy`) 依赖组件迁移进度

在重构首页之前，我们先迁移其依赖的 UI 组件。

- [x] **Modal**: `za-asclepius-applet/src/components/modal/index.wpy` (已完成)
- [x] **AuthModal**: `za-asclepius-applet/src/components/authModal/index.wpy` (已完成)
- [x] **GoldCore**: `za-asclepius-applet/src/components/goldCore/index.wpy` (已完成)
- [x] **GoodDoctor**: `za-asclepius-applet/src/components/goodDoctor/index.wpy` (已完成)
- [x] **GJModal**: `za-asclepius-applet/src/components/gjModal/index.wpy` (已完成)
- [x] **Guide**: `za-asclepius-applet/src/components/guide/index.wpy` (已完成)
- [x] **CarInsureModal**: `za-asclepius-applet/src/components/carInsureModal/index.wpy` (已完成)
- [x] **FamilyContinuedFee**: `za-asclepius-applet/src/components/continuedFee/index.wpy` (已完成)

> **备注**: `GoldSteel` 和 `CheckInquiry` 在我们开始协作前似乎已迁移。

## 三、 首页 (`index.wpy`) 事件通信机制 (`events`)

为了将 WePY 的 `$emit` 事件机制顺利地重构为 React 的 props 回调，下表记录了 `index.wpy` 中 `events` 对象监听的事件及其来源和触发时机。

| 父组件事件 (`events` key) | 来源子组件 (Taro)              | 触发时机                                                               |
| ------------------------- | -------------------------------- | ---------------------------------------------------------------------- |
| `auth-success`            | `AuthModal`                      | 用户在授权弹窗中成功绑定手机号后。                                     |
| `to-text-inquriy`         | `GoldCore`, `FamilyContinuedFee` | 用户点击“图文问诊”相关按钮时。                                         |
| `to-butler`               | `GoldCore`, `FamilyContinuedFee` | 用户点击“问医管家”相关按钮时。                                         |
| `has-inquiry`             | `CheckInquiry`                   | `CheckInquiry` 组件获取到问诊列表状态（有或无）后，通知父组件。        |
| `show-nodoctor-modal`     | `CheckInquiry`                   | (代码已注释) 原计划在“继续问诊”无医生时触发，当前已禁用。             |
| `has-real-auth`           | `CarInsureModal`                 | (功能未知) 从代码看似乎是实名认证成功后触发，但 `index.wpy` 中未处理。 |
| `to-service-list`         | *未知*                           | (未找到 emit 调用) 可能已废弃，或通过其他机制触发。重构时需重点观察。  |


## 四、 首页 (`pages/index`) 重构任务列表

我们将首页的重构工作拆分为以下四个主要阶段，以便循序渐进地完成。

### 阶段一：页面结构与静态内容迁移
- [x] **1.1: 创建页面文件**: 创建 `pages/index/index.tsx` 和 `pages/index/index.scss`。
- [x] **1.2: 迁移页面样式**: 将 `index.wpy` 中的 `<style>` 内容完整复制到 `index.scss`。
- [x] **1.3: 搭建 JSX 骨架**: 将 `index.wpy` 的 `<template>` 翻译为 JSX，引入并放置所有子组件。
- [x] **1.4: 初始化状态**: 使用 `useState` 定义所有需要的数据状态 (如 `bannerList`, `articleList` 等)，并赋予初始值。

### 阶段二：数据获取与动态渲染
- [x] **2.1: 实现 `onLoad` 数据获取**: 将 `onLoad` 中的逻辑 (如 `fetchResourceList`, `getArticle`) 迁移到 `useEffect(..., [])` 中。
- [x] **2.2: 实现 `onShow` 数据获取**: 将 `onShow` 中的逻辑 (如 `getBannerList`) 迁移到 `useDidShow` Hook 中。
- [x] **2.3: 渲染动态列表**: 将获取到的 `bannerList` 和 `articleList` 数据绑定到 JSX 中，实现动态渲染。

### 阶段三：核心交互与事件处理
- [x] **3.1: 迁移简单方法**: 实现 Banner 点击、文章 Tab 切换等简单的 `onClick` 事件处理。
- [x] **3.2: 替换 `$emit`/`events`**: 根据上文的事件表，为子组件添加 props 回调函数，并实现父页面的处理逻辑。

### 阶段四：高级弹窗与复杂逻辑
- [x] **4.1: 实现静态弹窗控制**: 实现 `AuthModal`, `GJModal` 等弹窗的显示/隐藏逻辑。
- [x] **4.2: 重构动态弹窗 (`homePopup`)**: 重构带队列和延时器的动态弹窗逻辑。
- [x] **4.3: 实现弹窗互斥逻辑**: 实现 `otherModalCanShow` 的相关逻辑，确保弹窗互斥规则正确。

## 五、 WebView 页面 (`webview.wpy`) 重构任务列表

我们采用分阶段的方式迁移 `webview` 页面，确保核心功能优先上线。

### 阶段一：核心功能迁移 (MVP)
- [x] **1.1: 创建页面文件**: 创建 `pages/webview/index.tsx` 和 `pages/webview/index.config.ts`。
- [x] **1.2: 实现 URL 参数解析**: 在 `useEffect` 中使用 `getCurrentInstance` 获取 `src`, `target`, `param` 等路由参数。
- [x] **1.3: 实现动态标题设置**: 根据路由参数中的 `title`，使用 `Taro.setNavigationBarTitle` 动态更新页面标题。
- [x] **1.4: 渲染 `<WebView />` 组件**: 将解析和拼接后的最终 H5 链接作为 `src` 属性传递给 `<WebView />` 组件。

### 阶段二：高级功能迁移 (后续实现)
- [x] **2.1: 实现与 H5 的消息通信**: 为 `<WebView />` 组件添加 `onMessage` 处理器，处理来自 H5 的 `postMessage` 事件。
- [x] **2.2: 迁移商城授权逻辑**: 完整迁移 `checkIsAuth` 相关逻辑，包括调用授权接口、处理 Token，以及在需要时渲染 `<AuthPage />` 组件。
- [x] **2.3: 重构自定义分享逻辑**: 实现 `onShareAppMessage` Hook，并迁移原有的过滤 URL 参数、拼接分享路径等复杂分享功能。
## 六、 个人中心页面 (`personalcenter.wpy`) 重构任务列表

我们将个人中心页面的重构工作拆分为以下四个主要阶段。

### 阶段一：页面结构与静态内容迁移 (Foundation)
- [x] **1.1: 创建页面与配置文件**: 创建 `pages/personalcenter/index.tsx`, `index.scss`, 和 `index.config.ts`。
- [x] **1.2: 迁移页面样式**: 将 `personalcenter.wpy` 中的 `<style>` 内容完整复制到 `index.scss`。
- [x] **1.3: 搭建 JSX 骨架**: 将 `personalcenter.wpy` 的 `<template>` 翻译为 JSX，处理 `wx:if`, `<repeat>`, 数据绑定等。
- [x] **1.4: 引入静态资源**: 确保所有图片、静态文本（如 `CDN_PREFIX`）都通过 `@/` 路径正确导入。

### 阶段二：状态管理与数据获取 (State & Data)
- [x] **2.1: 初始化 State**: 使用 `useState` 定义页面所需的所有数据状态 (如 `patientList`, `phone`, `loading` 等)。
- [x] **2.2: 迁移 `onLoad` 逻辑**: 将 `onLoad` 中的数据获取 (`getUserChannel`, `getBannerList` 等) 迁移到 `useEffect(..., [])` 中。
- [x] **2.3: 迁移 `onShow` 逻辑**: 将 `onShow` 中的逻辑 (检查 token、调用 `getData`) 迁移到 `useDidShow` Hook 中。
- [x] **2.4: 重构数据获取函数**: 将 `getData`, `getPatientList`, `getRedPot` 等 Class 方法重构为组件内的函数，并使用 `fetchJson` 和 `useState` 的 setter 更新状态。

### 阶段三：核心交互与事件处理 (Interactions)
- [x] **3.1: 迁移用户交互方法**: 实现 `swiperChange`, `toMyOrder` 等简单的 `onClick` 事件处理。
- [x] **3.2: 重构 `event()` 高阶函数逻辑**: 移除对 `event()` 的依赖，在每个需要权限的事件处理器（如 `toMyCoupon`）内部手动实现权限检查逻辑（检查 `phone` 状态，若无则显示授权弹窗）。
- [x] **3.3: 处理登录授权**: 重构 `getUserPhone` 逻辑，使用 Taro API 获取手机号并调用后端接口。
- [x] **3.4: 迁移 `events` 监听**: 为 `AuthModal` 组件添加 `onAuthSuccess` 回调 prop，替代原有的 `events` 机制。

### 阶段四：高级逻辑与清理 (Advanced Logic)
- [x] **4.1: 重构自定义分享**: 将 `onShareAppMessage` 逻辑迁移到 `useShareAppMessage` Hook，并为旧的 `app.za.para.autoTrack` 埋点添加 `TODO` 注释。
- [x] **4.2: 替换埋点**: 将所有 `sendIlog` 和 `app.za.customTrack` 调用替换为从 `@/utils/pageTrack` 导入的 `xflowPushEvent`。
- [x] **4.3: 最终检查与确认**: 检查所有 `TODO` 注释，确保所有旧框架依赖（如 `wepy` 实例）都已被移除或标记。

## 七、 H5 页面跳转迁移计划

为了确保从外部 H5 平滑跳转到重构后的 Taro 小程序，我们需要迁移或创建以下页面，以兼容旧的 URL 路径。这是基于对 H5 项目代码的分析以及对 WePY 项目 `app.wpy` 配置文件交叉比对后的最终清单。

- [ ] **任务 1: 迁移 WebView 页面 (`/pages/webview`)**
  - **来源**: `za-asclepius-applet/src/pages/webview.wpy`
  - **目标**: `applet-taro/apps/hy/src/pages/webview.tsx`
  - **说明**: 这是最核心的容器页面，用于承载所有内嵌 H5。需要确保其能正确解析 `src` 等参数。

- [ ] **任务 2: 迁移通用桥接页 (`/pages/common/middleBridgePage`)**
  - **来源**: `za-asclepius-applet/src/pages/common/middleBridgePage.wpy`
  - **目标**: `applet-taro/apps/hy/src/pages/common/middleBridgePage/index.tsx`
  - **说明**: 处理一些特殊的中间跳转逻辑。

- [ ] **任务 3: 迁移登录页 (`/pages/login`)**
  - **来源**: `za-asclepius-applet/src/pages/login.wpy` (或 `authnew.wpy`)
  - **目标**: `applet-taro/apps/hy/src/pages/login.tsx`
  - **说明**: 接收 H5 的登录请求，处理 `returnUrl` 等参数，并唤起授权流程。这是兼容旧 H5 登录流程的关键。

- [ ] **任务 4: 迁移支付页 (`/pages/pay`)**
  - **来源**: `za-asclepius-applet/src/pages/pay.wpy` (或 `jkzl/pay.wpy`)
  - **目标**: `applet-taro/apps/hy/src/pages/pay.tsx`
  - **说明**: 接收 H5 传递的支付参数并发起小程序支付。

- [ ] **任务 5: 迁移小程序跳转页 (`/pages/middleMiniapp`)**
  - **来源**: `za-asclepius-applet/src/pages/middleMiniapp.wpy`
  - **目标**: `applet-taro/apps/hy/src/pages/middleMiniapp.tsx`
  - **说明**: 用于实现小程序内部跳转到另一个小程序的功能。
