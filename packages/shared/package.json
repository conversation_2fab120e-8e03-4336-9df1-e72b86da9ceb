{"name": "shared", "version": "1.0.0", "description": "", "main": "index.js", "exports": {"./fetch": {"default": "./fetch/index.ts"}, "./storage": {"default": "./storage/index.ts"}, "./pages/pdf": {"default": "./pages/pdf/index.tsx"}, "./hooks": {"default": "./hooks/index.ts"}, "./track": {"default": "./track/index.ts"}, "./track/za": {"default": "./track/za/index.js"}}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@tarojs/components": "4.0.8", "@tarojs/plugin-platform-tt": "4.0.8", "@tarojs/plugin-platform-weapp": "4.0.8", "@tarojs/taro": "4.0.8", "lodash.debounce": "^4.0.8", "lodash.throttle": "^4.1.1", "react": "^18.0.0"}}