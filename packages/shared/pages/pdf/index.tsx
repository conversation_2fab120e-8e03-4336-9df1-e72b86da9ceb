import React, {
  useState,
  useEffect,
  useCallback,
} from 'react';
import { WebView, View } from '@tarojs/components';
import Taro, { getCurrentInstance } from '@tarojs/taro';
import { useViewed } from '../../hooks';

function PdfView() {
  const systemInfo = Taro.getDeviceInfo?.() || Taro.getSystemInfoSync();
  const { url = '', download } = getCurrentInstance()?.router?.params || {};
  const [webUrl, setWebURL] = useState('');

  useViewed(() => {
    Taro.navigateBack();
  });

  const downloadPdf = useCallback(() => {
    Taro.showLoading({
      title: '下载中...',
    });
    Taro.downloadFile({
      url: decodeURIComponent(url),
      success: ({ tempFilePath }) => {
        Taro.openDocument({
          filePath: tempFilePath,
          showMenu: true,
        });
      },
      fail: (e) => {
        // setWebURL(url);
        Taro.showModal({
          title: '提示',
          content: '加载失败，点击重试!',
          success(res) {
            if (res.confirm) {
              downloadPdf();
            }
          },
        });
        throw new Error(`pdf 下载失败-> ${JSON.stringify(e)}, url: ${url}`);
      },
      complete: () => {
        Taro.hideLoading();
      },
    });
  }, [url]);

  useEffect(() => {
    (async () => {
      // console.log(systemInfo);
      if (download?.toString() === 'true' || systemInfo.platform === 'android') {
        downloadPdf(url);
      } else {
        setWebURL(url);
      }
    })();
  }, [download, url, downloadPdf, systemInfo]);

  if (webUrl) {
    return (
      <WebView
        src={`${decodeURIComponent(webUrl)}`}
        onError={() => {
          throw new Error(`pdf webview 下载失败-> url: ${url}`);
        }}
      />
    );
  }
  return <View />;
}

export default PdfView;
