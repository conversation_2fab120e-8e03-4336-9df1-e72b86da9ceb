import Taro from '@tarojs/taro';

interface Options {
  env: string;
}

interface CachedData {
  __value: any;
  __storageSync: boolean;
  __expired?: number;
}
class Storage {
  private env: string;

  private cache: Record<string, CachedData>;

  constructor(options: Options) {
    const { env } = options;
    this.env = env;
    this.cache = {};
  }

  private getKey(key: string): string {
    return [this.env, key].filter(Boolean).join('_');
  }

  setStorageSync(key: string, data: any, time?: number): void {
    const storageKey = this.getKey(key);
    delete this.cache[storageKey];
    const cachedData: CachedData = {
      __value: data,
      __storageSync: true,
      __expired: time ? Date.now() + time : undefined,
    };
    Taro.setStorageSync(storageKey, cachedData);
    this.cache[storageKey] = cachedData;
  }

  setStorage(key: string, data: any): Promise<void> {
    const storageKey = this.getKey(key);
    delete this.cache[storageKey];
    return Taro.setStorage({ key: storageKey, data: { __value: data, __storageSync: false } })
      .then(() => {
        this.cache[storageKey] = { __value: data, __storageSync: false };
      })
      .catch((error) => {
        console.error('Failed to set storage:', error);
      });
  }

  getStorageSync(key: string): any {
    const storageKey = this.getKey(key);
    const cachedData = this.cache[storageKey];
    if (cachedData?.__value !== undefined) {
      if (cachedData.__expired && cachedData.__expired < Date.now()) {
        delete this.cache[storageKey];
        return undefined;
      }
      return cachedData.__value;
    }
    const data = Taro.getStorageSync(storageKey);
    if (data && (data.__expired === undefined || data.__expired >= Date.now())) {
      this.cache[storageKey] = data;
      return data.__value;
    }

    return undefined;
  }

  getStorage(key: string): Promise<any> {
    const storageKey = this.getKey(key);
    return Taro.getStorage({ key: storageKey })
      .then((res) => {
        const { data } = res;
        if (data && (data.__expired === undefined || data.__expired >= Date.now())) {
          this.cache[storageKey] = data;
          return data.__value || data;
        }
        return undefined;
      })
      .catch((error) => {
        console.error('Failed to get storage:', error);
        return undefined;
      });
  }

  removeStorageSync(key: string): void {
    const storageKey = this.getKey(key);
    delete this.cache[storageKey];
    Taro.removeStorageSync(storageKey);
  }
}

export default Storage;
