import Taro from '@tarojs/taro';
import loadingInterceptor from './middlewares/loading';
import Storage from '../storage';

enum EntityBodyMethod {
  Put = 'PUT',
  Post = 'POST',
  Patch = 'PATCH',
  Delete = 'DELETE',
}

export interface IResponse<T = any> {
  code?: string;
  message?: string;
  result?: T;
  [key: string]: any;
}

interface IOptions {
  method?: string;
  server?: string;
  path?: string;
  url?: string;
  login?: boolean; // 触发 token 的读取与更新
  loading?: boolean;
  showErrorMessage?: boolean;
  header?: Record<string, any>;
  data?: Record<string, any>;
  [key: string]: any;
}

interface IFetchAPI {
  apiConfig: ApiConfig;
  authOptions: AuthOptions;
  storage: Storage;
}

interface AuthOptions {
  url: string;
  data: Record<string, any>;
}
interface ApiConfig {
  [key: string]: any;
}

class FetchAPI {
  private apiConfig: ApiConfig;

  private storage: Storage;

  private authOptions: AuthOptions;

  private requestTask;

  private tokenFetchPromise: Promise<string> | null = null;

  constructor(data: IFetchAPI) {
    const { apiConfig, storage, authOptions } = data;
    this.apiConfig = apiConfig;
    this.storage = storage;
    this.authOptions = authOptions;
    return this;
  }

  static addInterceptor(middlewares) {
    for (let i = 0; i < middlewares?.length; i++) {
      Taro.addInterceptor(middlewares[i]);
    }
    Taro.addInterceptor(loadingInterceptor);
  }

  setToken(value: string) {
    return this.storage.setStorageSync('token', value, 30 * 60 * 1000);
  }

  removeToken() {
    this.storage.removeStorageSync('token');
  }

  getToken() {
    return this.storage.getStorageSync('token');
  }

  async fetchTokenOnce(force = false) {
    if (this.tokenFetchPromise && !force) {
      return this.tokenFetchPromise;
    }

    // 创建一个新的 Promise 来获取 token
    this.tokenFetchPromise = new Promise((resolve, reject) => {
      const tokenInStorage = this.getToken();
      if (tokenInStorage && !force) {
        resolve(tokenInStorage);
        return;
      }

      Taro.login({
        success: (res) => {
          const { code } = res;
          const data: any = { code };
          const options = {
            url: this.authOptions.url,
            data: {
              ...data,
              ...this.authOptions.data,
            },
          };
          this.fetch(options)
            .then((resp) => {
              const { result } = resp || {};
              this.setToken(result);
              resolve(result);
            })
            .catch((error) => {
              reject(error);
            });
        },
        fail: (error) => {
          reject(error);
        },
        force: true,
      });
    });

    return this.tokenFetchPromise;
  }

  async fetchToken(force = false) {
    const result = await this.fetchTokenOnce(force);
    this.tokenFetchPromise = null;
    return result;
  }

  getUrl(options: IOptions): string {
    const { url, path, server = '' } = options;
    const info = this.apiConfig[server] || {};
    const { host } = info;

    return url || `${host}${path}`;
  }

  static getMethod(options: IOptions): string {
    const { method } = options;

    return method ? method.toUpperCase() : 'GET';
  }

  async fetchHeader(options: IOptions): Promise<Partial<Record<string, string>>> {
    const { login = false, header = {} } = options;

    const method = FetchAPI.getMethod(options);
    const res = {
      Accept: 'application/json',
      ...header,
    };

    if (login) {
      res['Access-Token'] = await this.fetchToken();
    }

    return EntityBodyMethod[method] ? { 'Content-Type': 'application/json', ...res } : res;
  }

  async fetchOptions(options: IOptions): Promise<any> {
    const { method: optionsMethod, server: optionsServer, path: optionsPath, url: optionsURL, login: optionsLogin, ...others } = options;

    const url = this.getUrl(options);
    const method = FetchAPI.getMethod(options);
    const header = await this.fetchHeader(options);

    return {
      dataType: 'json',
      ...others,
      url,
      method,
      header,
    };
  }

  private async fetchOnce(options: IOptions) {
    const requestOptions = await this.fetchOptions(options);
    // : Promise<Taro.uploadFile.SuccessCallbackResult> | Taro.RequestTask<IResponse<any>>
    if (!this.requestTask) {
      if (options.method === 'upload') {
        this.requestTask = Taro.uploadFile(requestOptions).then((response) => {
          try {
            response.data = JSON.parse(response.data);
          } catch {
            this.requestTask = null;
            // todo
          }

          return response;
        });
      } else {
        this.requestTask = Taro.request<IResponse>(requestOptions);
      }
    }
    try {
      const result = await this.requestTask;
      this.requestTask = null;
      const { data = {} } = result;

      return data as Promise<IResponse>;
    } catch (e) {
      this.requestTask = null;
    }
  }

  async fetch(options: IOptions) {
    const requestOptions = await this.fetchOptions(options);
    // : Promise<Taro.uploadFile.SuccessCallbackResult> | Taro.RequestTask<IResponse<any>>
    let task;
    if (options.method === 'upload') {
      task = Taro.uploadFile(requestOptions).then((response) => {
        try {
          response.data = JSON.parse(response.data);
        } catch {
          // todo
        }

        return response;
      });
    } else {
      task = Taro.request<IResponse>(requestOptions);
    }

    const result = await task;
    const { data = {} } = result;

    return data as Promise<IResponse>;
  }

  get<T = any>(options: IOptions): Promise<IResponse<T>> {
    return this.fetch({ ...options, method: 'get' });
  }

  post<T = any>(options: IOptions): Promise<IResponse<T>> {
    return this.fetch({ ...options, method: 'post' });
  }

  form<T = any>(options: IOptions): Promise<IResponse<T>> {
    return this.fetch({ ...options, method: 'form' });
  }

  upload<T = any>(options: IOptions): Promise<IResponse<T>> {
    return this.fetch({ ...options, method: 'upload' });
  }

  async request<T = any>(options: IOptions): Promise<IResponse<T>> {
    const requestOptions = await this.fetchOptions(options);
    const task = Taro.request<IResponse>(requestOptions);
    return task;
  }
}

export default FetchAPI;
