import Taro from '@tarojs/taro';

let count = 0;

const loadingInterceptor: Taro.interceptor = (chain) => {
  const { requestParams } = chain;
  const { loading } = requestParams;

  if (loading) {
    !count && Taro.showLoading({
      title: '加载中...',
      mask: true,
    });

    count += 1;
  }

  const p = chain.proceed(requestParams);
  const res = p.then(
    (response) => {
      if (loading) {
        count -= 1;

        if (count <= 0) {
          Taro.hideLoading();
        }
      }

      return response;
    },
    // 支付宝网络请求状态异常会触发 onRejected，同时重置 promise 状态
    (response) => {
      if (loading) {
        count -= 1;

        if (count <= 0) {
          Taro.hideLoading();
        }
      }

      return response;
    },
  );

  if (typeof p.abort === 'function') res.abort = p.abort;

  return res;
};

export default loadingInterceptor;
