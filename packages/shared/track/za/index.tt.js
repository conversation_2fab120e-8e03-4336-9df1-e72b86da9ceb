var _={},zaMini={storageName:"_za_sdk_tt",platform_obj:tt,lib:{name:"bytedance",version:"0.0.1"},para:{name:"za<PERSON><PERSON>",server_url:"",max_string_length:300,datasend_timeout:6e3,autoTrack:{appLaunch:!0,appShow:!0,appHide:!0,pageShow:!0},app_name:"",app_id:"",source_id:"",debug:!1},_proxy:{type:{$MPLaunch:"launch",$MPShow:"show",$MPHide:"hide",$MPViewScreen:"page",$MPCustom:"custom",$MPLogin:"SignUp",$MPABTest:"ABTestTrigger"}}},logger="object"===typeof logger?logger:{};logger.info=function(){if(zaMini.para.debug&&"object"===typeof console&&console.log)try{return console.log.apply(console,arguments)}catch(e){console.log(arguments[0])}},zaMini.setPara=function(e){zaMini.para=_.extend2Lev(zaMini.para,e),zaMini.para.server_url||logger.info("\u8bf7\u4f7f\u7528 setPara() \u65b9\u6cd5\u8bbe\u7f6e server_url \u6570\u636e\u63a5\u6536\u5730\u5740")},zaMini.status={};var ArrayProto=Array.prototype,FuncProto=Function.prototype,ObjProto=Object.prototype,slice=ArrayProto.slice,toString=ObjProto.toString,hasOwnProperty=ObjProto.hasOwnProperty,source_channel_standard="utm_campaign utm_source utm_medium utm_term utm_content bizOrigin messageNo taskCode clickid ABTVersion",mp_scene={},page_route_map=[],is_first_launch=!1;function omitEmptyValue(e){var t={};for(var i in e)hasOwnProperty.call(e,i)&&e[i]&&(t[i]=e[i]);return t}function getUTMMixObj(e){if(!e)return{};var t={};try{for(var i=source_channel_standard.split(" "),n=0;n<i.length;n++){var r=i[n];r&&e[r]&&(t[r]=e[r])}}catch(e){}return t}function getExtendsInfo(e){try{var t={},i=_.extend({},zaMini.para.extendsInfo||{}),n={};if("function"===typeof i.getDynamic)try{n=i.getDynamic(e)}catch(e){}for(var r in"object"===typeof n&&(i={...i,...n}),i)if(hasOwnProperty.call(i,r))if("function"!==typeof i[r])t[r]=i[r];else if("function"===typeof i[r]&&"getDynamic"!==r)try{t[r]=i[r]()}catch(e){logger.info("\u52a8\u6001\u914d\u7f6e\u9879\u6267\u884c\u9519\u8bef")}return t}catch(e){return logger.info("call getExtendsInfo error",e),{}}}function mp_proxy(e,t,i){var n=zaMini.autoTrackCustom[i];if(e[t]){var r=e[t];e[t]=function(){"onLaunch"===t&&(this[zaMini.para.name]=zaMini),!zaMini.para.autoTrackIsFirst||_.isObject(zaMini.para.autoTrackIsFirst)&&!zaMini.para.autoTrackIsFirst[i]?(r.apply(this,arguments),n.apply(this,arguments)):(!0===zaMini.para.autoTrackIsFirst||_.isObject(zaMini.para.autoTrackIsFirst)&&zaMini.para.autoTrackIsFirst[i])&&(n.apply(this,arguments),r.apply(this,arguments))}}else e[t]=function(){"onLaunch"===t&&(this[zaMini.para.name]=zaMini),n.apply(this,arguments)}}(function(){FuncProto.bind;var e=ArrayProto.forEach,t=ArrayProto.indexOf,i=Array.isArray,n={},r=_.each=function(t,i,r){if(null==t)return!1;if(e&&t.forEach===e)t.forEach(i,r);else if(t.length===+t.length){for(var a=0,o=t.length;a<o;a++)if(a in t&&i.call(r,t[a],a,t)===n)return!1}else for(var s in t)if(hasOwnProperty.call(t,s)&&i.call(r,t[s],s,t)===n)return!1};_.logger=logger,_.extend=function(e){return r(slice.call(arguments,1),(function(t){for(var i in t)void 0!==t[i]&&(e[i]=t[i])})),e},_.extend2Lev=function(e){return r(slice.call(arguments,1),(function(t){for(var i in t)void 0!==t[i]&&(_.isObject(t[i])&&_.isObject(e[i])?_.extend(e[i],t[i]):e[i]=t[i])})),e},_.coverExtend=function(e){return r(slice.call(arguments,1),(function(t){for(var i in t)void 0!==t[i]&&void 0===e[i]&&(e[i]=t[i])})),e},_.isArray=i||function(e){return"[object Array]"===toString.call(e)},_.isFunction=function(e){try{return/^\s*\bfunction\b/.test(e)}catch(e){return!1}},_.isArguments=function(e){return!(!e||!hasOwnProperty.call(e,"callee"))},_.toArray=function(e){return e?e.toArray?e.toArray():_.isArray(e)||_.isArguments(e)?slice.call(e):_.values(e):[]},_.values=function(e){var t=[];return null==e||r(e,(function(e){t[t.length]=e})),t},_.include=function(e,i){var a=!1;return null==e?a:t&&e.indexOf===t?-1!=e.indexOf(i):(r(e,(function(e){if(a||(a=e===i))return n})),a)},_.trim=function(e){return e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")},_.isObject=function(e){return void 0!==e&&null!==e&&"[object Object]"==toString.call(e)},_.isEmptyObject=function(e){if(_.isObject(e)){for(var t in e)if(hasOwnProperty.call(e,t))return!1;return!0}return!1},_.isUndefined=function(e){return void 0===e},_.setDefaultValue=function(e){var t=e;for(var i in t)hasOwnProperty.call(t,i)&&_.isUndefined(t[i])&&"accountId"!==i&&(t[i]="");return t},_.handleUnfinedValue=function(e){return"undefined"==typeof e||""===e?"":e},_.isString=function(e){return"[object String]"==toString.call(e)},_.isDate=function(e){return"[object Date]"==toString.call(e)},_.isBoolean=function(e){return"[object Boolean]"==toString.call(e)},_.isNumber=function(e){return"[object Number]"==toString.call(e)&&/[\d\.]+/.test(String(e))},_.isJSONString=function(e){try{JSON.parse(e)}catch(e){return!1}return!0},_.decodeURIComponent=function(e){var t="";try{t=decodeURIComponent(e)}catch(i){t=e}return t},_.encodeDates=function(e){return _.each(e,(function(t,i){_.isDate(t)?e[i]=_.formatDate(t):_.isObject(t)&&(e[i]=_.encodeDates(t))})),e},_.formatDate=function(e,t){function i(e){return e<10?"0"+e:e}return e.getFullYear()+"-"+i(e.getMonth()+1)+"-"+i(e.getDate())+" "+i(e.getHours())+":"+i(e.getMinutes())+":"+i(e.getSeconds())+(!0===t?"":"."+i(e.getMilliseconds()))},_.searchObjDate=function(e){_.isObject(e)&&_.each(e,(function(t,i){_.isObject(t)?_.searchObjDate(e[i]):_.isDate(t)&&(e[i]=_.formatDate(t))}))},_.formatString=function(e){return e.length>zaMini.para.max_string_length?(logger.info("\u5b57\u7b26\u4e32\u957f\u5ea6\u8d85\u8fc7\u9650\u5236\uff0c\u5df2\u7ecf\u505a\u622a\u53d6--"+e),e.slice(0,zaMini.para.max_string_length)):e},_.searchObjString=function(e){_.isObject(e)&&_.each(e,(function(t,i){_.isObject(t)?_.searchObjString(e[i]):_.isString(t)&&(e[i]=_.formatString(t))}))},_.unique=function(e){for(var t,i=[],n={},r=0;r<e.length;r++)t=e[r],t in n||(n[t]=!0,i.push(t));return i},_.strip_empty_properties=function(e){var t={};return _.each(e,(function(e,i){null!=e&&(t[i]=e)})),t}})(),_.getCurrentPage=function(){var e={};try{var t=getCurrentPages();e=t[t.length-1]}catch(e){logger.info(e)}return e},_.getCurrentPath=function(){var e="\u672a\u53d6\u5230";try{var t=getCurrentPages(),i=t[t.length-1];e=i.route}catch(e){logger.info(e)}return e},_.getPath=function(e){return e="string"===typeof e?e.replace(/^\//,""):"\u53d6\u503c\u5f02\u5e38",e},zaMini.initialState={queue:[],isComplete:!1,systemIsComplete:!1,storeIsComplete:!1,checkIsComplete:function(){this.systemIsComplete&&this.storeIsComplete&&(this.isComplete=!0,this.queue.length>0&&(_.each(this.queue,(function(e){zaMini[e[0]].apply(zaMini,slice.call(e[1]))})),this.queue=[]))}},_.setRefPage=function(){var e={route:"\u76f4\u63a5\u6253\u5f00",title:""};try{var t=_.getCurrentPage();if(t&&t.route){var i=t.route,n=_.getPageTitle(i);e.route=i,e.title=n;var r=page_route_map.length,a="";r>=1&&(a=page_route_map[r-1].route),a!==i&&(r>=2?(page_route_map.shift(),page_route_map.push(e)):page_route_map.push(e))}}catch(e){logger.info(e)}},_.getRefPage=function(){var e={route:"\u76f4\u63a5\u6253\u5f00",title:""};return page_route_map.length>1&&(e.title=page_route_map[0].title,e.route=page_route_map[0].route),e},_.setPageRefData=function(e){var t=_.getRefPage();_.isObject(e)&&(e.$referrer=t.route,e.$referrer_title=t.title)},_.getPageTitle=function(e){return""},_.getCustomUtmFromQuery=function(e,t){if(!_.isObject(e))return{};var i={};for(var n in e)if(-1===(" "+source_channel_standard+" ").indexOf(" "+n+" "));else{var r={[n]:e[n]};zaMini.registerApp(r),i[t+n]=e[n]}return i},_.getObjFromQuery=function(e){var t=e.split("?"),i={};return t&&t[1]?(_.each(t[1].split("&"),(function(e){var t=e.split("=");t[0]&&t[1]&&(i[t[0]]=t[1])})),i):{}},_.setStorageSync=function(e,t){var i=function(){zaMini.platform_obj.setStorageSync&&zaMini.platform_obj.setStorageSync(e,t)};try{i()}catch(e){logger.info("set Storage fail --",e);try{i()}catch(e){logger.info("set Storage fail again --",e)}}},_.getStorageSync=function(e){var t="";try{t=zaMini.platform_obj.getStorageSync&&zaMini.platform_obj.getStorageSync(e)}catch(i){try{t=zaMini.platform_obj.getStorageSync&&zaMini.platform_obj.getStorageSync(e)}catch(i){logger.info("getStorage fail")}}return t},_.getMPScene=function(e){return"number"===typeof e||"string"===typeof e&&""!==e?(e=String(e),mp_scene[e]||e):void 0},_.detectOptionQuery=function(e){if(!e||!_.isObject(e.query))return{};var t={};return t.query=_.extend({},e.query),t},_.getMixedQuery=function(e){var t=_.detectOptionQuery(e),i=t.query;for(var n in i)i[n]=_.decodeURIComponent(i[n]);return i},_.setUtm=function(e,t){var i=_.getMixedQuery(e),n=_.getCustomUtmFromQuery(i,"$","_");_.extend(t,n)},_.request=function(e){var t=zaMini.platform_obj.request&&zaMini.platform_obj.request(e);setTimeout((function(){_.isObject(t)&&_.isFunction(t.abort)&&t.abort()}),zaMini.para.datasend_timeout)},_.info={currentProps:{},properties:{},getSystem:function(){var e=this.properties;function t(){zaMini.platform_obj.getNetworkType&&zaMini.platform_obj.getNetworkType({success:function(t){e.$network_type=t["networkType"]},complete:n})}function i(e){var t=e.toLowerCase();return"ios"===t?"iOS":"android"===t?"Android":e}function n(){zaMini.platform_obj.getSystemInfo&&zaMini.platform_obj.getSystemInfo({success:function(t){e.$manufacturer=t["brand"],e.$model=t["model"],e.$screen_width=Number(t["screenWidth"]),e.$screen_height=Number(t["screenHeight"]),e.$os=i(t["platform"]),e.$os_version=t["system"].indexOf(" ")>-1?t["system"].split(" ")[1]:t["system"],e.$wx_version=t["version"],e.$language=t["language"]},complete:function(){zaMini.initialState.systemIsComplete=!0,zaMini.initialState.checkIsComplete()}})}t()}},zaMini._=_,zaMini.prepareData=function(e,t){var i={properties:{}};_.extend(i,e),_.isObject(e.properties)&&!_.isEmptyObject(e.properties)&&_.extend(i.properties,e.properties),e.type&&"profile"===e.type.slice(0,7)?(i.properties=_.extend({},_.info.properties,zaMini.store.getProps(),_.info.currentProps),i.profile_properties=e.properties):i.properties=_.extend({},_.info.properties,zaMini.store.getProps(),_.info.currentProps,i.properties),_.searchObjDate(i),_.searchObjString(i),zaMini.sendStrategy.send(i,t)},zaMini.store={storageInfo:null,getUUID:function(){return Date.now()+"-"+Math.floor(1e7*Math.random())+"-"+Math.random().toString(16).replace(".","")+"-"+String(31242*Math.random()).replace(".","").slice(0,8)},getStorage:function(){return this.storageInfo||(this.storageInfo=zaMini._.getStorageSync(zaMini.storageName)||""),this.storageInfo},_state:{},mem:{mdata:[],getLength:function(){return this.mdata.length},add:function(e){this.mdata.push(e)},clear:function(e){this.mdata.splice(0,e)}},toState:function(e){var t=null;_.isJSONString(e)?(t=JSON.parse(e),t.uid?(this._state=t,this.set("isnew",!1)):(this.set("uid",this.getUUID()),this.set("isnew",!0))):_.isObject(e)?(t=e,t.uid?(this._state=t,this.set("isnew",!1)):(this.set("uid",this.getUUID()),this.set("isnew",!0))):(this.set("isnew",!0),this.set("uid",this.getUUID()))},getDeviceId:function(){return this._state.uid},getProps:function(){return this._state.props||{}},setProps:function(e,t){var i=this._state.props||{};t?this.set("props",e):(_.extend(i,e),this.set("props",i))},set:function(e,t){var i={};for(var n in"string"===typeof e?i[e]=t:"object"===typeof e&&(i=e),this._state=this._state||{},i)this._state[n]=i[n];this.save()},change:function(e,t){this._state[e]=t},save:function(){zaMini._.setStorageSync(zaMini.storageName,this._state)},init:function(){var e=this.getStorage();if(e)this.toState(e);else{is_first_launch=!0;var t=new Date,i=t.getTime();t.setHours(23),t.setMinutes(59),t.setSeconds(60),this.set({uid:this.getUUID(),first_visit_time:i,first_visit_day_time:t.getTime(),isnew:!0})}}},zaMini.track=function(e,t,i){this.prepareData({type:"track",event:e,properties:t},i)},zaMini.customTrack=function(e,t){e=e||{};var i=_.extend({},e);zaMini.para&&zaMini.para.isPlugin&&zaMini.dynamicInfo&&zaMini.dynamicInfo.$url_path&&"\u672a\u53d6\u5230"===_.getCurrentPath()?e.$url_path=zaMini.dynamicInfo.$url_path:e.$url_path=_.getCurrentPath(),this.prepareData({type:"track",event:"$MPCustom",properties:e,originalProperties:i},t)},zaMini.triggerABTest=function(e,t,i,n,r){n=n||{};var a=_.extend({},n);zaMini.para&&zaMini.para.isPlugin&&zaMini.dynamicInfo&&zaMini.dynamicInfo.$url_path&&"\u672a\u53d6\u5230"===_.getCurrentPath()?n.$url_path=zaMini.dynamicInfo.$url_path:n.$url_path=_.getCurrentPath(),a.experiment_code=e,a.ABTVersion=t,a.control_content=i,this.prepareData({type:"track",event:"$MPABTest",properties:n,originalProperties:a},r)},zaMini.fetchABTestWithCode=function(e,t){try{if(e){const i=zaMini.abTestData||[];if(_.isArray(i)){const n=i.find((t=>t.ec===e));return t&&n&&zaMini.triggerABTest(n.ec,n.esc,"",{}),n}return null}return null}catch(t){return null}},zaMini.fetchABTestWithParamName=function(e,t){try{if(e){const i=zaMini.abTestData||[];if(_.isArray(i)){const n=i.find((t=>!!_.isArray(t.paramList)&&t.paramList.find((t=>t.name===e))));return t&&n&&zaMini.triggerABTest(n.ec,n.esc,"",{}),n}return null}return null}catch(t){return null}},zaMini.pageTrack=function(e,t){e=e||{},zaMini.para&&zaMini.para.isPlugin&&e.$url_path?(zaMini.dynamicInfo=zaMini.dynamicInfo||{},zaMini.dynamicInfo.$url_path=e.$url_path):e.$url_path=_.getCurrentPath(),this.prepareData({type:"track",event:"MPViewScreen",properties:e},t)},zaMini.setLogin=function(e,t){"string"!==typeof e&&"number"!==typeof e||!e||(zaMini.registerApp&&zaMini.registerApp({accountId:e}),this.prepareData({type:"track",event:"$MPLogin",properties:{accountId:e}},t))},zaMini.getSDKData=function(){function e(e,t){return e?e+"="+t+";":""}var t=";",i=zaMini.store.getStorage&&zaMini.store.getStorage().openid;return t=t+e("_xflow_traceid",_.handleUnfinedValue(_.info&&_.info.currentProps&&_.info.currentProps.trace_id))+e("_xflow_session_id",_.handleUnfinedValue(_.info&&_.info.currentProps&&_.info.currentProps.session_id))+e("_xflow_session_time",_.handleUnfinedValue(_.info&&_.info.currentProps&&_.info.currentProps.session_time))+e("_xflow_uid",_.handleUnfinedValue(zaMini.store.getStorage&&zaMini.store.getStorage().uid))+(i?e("_xflow_openid",_.handleUnfinedValue(i)):""),t},zaMini.registerApp=function(e){_.isObject(e)&&!_.isEmptyObject(e)&&(_.info.currentProps=_.extend(_.info.currentProps,e))},zaMini.register=function(e){_.isObject(e)&&!_.isEmptyObject(e)&&zaMini.store.setProps(e)},zaMini.clearAllRegister=function(){zaMini.store.setProps({},!0)},zaMini.clearAllProps=function(e){var t=zaMini.store.getProps(),i={};_.isArray(e)&&(_.each(t,(function(t,n){_.include(e,n)||(i[n]=t)})),zaMini.store.setProps(i,!0))},zaMini.clearAppRegister=function(e){_.isArray(e)&&_.each(_.info.currentProps,(function(t,i){_.include(e,i)&&delete _.info.currentProps[i]}))},zaMini.initial=function(){this._.info.getSystem(),this.store.init()},zaMini.init=function(e){if(!0===this.hasInit)return!1;if(zaMini.registerApp&&zaMini.registerApp({session_id:zaMini.store&&zaMini.store.getUUID&&zaMini.store.getUUID(),session_time:_.formatDate(new Date,!0),trace_id:zaMini.store&&zaMini.store.getUUID&&zaMini.store.getUUID()}),this.hasInit=!0,zaMini.setPara(e),zaMini.para.ABTestUrl){const e=zaMini.store.getStorage().openid||zaMini.store.getStorage().uid,t={};zaMini.para.tenantCode&&(t["webgw-tenant-id"]=zaMini.para.tenantCode),_.request({url:zaMini.para.ABTestUrl+"?sf="+e,method:"GET",header:t,success:function(e){e&&e.data&&200===e.data.status&&(zaMini.abTestData=e.data.data||[])}})}zaMini.initialState.storeIsComplete=!0,zaMini.initialState.checkIsComplete()},zaMini.dataToWebview=function(e){if(!this.hasInit)return logger.info("SDK needs to be initialized first, please use this method after zaMini.init();"),"";try{var t=_.info&&_.info.currentProps||{},i=zaMini.store.getStorage&&zaMini.store.getStorage()||{},n=_.extend({uid:i.uid,trace_id:t.trace_id,session_id:t.session_id,session_time:t.session_time,open_id:i.openid,union_id:i.unionId||t.$unionId},!0===e?omitEmptyValue(_.extend({sdk_type:"xcx",xcx_sdk_source:zaMini.lib&&zaMini.lib.name,web_handle_xcx:1,accountId:t.accountId},getUTMMixObj(t))):{}),r="xflow_d_t_wv="+encodeURIComponent(JSON.stringify(n));return r}catch(e){}return""},zaMini.getPresetProperties=function(){if(_.info&&_.info.properties&&_.info.properties.$lib){var e={};_.each(_.info.currentProps,(function(t,i){0===i.indexOf("$")&&(e[i]=t)}));var t=_.extend(e,{$url_path:_.getCurrentPath()},_.info.properties,zaMini.store.getProps());return delete t.$lib,t}return{}},zaMini.sendStrategy={dataHasSend:!0,dataHasChange:!1,send:function(e,t){if(!zaMini.para.server_url)return!1;this.sendData(e,t)},sendData:function(e,t){const i=!e.type||"profile"!==e.type.slice(0,7);if(e=i?this.conversionData(e):this.conversionProfileData(e),!e||void 0!==e.event_name||!zaMini.para.isPlugin){if(i)try{e=_.extend({type:"event",event_name:e.event_name||e.type,source_id:e.source_id,sdk_type:"xcx",xcx_sdk_source:zaMini.lib.name,trace_id:e.trace_id,debug:zaMini.para.debug?"true":void 0},_.setDefaultValue(e.common),e.infos,e.extendsInfo)}catch(e){}zaMini.para.debug&&(logger.info("open_id",e.open_id),logger.info(e)),e=JSON.stringify(e),!0!==zaMini.para.noLog&&(_.request({url:zaMini.para.server_url,method:"POST",data:encodeURIComponent(e),success:function(e){},complete:function(){"function"===typeof t&&t()}}),zaMini.store.set("isnew",!1),zaMini.store.storageInfo.isnew=!1)}},is_first_batch_write:!0,conversionData:function(e){var t=e.properties,i=this.conversionType(e.event),n="custom"===i?_.handleUnfinedValue(t&&t.event_value)||"custom":i,r=getExtendsInfo(n),a=getUTMMixObj(t),o={type:"event",event_name:n,source_id:zaMini.para.source_id,trace_id:t.trace_id,common:_.extend({resolution:t.$screen_width+"x"+t.$screen_height,device_model:t.$model,network:t.$network_type,event_time:_.formatDate(new Date,!0),event_timestamp:Date.now(),sdk_version:zaMini.lib.version,language:t.$language,wx_xcx_version:t.$wx_version,wx_xcx_id:zaMini.para.app_id,wx_xcx_name:zaMini.para.app_name,url:t.$url_path,url_param:t.$url_query||"",title:t.$title||"",refer_url:t.$referrer||"",referrer_title:t.$referrer_title||"",open_id:zaMini.store.getStorage().openid,uid:zaMini.store.getStorage().uid,session_id:t.session_id,session_time:t.session_time,channel:t.$latest_scene,channel_id:t.$latest_scene_id,device_id:zaMini.store.getStorage().openid||zaMini.store.getStorage().uid,device_brand:t.$manufacturer,os_type:t.$os,platform_os_version:t.$os_version,accountId:t.accountId||void 0,union_id:zaMini.store.getStorage().unionId||t.$unionId},a),infos:this.setInfoByType(t,i,e),extendsInfo:r||{}};return o},conversionProfileData:function(e){var t=e.properties,i={type:"profile",source_id:zaMini.para.source_id,event_name:"modify_profile",profile_name:e.type,event_time:_.formatDate(new Date,!0),event_timestamp:Date.now(),sdk_type:"xcx",xcx_sdk_source:zaMini.lib.name,sdk_version:zaMini.lib.version,accountId:t.accountId||void 0,device_id:zaMini.store.getStorage().openid||zaMini.store.getStorage().uid,open_id:zaMini.store.getStorage().openid,union_id:zaMini.store.getStorage().unionId||t.$unionId,uid:zaMini.store.getStorage().uid,properties:e.profile_properties,debug:zaMini.para.debug?"true":void 0};return i},conversionType:function(e){return void 0!=zaMini._proxy.type[e]?zaMini._proxy.type[e]:zaMini.para.isPlugin?void 0:"page"},setInfoByType:function(e,t,i){var n={};try{if("custom"===t){var r=i&&i.originalProperties||{};return _.extend({custom_event_type:_.handleUnfinedValue(e.event_type),custom_event_value:_.handleUnfinedValue(e.event_value),custom_event_description:_.handleUnfinedValue(e.event_description)},r||{})}if("ABTestTrigger"===t){r=i&&i.originalProperties||{};return r}return n}catch(e){return n}}},zaMini.setOpenid=function(e){zaMini.store.set("openid",e),zaMini.store&&zaMini.store.storageInfo&&(zaMini.store.storageInfo.openid=e)},zaMini.setUnionId=function(e){zaMini.store.set("unionId",e),zaMini.store&&zaMini.store.storageInfo&&(zaMini.store.storageInfo.unionId=e),zaMini.registerApp({$unionId:e||void 0})},_.each(["setProfile","setOnceProfile","track","incrementProfile","appendProfile","deleteProfile","unsetProfile"],(function(e){var t=zaMini[e];zaMini[e]=function(){zaMini.initialState.isComplete?t.apply(zaMini,arguments):zaMini.initialState.queue.push([e,arguments])}})),_.setQuery=function(e,t){var i="";if(e&&_.isObject(e)&&!_.isEmptyObject(e)){var n=[];return _.each(e,(function(e,i){"q"===i&&_.isString(e)&&0===e.indexOf("http")||"scene"===i||"__key_"===i||(t?n.push(i+"="+e):n.push(i+"="+_.decodeURIComponent(e)))})),n.join("&")}return i},_.getUtmFromPage=function(){var e={};try{var t=getCurrentPages(),i=t[t.length-1].options;e=_.getCustomUtmFromQuery(i,"$","_")}catch(e){logger.info(e)}return e},zaMini.autoTrackCustom={trackCustom:function(e,t,i){var n=zaMini.para.autoTrack[e],r="";if(zaMini.para.autoTrack&&n){if("function"===typeof n){if(r=n(),!1===r)return;_.isObject(r)&&_.extend(t,r)}else _.isObject(n)&&(_.extend(t,n),zaMini.para.autoTrack[e]=!0);_.setPageRefData(t),zaMini.track(i,t)}},appLaunch:function(e,t){"object"!==typeof this||this["trackCustom"]||(this[zaMini.para.name]=zaMini);var i={};e&&e.path&&(i.$url_path=_.getPath(e.path)),_.setUtm(e,i),e.scene=e.scene||"\u672a\u53d6\u5230\u503c",i.$scene=_.getMPScene(e.scene),i.$scene_id=e.scene,zaMini.registerApp({$latest_scene:i.$scene,$latest_scene_id:i.$scene_id,$url_query:_.setQuery(e.query)}),t?(i=_.extend(i,t),zaMini.track("$MPLaunch",i)):zaMini.para.autoTrack&&zaMini.para.autoTrack.appLaunch&&zaMini.autoTrackCustom.trackCustom("appLaunch",i,"$MPLaunch")},appShow:function(e,t){var i={};e&&e.path&&(i.$url_path=_.getPath(e.path)),_.setUtm(e,i),e.scene=e.scene||"\u672a\u53d6\u5230\u503c",i.$scene=_.getMPScene(e.scene),i.$scene_id=e.scene,zaMini.registerApp({$latest_scene:i.$scene,$latest_scene_id:i.$scene_id,$url_query:_.setQuery(e.query)}),t?(i=_.extend(i,t),zaMini.track("$MPShow",i)):zaMini.para.autoTrack&&zaMini.para.autoTrack.appShow&&zaMini.autoTrackCustom.trackCustom("appShow",i,"$MPShow")},appHide:function(e){var t={};t.$url_path=_.getCurrentPath(),e?(t=_.extend(t,e),zaMini.track("$MPHide",t)):zaMini.para.autoTrack&&zaMini.para.autoTrack.appHide&&zaMini.autoTrackCustom.trackCustom("appHide",t,"$MPHide")},pageLoad:function(e){e&&_.isObject(e)&&(this.za_mp_url_query=_.setQuery(e))},pageShow:function(){var e={},t=_.getCurrentPath(),i=_.getPageTitle(t);_.setRefPage(),e.$url_path=t;var n={$url_query:this.za_mp_url_query?this.za_mp_url_query:"",$title:i};_.setPageRefData(n),zaMini.registerApp(n),e=_.extend(e,_.getUtmFromPage()),zaMini.para.onshow?zaMini.para.onshow(zaMini,t,this):zaMini.para.autoTrack&&zaMini.para.autoTrack.pageShow&&zaMini.autoTrackCustom.trackCustom("pageShow",e,"$MPViewScreen")}};var oldApp=App;App=function(e){mp_proxy(e,"onLaunch","appLaunch"),mp_proxy(e,"onShow","appShow"),mp_proxy(e,"onHide","appHide"),oldApp.apply(this,arguments)};var oldPage=Page;Page=function(e){mp_proxy(e,"onLoad","pageLoad"),mp_proxy(e,"onShow","pageShow"),oldPage.apply(this,arguments)};var oldComponent=Component;Component=function(e){try{mp_proxy(e.methods,"onLoad","pageLoad"),mp_proxy(e.methods,"onShow","pageShow"),oldComponent.apply(this,arguments)}catch(e){oldComponent.apply(this,arguments)}},zaMini.initial(),module.exports=zaMini;
