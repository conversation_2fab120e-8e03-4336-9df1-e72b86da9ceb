var zaAliMini={},_={};zaAliMini.para={name:"zaAliMini",server_url:"https://xflowcloud.zhongan.io/nginx/cloud_xcx_sdk",max_string_length:200,datasend_timeout:25e3,autoTrack:{appLaunch:!0,appShow:!0,appHide:!0,pageShow:!0,mpClick:!0,pageShare:!0},debug:false,framework:{},ABTestUrl:"",tenantCode:""};_._proxy={type:{$MPLaunch:"launch",$MPShow:"show",$MPShare:"share",$MPViewScreen:"page",$MPHide:"hide",$MPClick:"click",$MPCustom:"custom",$MPLogin:"SignUp",$MPABTest:"ABTestTrigger"}};zaAliMini.setPara=function(e){zaAliMini.para=_.extend2Lev(zaAliMini.para,e);_.isObject(zaAliMini.para.register)&&_.extend(_.info.properties,zaAliMini.para.register);zaAliMini.para.name||(zaAliMini.para.name="zaAliMini");var i=zaAliMini.para.server_url;if(!i){console.error("请使用 setPara() 方法设置 server_url 数据接收地址")}};zaAliMini._queue=[];zaAliMini.getSystemInfoComplete=!1;var ArrayProto=Array.prototype,FuncProto=Function.prototype,ObjProto=Object.prototype,slice=ArrayProto.slice,toString=ObjProto.toString,hasOwnProperty=ObjProto.hasOwnProperty,LIB_VERSION="0.0.3",LIB_NAME="AlipayMini",source_channel_standard="utm_campaign utm_source utm_medium utm_term utm_content bizOrigin messageNo taskCode clickid ABTVersion",latest_source_channel=["$latest_utm_campaign","$latest_utm_source","$latest_utm_campaign","$latest_utm_term","$latest_bizOrigin"];zaAliMini.lib_version=LIB_VERSION;var is_first_launch=!1,mpshow_time=null,first_show_page=!1,logger="object"==typeof logger?logger:{};var alipay_scene={1e3:"首页12宫格及更多",1002:"小程序收藏应用入口，包含朋友tab中的入口",1005:"顶部搜索框的搜索结果页",1007:"单人聊天会话中的小程序消息卡片",1011:"扫描二维码",1014:"小程序模版消息（服务提醒）",1020:"生活号profile页相关小程序列表",1023:"系统桌面图标",1037:"小程序打开小程序",1038:"从另一个小程序返回",1090:"长按小程序右上角菜单唤出最近使用历史",1200:"城市服务频道",1201:"芝麻信用频道",1202:"车主服务频道",1203:"医疗服务频道",1204:"大学生活频道",1205:"中小学频道",1206:"共享单车频道",1207:"保险服务频道",1208:"天天有料频道",1209:"支付宝会员频道",1300:"第三方APP打开","0000":"其他渠道场景渠道"};logger.info=function(){if(zaAliMini.para.debug&&"object"==typeof console&&console.log){try{return console.log.apply(console,arguments)}catch(e){console.log(arguments[0])}}};(function(){FuncProto.bind;var o=ArrayProto.forEach,n=ArrayProto.indexOf,e=Array.isArray,s={},r=_.each=function(e,i,t){if(null==e)return!1;if(o&&e.forEach===o)e.forEach(i,t);else if(e.length===+e.length){for(var n=0,r=e.length;n<r;n++)if(n in e&&i.call(t,e[n],n,e)===s)return!1}else for(var a in e)if(hasOwnProperty.call(e,a)&&i.call(t,e[a],a,e)===s)return!1};_.logger=logger;_.extend=function(t){return r(slice.call(arguments,1),function(e){for(var i in e)void 0!==e[i]&&(t[i]=e[i])}),t};_.extend2Lev=function(t){return r(slice.call(arguments,1),function(e){for(var i in e)void 0!==e[i]&&null!==e[i]&&(_.isObject(e[i])&&_.isObject(t[i])?_.extend(t[i],e[i]):t[i]=e[i])}),t};_.coverExtend=function(t){return r(slice.call(arguments,1),function(e){for(var i in e)void 0!==e[i]&&void 0===t[i]&&(t[i]=e[i])}),t};_.isArray=e||function(e){return"[object Array]"===toString.call(e)};_.isFunction=function(e){try{return/^\s*\bfunction\b/.test(e)}catch(e){return!1}};_.isArguments=function(e){return!(!e||!hasOwnProperty.call(e,"callee"))};_.toArray=function(e){return e?e.toArray?e.toArray():_.isArray(e)?slice.call(e):_.isArguments(e)?slice.call(e):_.values(e):[]};_.omit=function(e,i){if(!_.isArray(i)||!e)return e;var t={};for(var n in e){if(hasOwnProperty.call(e,n)){if(i&&i.indexOf&&i.indexOf(n)===-1){t[n]=e[n]}}}return t};_.values=function(e){var i=[];return null==e?i:(r(e,function(e){i[i.length]=e}),i)};_.include=function(e,i){var t=!1;return null==e?t:n&&e.indexOf===n?-1!=e.indexOf(i):(r(e,function(e){if(t||(t=e===i))return s}),t)}})();_.trim=function(e){return e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")};_.isObject=function(e){return"[object Object]"==toString.call(e)&&null!=e};_.isEmptyObject=function(e){if(_.isObject(e)){for(var i in e)if(hasOwnProperty.call(e,i))return!1;return!0}return!1};_.isUndefined=function(e){return void 0===e};_.isString=function(e){return"[object String]"==toString.call(e)};_.isDate=function(e){return"[object Date]"==toString.call(e)};_.isBoolean=function(e){return"[object Boolean]"==toString.call(e)};_.isNumber=function(e){return"[object Number]"==toString.call(e)&&/[\d\.]+/.test(String(e))};_.isJSONString=function(e){try{JSON.parse(e)}catch(e){return!1}return!0};_.decodeURIComponent=function(i){var t="";try{t=decodeURIComponent(i)}catch(e){t=i}return t};_.encodeDates=function(t){return _.each(t,function(e,i){_.isDate(e)?t[i]=_.formatDate(e):_.isObject(e)&&(t[i]=_.encodeDates(e))}),t};_.formatDate=function(e){function i(e){return e<10?"0"+e:e}return e.getFullYear()+"-"+i(e.getMonth()+1)+"-"+i(e.getDate())+" "+i(e.getHours())+":"+i(e.getMinutes())+":"+i(e.getSeconds())};_.searchObjDate=function(t){_.isObject(t)&&_.each(t,function(e,i){_.isObject(e)?_.searchObjDate(t[i]):_.isDate(e)&&(t[i]=_.formatDate(e))})};_.formatString=function(e){return e.length>zaAliMini.para.max_string_length?(logger.info("字符串长度超过限制，已经做截取--"+e),e.slice(0,zaAliMini.para.max_string_length)):e};_.searchObjString=function(t){_.isObject(t)&&_.each(t,function(e,i){_.isObject(e)?_.searchObjString(t[i]):_.isString(e)&&(t[i]=_.formatString(e))})};_.unique=function(e){for(var i,t=[],n={},r=0;r<e.length;r++)(i=e[r])in n||(n[i]=!0,t.push(i));return t};_.utf8Encode=function(e){var i,t,n,r,a="";for(i=t=0,n=(e=(e+"").replace(/\r\n/g,"\n").replace(/\r/g,"\n")).length,r=0;r<n;r++){var o=e.charCodeAt(r),s=null;o<128?t++:s=o>127&&o<2048?String.fromCharCode(o>>6|192,63&o|128):String.fromCharCode(o>>12|224,o>>6&63|128,63&o|128),null!==s&&(t>i&&(a+=e.substring(i,t)),a+=s,i=t=r+1)}return t>i&&(a+=e.substring(i,e.length)),a};_.base64Encode=function(e){var i,t,n,r,a,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",s=0,l=0,c="",u=[];if(!e)return e;e=_.utf8Encode(e);do{i=(a=e.charCodeAt(s++)<<16|e.charCodeAt(s++)<<8|e.charCodeAt(s++))>>18&63,t=a>>12&63,n=a>>6&63,r=63&a,u[l++]=o.charAt(i)+o.charAt(t)+o.charAt(n)+o.charAt(r)}while(s<e.length);switch(c=u.join(""),e.length%3){case 1:c=c.slice(0,-2)+"==";break;case 2:c=c.slice(0,-1)+"="}return c};_.info={currentProps:{},properties:{$lib:LIB_NAME,$lib_version:String(LIB_VERSION)},getSystem:function(){var n=this.properties,e=!0;function i(){e&&(e=!1,my.getSystemInfo({success:function(e){var i,t;n.$model=e.model,n.$screen_width=Number(e.screenWidth),n.$screen_height=Number(e.screenHeight),n.$os=(i=e.platform,"ios"===(t=i.toLowerCase())?"iOS":"android"===t?"Android":i),n.$platform_os_version=e.system.indexOf(" ")>-1?e.system.split(" ")[1]:e.system,n.$manufacturer=e.brand,n.$language=e.language,n.$alipay_version=e.version},complete:function(){var e,i=(new Date).getTimezoneOffset();my.getAppIdSync&&(e=my.getAppIdSync().appId),e&&(n.$app_id=e),_.isNumber(i)&&(n.$timezone_offset=i),zaAliMini.getSystemInfoComplete=!0,zaAliMini.checkIsComplete()}}))}my.getNetworkType({success:function(e){n.$network_type=e.networkType,i()},complete:function(){i()}})},setStatusComplete:function(){if(zaAliMini.getSystemInfoComplete)return!1;zaAliMini.getSystemInfoComplete=!0,zaAliMini._queue.length>0&&(_.each(zaAliMini._queue,function(e){zaAliMini.prepareData.apply(zaAliMini,slice.call(e))}),zaAliMini._queue=[])}};zaAliMini._=_;zaAliMini.prepareData=function(e,i){if(!zaAliMini.isComplete)return zaAliMini._queue.push(arguments),!1;var t={distinct_id:this.store.getDistinctId(),lib:{$lib:LIB_NAME,$lib_method:"code",$lib_version:String(LIB_VERSION)},properties:{}};_.extend(t,e);_.isObject(e.properties)&&!_.isEmptyObject(e.properties)&&_.extend(t.properties,e.properties);if(!e.type||"profile"!==e.type.slice(0,7)){t.properties=_.extend({},_.info.properties,zaAliMini.store.getProps(),_.info.currentProps,t.properties)}else{t.properties=_.extend({},_.info.properties,zaAliMini.store.getProps(),_.info.currentProps);t.profile_properties=e.properties}t.properties.$time&&_.isDate(t.properties.$time)?(t.time=1*t.properties.$time,delete t.properties.$time):t.time=1*new Date;_.searchObjDate(t);_.searchObjString(t);zaAliMini.send(t,i)};zaAliMini.checkIsComplete=function(){this.isComplete=!1,this.getSystemInfoComplete&&this.hasInit&&(this.isComplete=!0,zaAliMini._queue.length>0&&(_.each(zaAliMini._queue,function(e){zaAliMini.prepareData.apply(zaAliMini,slice.call(e))}),zaAliMini._queue=[]))};zaAliMini.store={getUUID:function(){return Date.now()+"-"+Math.floor(1e7*Math.random())+"-"+Math.random().toString(16).replace(".","")+"-"+String(31242*Math.random()).replace(".","").slice(0,8)},setStorage:function(){},getStorage:function(){return my.getStorageSync({key:"za_xflow_alipay"})||{}},_state:{},toState:function(e){"object"==typeof e&&e.distinct_id?this._state=e:this.set("distinct_id",this.getUUID())},getFirstId:function(){return this._state.first_id},getDistinctId:function(){return this._state.distinct_id},getProps:function(){return this._state.props||{}},setProps:function(e,i){var t=this._state.props||{};i?this.set("props",e):(_.extend(t,e),this.set("props",t))},set:function(e,i){var t={};for(var n in"string"==typeof e?t[e]=i:"object"==typeof e&&(t=e),this._state=this._state||{},t)this._state[n]=t[n];this.save()},change:function(e,i){this._state[e]=i},save:function(){my.setStorageSync({key:"za_xflow_alipay",data:this._state})},init:function(){var e=this.getStorage().data;if(e){this.toState(e)}else{is_first_launch=!0;var i=new Date,t=i.getTime();i.setHours(23);i.setMinutes(59);i.setSeconds(60);zaAliMini.setOnceProfile({$first_visit_time:new Date});this.set({distinct_id:this.getUUID(),first_visit_time:t,first_visit_day_time:i.getTime()})}}};zaAliMini.setProfile=function(e,i){if(!_.isObject(e)){return false}zaAliMini.prepareData({type:"profile_set",properties:e},i)};zaAliMini.setOnceProfile=function(e,i){if(!_.isObject(e)){return false}zaAliMini.prepareData({type:"profile_set_once",properties:e},i)};zaAliMini.appendProfile=function(t,e){if(!_.isObject(t)){return false}_.each(t,function(e,i){if(_.isString(e)){t[i]=[e]}else if(_.isArray(e)){}else{delete t[i];logger.info("appendProfile属性的值必须是字符串或者数组")}});zaAliMini.prepareData({type:"profile_append",properties:t},e)};zaAliMini.incrementProfile=function(e,i){if(!_.isObject(e)){return false}var t=e;if(_.isString(e)){e={};e[t]=1}zaAliMini.prepareData({type:"profile_increment",properties:e},i)};zaAliMini.deleteProfile=function(e,i){zaAliMini.prepareData({type:"profile_delete"},i)};zaAliMini.unsetProfile=function(e,i){var t=e;if(_.isString(e)){e=[t]}if(_.isArray(e)){const n={};_.each(e,function(e){if(_.isString(e)){n[e]=true}else{console.log("profile_unset给的数组里面的值必须时string,已经过滤掉",e)}});e=n}else{console.log("profile_unset的参数是数组");return false}zaAliMini.prepareData({type:"profile_unset",properties:e},i)};zaAliMini.track=function(e,i,t){this.prepareData({type:"track",event:e,properties:i},t)};zaAliMini.identify=function(e,i){if("number"==typeof e)e=String(e);else if("string"!=typeof e)return!1;var t=zaAliMini.store.getFirstId();!0===i?t?zaAliMini.store.set("first_id",e):zaAliMini.store.set("distinct_id",e):t?zaAliMini.store.change("first_id",e):zaAliMini.store.change("distinct_id",e)};zaAliMini.trackSignup=function(e,i,t,n){var r=zaAliMini.store.getFirstId()||zaAliMini.store.getDistinctId();zaAliMini.store.set("distinct_id",e);zaAliMini.prepareData({original_id:r,distinct_id:e,type:"track_signup",event:i,properties:t},n)};zaAliMini.registerApp=function(e){_.isObject(e)&&!_.isEmptyObject(e)&&(_.info.currentProps=_.extend(_.info.currentProps,e))};zaAliMini.clearAppRegister=function(t){_.isArray(t)&&_.each(_.info.currentProps,function(e,i){_.include(t,i)&&delete _.info.currentProps[i]})};zaAliMini.clearAllRegister=function(){zaAliMini.store.setProps({},!0)};zaAliMini.login=function(e){var i=zaAliMini.store.getFirstId(),t=zaAliMini.store.getDistinctId();e!==t&&(i?zaAliMini.trackSignup(e,"$SignUp"):(zaAliMini.store.set("first_id",t),zaAliMini.trackSignup(e,"$SignUp")))};zaAliMini.logout=function(e){var i=zaAliMini.store.getFirstId();i?(zaAliMini.store.set("first_id",""),!0===e?zaAliMini.store.set("distinct_id",zaAliMini.store.getUUID()):zaAliMini.store.set("distinct_id",i)):logger.info("没有first_id，logout失败")};zaAliMini.getAnonymousID=function(){if(!_.isEmptyObject(zaAliMini.store._state))return zaAliMini.store._state.first_id||zaAliMini.store._state.distinct_id;logger.info("请先初始化SDK")};zaAliMini.getLocation=function(){my.getSetting({success:function(e){if(!e.authSetting.location)return!1;my.getLocation({success:function(e){zaAliMini.registerApp({$latitude:e.latitude,$longitude:e.longitude})},fail:function(e){console.log("获取位置失败：",e)}})}})};zaAliMini.initial=function(){this._.info.getSystem();this.store.init();_.isObject(this.para.register)&&(_.info.properties=_.extend(_.info.properties,this.para.register))};zaAliMini.init=function(){zaAliMini.registerApp&&zaAliMini.registerApp({session_id:zaAliMini.store&&zaAliMini.store.getUUID&&zaAliMini.store.getUUID(),session_time:_.formatDate(new Date),trace_id:zaAliMini.store&&zaAliMini.store.getUUID&&zaAliMini.store.getUUID()});if(!0===this.hasInit)return!1;this.hasInit=!0;if(zaAliMini.para.ABTestUrl){var e=_.info&&_.info.currentProps&&_.info.currentProps.xflow_user_id;var i=e||zaAliMini.store.getDistinctId();var t={};if(zaAliMini.para.tenantCode){t["webgw-tenant-id"]=zaAliMini.para.tenantCode}const s={url:zaAliMini.para.ABTestUrl+"?sf="+i,method:"GET",headers:t,success:function(e){if(e&&e.data&&e.data.status===200){zaAliMini.abTestData=e.data.data||[]}}};if(my.canIUse("request")){var n=setTimeout(function(){_.isObject(r)&&_.isFunction(r.abort)&&r.abort()},zaAliMini.para.datasend_timeout);var r=my.request({...s,complete:function(){n&&clearTimeout(n)}})}else{var a=setTimeout(function(){_.isObject(o)&&_.isFunction(o.abort)&&o.abort()},zaAliMini.para.datasend_timeout);var o=my.httpRequest({...s,complete:function(){a&&clearTimeout(a)}})}}zaAliMini.checkIsComplete()};function conversionType(e){var i=_._proxy.type&&_._proxy.type[e];return i!=undefined?i:"page"}function getUTMMixObj(e){if(!e)return{};var i={};try{var t=source_channel_standard.split(" ");for(var n=0;n<t.length;n++){var r=t[n];if(r&&e[r]){i[r]=e[r]}}}catch(e){}return i}function setInfoByType(i,e){var t={};try{var n={};_.each(Object.keys(i),function(e){if(e&&e.indexOf&&e.indexOf("extend")>-1&&i[e]){n[e]=i[e]}});if(e==="share"){return _.extend({share_title:i.shareTitle,description:i.shareDesc,share_from:i.$from},n)}if(e==="click"){var r=i.datasets||{};var n={};_.each(Object.keys(r),function(e){if(e&&r[e]&&e!=="id"){n[e]=r[e]}});return _.extend({click_id:r.id},n)}if(e==="custom"){var a=i&&i.originalProperties||{};return _.extend({},_.omit(a,["event_type","event_value","event_description"]),{custom_event_type:a.event_type,custom_event_value:a.event_value,custom_event_description:a.event_description})}if(e==="ABTestTrigger"){var a=i&&i.originalProperties||{};return _.extend({},a)}return _.extend({},n)}catch(e){return t}}function getExtendsInfo(){try{var e={};var i=_.extend({},zaAliMini.para&&zaAliMini.para.extendsInfo||{});var t={};if(typeof i.getDynamic==="function"){try{t=i.getDynamic()}catch(e){}}if(typeof t==="object"){i={...i,...t}}for(var n in i){if(hasOwnProperty.call(i,n)){if(typeof i[n]!=="function"){e[n]=i[n]}else if(typeof i[n]==="function"&&n!=="getDynamic"){try{e[n]=i[n]()}catch(e){console.log("动态配置项执行错误")}}}}return e}catch(e){console.log("call getExtendsInfo error",e);return{}}}_.conversionData=function(e){e=e||{};var i=e.properties||{};var t=conversionType(e.event);var n=getUTMMixObj(i);var r=setInfoByType(i,t,e);var a=getExtendsInfo();var o=_.extend({type:"event",xcx_sdk_source:"alipay",sdk_type:"xcx",event_name:t==="custom"?_.handleUndefinedValue(i.originalProperties&&i.originalProperties.event_value)||"custom":t,source_id:zaAliMini.para.source_id,trace_id:i&&i.trace_id,resolution:i.$screen_width&&i.$screen_height&&i.$screen_width+"x"+i.$screen_height,device_model:i.$model,network:i.$network_type,event_time:_.formatDate(new Date),event_timestamp:Date.now(),sdk_version:i.$lib_version,language:i.$language,wx_xcx_version:i.$alipay_version,wx_xcx_id:zaAliMini.para.app_id||i.$app_id,wx_xcx_name:zaAliMini.para.app_name,url:i.$url_path,url_param:i.$url_query,open_id:i.xflow_user_id,uid:zaAliMini.store.getDistinctId(),session_id:i.session_id,session_time:i.session_time,channel:i.$latest_scene,channel_id:i.$latest_scene_id,device_id:i.xflow_user_id||zaAliMini.store.getDistinctId(),device_brand:i.$manufacturer,latitude:i.$latitude,longitude:i.$longitude,os_type:i.$os,platform_os_version:i.$platform_os_version,accountId:i.accountId,debug:zaAliMini.para.debug?"true":undefined},n,r,a);return o};_.conversionProfileData=function(e){var i=e.properties||{};var t={type:"profile",source_id:zaAliMini.para.source_id,event_name:"modify_profile",profile_name:e.type,event_time:_.formatDate(new Date,true),event_timestamp:Date.now(),sdk_type:"xcx",xcx_sdk_source:"alipay",sdk_version:i.$lib_version,open_id:i.xflow_user_id,uid:zaAliMini.store.getDistinctId(),device_id:i.xflow_user_id||zaAliMini.store.getDistinctId(),accountId:i.accountId,properties:e.profile_properties,debug:zaAliMini.para.debug?"true":undefined};return t};zaAliMini.send=function(r){if(!zaAliMini.para.server_url)return;const e=!r.type||r.type.slice(0,7)!=="profile";if(e){r=_.conversionData(r)}else{r=_.conversionProfileData(r)}logger.info("open_id",r.open_id);logger.info(r);r=encodeURIComponent(JSON.stringify(r));if(zaAliMini.para.noLog===true)return;(function(){if(my.canIUse("request")){var e=setTimeout(function(){_.isObject(i)&&_.isFunction(i.abort)&&i.abort()},zaAliMini.para.datasend_timeout);var i=my.request({url:zaAliMini.para.server_url,data:r,dataType:"json",method:"POST",headers:{"content-type":"application/x-www-form-urlencoded"},complete:function(){e&&clearTimeout(e)}})}else{var t=setTimeout(function(){_.isObject(n)&&_.isFunction(n.abort)&&n.abort()},zaAliMini.para.datasend_timeout);var n=my.httpRequest({url:zaAliMini.para.server_url,data:r,dataType:"text",method:"POST",headers:{"content-type":"application/x-www-form-urlencoded"},complete:function(){t&&clearTimeout(t)}})}})()};_.getPath=function(e){return e="string"==typeof e?e.replace(/^\//,""):"取值异常"};_.getQueryParam=function(e,i){var t=new RegExp("[\\?&]"+i+"=([^&#]*)").exec(e);return null===t||t&&"string"!=typeof t[1]&&t[1].length?"":_.decodeURIComponent(t[1])};_.getMPScene=function(e){if(typeof e==="number"||typeof e==="string"&&e!==""){e=String(e);return alipay_scene[e]||e}else{return"未取到值"}};_.getQuery=function(e){var i={};if(e&&_.isObject(e.query)&&(i=_.extend({},e.query),e.query.qrCode&&_.extend(i,_.getObjFromQuery(_.decodeURIComponent(e.query.qrCode)))),e&&_.isObject(e.referrerInfo)&&e.referrerInfo.extraData){var t={};_.isObject(e.referrerInfo.extraData)&&!_.isEmptyObject(e.referrerInfo.extraData)?t=e.referrerInfo.extraData:_.isJSONString(e.referrerInfo.extraData)&&(t=JSON.parse(e.referrerInfo.extraData)),_.extend(i,t)}return i};_.setUtm=function(e,i){var t=_.getQuery(e),n={},r=_.getCustomUtmFromQuery(t,"$","_","$"),a=_.getCustomUtmFromQuery(t,"$latest_","_latest_","$latest_");return n.pre1=r,n.pre2=a,_.extend(i,n.pre1),n};_.getObjFromQuery=function(e){var i=e.split("?"),t={};return i&&i[1]?(_.each(i[1].split("&"),function(e){var i=e.split("=");i[0]&&i[1]&&(t[i[0]]=i[1])}),t):{}};_.getCustomUtmFromQuery=function(e,i,t,n){if(!_.isObject(e))return{};var r={};if(e.sa_utm){for(var a in e)"sa_utm"!==a?_.include(zaAliMini.para.source_channel,a)&&(r[t+a]=e[a]):r[n+a]=e[a]}else{for(var a in e){if(-1===(" "+source_channel_standard+" ").indexOf(" "+a+" ")){_.include(zaAliMini.para.source_channel,a)&&(r[t+a]=e[a])}else{var o={};o[a]=e[a];zaAliMini.registerApp(o);r[i+a]=e[a]}}}return r};_.existLatestUtm=function(){var t=!1;return _.each(latest_source_channel,function(e,i){_.info.currentProps[e]&&(t=!0)}),t};_.handleUndefinedValue=function(e){if(typeof e=="undefined"||e===""){return""}else{return e}};_.setQuery=function(e){if(e&&_.isObject(e)&&!_.isEmptyObject(e)){var t=[];return _.each(e,function(e,i){"q"===i&&_.isString(e)&&0===e.indexOf("http")||"scene"===i||t.push(i+"="+e)}),t.join("&")}return""};_.setLatestChannel=function(e){_.isEmptyObject(e)||(function(e,i){var t=!1;for(var n in i)e[i[n]]&&(t=!0);return t}(e,latest_source_channel)&&zaAliMini.clearAppRegister(latest_source_channel),zaAliMini.registerApp(e))};_.getCurrentPath=function(){var e="未取到";try{var i=getCurrentPages();e=i[i.length-1].route}catch(e){logger.info(e)}return e};zaAliMini.appLaunch=function(e,i){i&&_.isObject(i)||(i={});var t={};e&&e.path&&(t.$url_path=_.getPath(e.path));e&&e.query&&(t.$url_query=_.setQuery(e.query));var n=_.setUtm(e,t);is_first_launch?(t.$is_first_time=!0,_.isEmptyObject(n.pre1)||zaAliMini.setOnceProfile(n.pre1)):t.$is_first_time=!1,_.isEmptyObject(n.pre2)||_.setLatestChannel(n.pre2);var r=_.getMPScene(e.scene);r&&(t.$scene=r,zaAliMini.registerApp({$latest_scene:t.$scene,$latest_scene_id:e.scene})),_.extend(t,i),zaAliMini.para.autoTrack&&zaAliMini.para.autoTrack.appLaunch&&zaAliMini.track("$MPLaunch",t)};zaAliMini.appShow=function(e,i){i&&_.isObject(i)||(i={});var t={};mpshow_time=(new Date).getTime(),first_show_page=!0,e&&e.path&&(t.$url_path=_.getPath(e.path));var n=_.setUtm(e,t);_.isEmptyObject(n.pre2)||_.setLatestChannel(n.pre2);if(zaAliMini.para.getLocation===true){zaAliMini.getLocation()}var r=_.getMPScene(e.scene);r&&(t.$scene=r,zaAliMini.registerApp({$latest_scene:t.$scene,$latest_scene_id:e.scene})),t.$url_query=_.setQuery(e.query),_.extend(t,i),zaAliMini.para.autoTrack&&zaAliMini.para.autoTrack.appShow&&zaAliMini.track("$MPShow",t)};zaAliMini.appHide=function(e){e&&_.isObject(e)||(e={});var i=(new Date).getTime(),t={};t.$url_path=_.getCurrentPath(),mpshow_time&&i-mpshow_time>0&&(i-mpshow_time)/36e5<24&&(t.event_duration=(i-mpshow_time)/1e3),_.extend(t,e),zaAliMini.para.autoTrack&&zaAliMini.para.autoTrack.appHide&&zaAliMini.track("$MPHide",t)};zaAliMini.pageShow=function(e){var i={},t=_.getCurrentPath();i.$url_path=t;var n=getCurrentPages(),r=n[n.length-1];try{_.isObject(r)?(i.$url_query=r.za_ali_query?r.za_ali_query:"",first_show_page&&(first_show_page=!1,!_.existLatestUtm()&&r.utm&&_.isObject(r.utm.pre2)&&zaAliMini.registerApp(r.utm.pre2)),r.utm&&_.isObject(r.utm.pre1)&&_.extend(i,r.utm.pre1)):i.$url_query=""}catch(e){logger.info(e)}_.extend(i,e),zaAliMini.para.autoTrack&&zaAliMini.para.autoTrack.pageShow&&zaAliMini.track("$MPViewScreen",i)};zaAliMini.customTrack=function(e){zaAliMini.track("$MPCustom",{$url_path:_.getCurrentPath(),originalProperties:e})};zaAliMini.triggerABTest=function(e,i,t,n){n=n||{};var r=_.extend({},n);r.experiment_code=e;r.ABTVersion=i;r.control_content=t;zaAliMini.track("$MPABTest",{$url_path:_.getCurrentPath(),originalProperties:r})};zaAliMini.fetchABTestWithCode=function(i,e){try{if(i){const t=zaAliMini.abTestData||[];if(_.isArray(t)){const n=t.find(e=>{return e.ec===i});if(e&&n){zaAliMini.triggerABTest(n.ec,n.esc,"",{})}return n}return null}return null}catch(e){return null}};zaAliMini.fetchABTestWithParamName=function(i,e){try{if(i){const t=zaAliMini.abTestData||[];if(_.isArray(t)){const n=t.find(e=>{if(_.isArray(e.paramList)){return e.paramList.find(e=>e.name===i)}return false});if(e&&n){zaAliMini.triggerABTest(n.ec,n.esc,"",{})}return n}return null}return null}catch(e){return null}};zaAliMini.pageShare=function(e){var i=function(){return{}};var r=typeof e.onShareAppMessage==="function"?e.onShareAppMessage:i;e.onShareAppMessage=function(e,i,t){var n=r.apply(this,arguments);if(zaAliMini.para.autoTrack&&zaAliMini.para.autoTrack.pageShare){zaAliMini.track("$MPShare",{shareDesc:n&&n.desc||"",shareTitle:n&&n.title||"",$url_path:_.getCurrentPath(),$from:e&&e.from})}return n}};zaAliMini.pageLoad=function(e,i){if(i&&_.isObject(i)&&_.isObject(e))try{e.za_ali_query=_.setQuery(i);var t={};t.pre1=_.getCustomUtmFromQuery(i,"$","_","$"),t.pre2=_.getCustomUtmFromQuery(i,"$latest_","_latest_","$latest_"),e.utm=t}catch(e){logger.info(e)}};zaAliMini.quick=function(){var e=arguments[0],i=arguments[1],t=arguments[2],n=_.isObject(t)?t:{};"appLaunch"===e||"appShow"===e?i?zaAliMini[e](i,n):logger.info("App的launch和show，在sensors.quick第二个参数必须传入App的options参数"):"appHide"===e&&(n=_.isObject(i)?i:{},zaAliMini[e](n))};zaAliMini.getSDKData=function(){function e(e,i){if(!e)return"";return e+"="+i+";"}var i=";";var t=_.info&&_.info.currentProps&&_.info.currentProps.xflow_user_id;i=i+e("_xflow_traceid",_.handleUndefinedValue(_.info&&_.info.currentProps&&_.info.currentProps.trace_id))+e("_xflow_session_id",_.handleUndefinedValue(_.info&&_.info.currentProps&&_.info.currentProps.session_id))+e("_xflow_session_time",_.handleUndefinedValue(_.info&&_.info.currentProps&&_.info.currentProps.session_time))+e("_xflow_uid",_.handleUndefinedValue(zaAliMini.store.getDistinctId()))+(t?e("_xflow_openid",_.handleUndefinedValue(t)):"");return i};zaAliMini.setUserId=zaAliMini.setOpenid=function(e){if(typeof e!=="string"&&typeof e!=="number")return;if(!e)return;zaAliMini.registerApp({xflow_user_id:e})};zaAliMini.setLogin=function(e){if(typeof e!=="string"&&typeof e!=="number")return;if(!e)return;zaAliMini.registerApp({accountId:e});zaAliMini.track("$MPLogin",{accountId:e,$url_path:_.getCurrentPath()})};zaAliMini.dataToWebview=function(){if(!this.hasInit){console.log("SDK needs to be initialized first, please use this method after za.init();");return""}try{var e={uid:zaAliMini.store.getDistinctId(),trace_id:_.handleUndefinedValue(_.info&&_.info.currentProps&&_.info.currentProps.trace_id)};var i="xflow_d_t_wv="+encodeURIComponent(JSON.stringify(e));return i}catch(e){}return""};Object.defineProperty($global,"zaAlipay",{enumerable:!1,configurable:!1,value:{}});var mpHooks={onLoad:1,onShow:1,onHide:1,onReady:1,onUnload:1,onTitleClick:1,onPullDownRefresh:1,onReachBottom:1,onPageScroll:1,onResize:1,onTabItemTap:1,onOptionMenuClick:1,onPopMenuClick:1,onPullIntercept:1,onAddToFavorites:1,onShareAppMessage:1,onShareTimeline:1,eventHandler:1,data:1};function click_proxy(e,i){var o=e[i];e[i]=function(){var e=o.apply(this,arguments),i={},t="";try{if(xflowShowDetailLog)logger.info(arguments)}catch(e){}if(_.isObject(arguments[0])){var n=arguments[0].currentTarget||{},r=arguments[0].target||{};if(_.isObject(zaAliMini.para.framework)&&_.isObject(zaAliMini.para.framework.taro)&&!zaAliMini.para.framework.taro.createApp&&r.id&&n.id&&r.id!==n.id)return e;var a=n.dataset||{};t=arguments[0].type,i.$url_path=_.getCurrentPath(),i.datasets=a}return t&&_.isClick(t)&&zaAliMini.track("$MPClick",i),e}}_.getMethods=function(e){var i=[];for(var t in e)"function"!=typeof e[t]||mpHooks[t]||i.push(t);return i};_.isClick=function(e){return!!{tap:1,longTap:1}[e]};$global.zaAlipay.App=function(e){var i=e.onShow,t=e.onLaunch,n=e.onHide;return e.onShow=function(){i&&i.apply(this,arguments),zaAliMini.appShow(arguments[0])},e.onLaunch=function(){this[zaAliMini.para.name]=zaAliMini,t&&t.apply(this,arguments),zaAliMini.appLaunch(arguments[0])},e.onHide=function(){n&&n.apply(this,arguments),zaAliMini.appHide({})},$global.zaAlipay.useApp=!0,App(e)};$global.zaAlipay.Page=function(e){var i=zaAliMini.para.autoTrack&&zaAliMini.para.autoTrack.mpClick&&_.getMethods(e);if(i)for(var t=0,n=i.length;t<n;t++)click_proxy(e,i[t]);var r=e.onShow,a=e.onLoad;return e.onShow=function(){r&&r.apply(this,arguments),zaAliMini.pageShow()},e.onLoad=function(){a&&a.apply(this,arguments),zaAliMini.pageLoad(this,arguments[0])},$global.zaAlipay.usePage=!0,Page(e)};var oldApp=App;App=function(e){if(oldApp.apply(this,arguments),$global.zaAlipay.useApp)return!1;var i=e.onShow,t=e.onLaunch,n=e.onHide;e.onLaunch=function(){this[zaAliMini.para.name]=zaAliMini,t&&t.apply(this,arguments),zaAliMini.appLaunch(arguments[0])},e.onShow=function(){i&&i.apply(this,arguments),zaAliMini.appShow(arguments[0])},e.onHide=function(){n&&n.apply(this,arguments),zaAliMini.appHide({})}};var oldPage=Page;Page=function(e){if($global.zaAlipay.usePage)return!1;zaAliMini.pageShare(e);var i=zaAliMini.para.autoTrack&&zaAliMini.para.autoTrack.mpClick&&_.getMethods(e);if(i)for(var t=0,n=i.length;t<n;t++)click_proxy(e,i[t]);var r=e.onShow,a=e.onLoad;e.onShow=function(){r&&r.apply(this,arguments),zaAliMini.pageShow()},e.onLoad=function(){a&&a.apply(this,arguments),zaAliMini.pageLoad(this,arguments[0])};oldPage.apply(this,arguments)};zaAliMini.initial();module.exports=zaAliMini;