/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "/";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = "./src/index.js");
/******/ })
/************************************************************************/
/******/ ({

/***/ "./src/base/api.js":
/*!*************************!*\
  !*** ./src/base/api.js ***!
  \*************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _baseConfig__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./baseConfig */ "./src/base/baseConfig.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ "./src/base/utils.js");
/* harmony import */ var _base_console__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../base/console */ "./src/base/console.js");




/**
 * 数据持久化
 */
class API {
  constructor(url) {

    const UPLOAD_PATH = _utils__WEBPACK_IMPORTED_MODULE_1__["default"].getConfigData("uploadUrl")

    this.url = url || `${UPLOAD_PATH}${_baseConfig__WEBPACK_IMPORTED_MODULE_0__["REPORT_URL"]}`
  }
  /**
   * 上报信息 单条记录 （默认方式）
   */
  report(data) {
    if (!this.checkUrl(this.url)) {
      console.error('上报信息url地址格式不正确,url=', this.url)
      return
    }
    Object(_base_console__WEBPACK_IMPORTED_MODULE_2__["default"])('上报地址：' + this.url)
    const mergeData = this.extendData(data)
    this.sendInfo(mergeData)
  }
  /**
   * 上报信息 多条记录 （默认方式）
   */
  reports(data) {
    if (!this.checkUrl(this.url)) {
      console.error('上报信息url地址格式不正确,url=', this.url)
      return
    }
    Object(_base_console__WEBPACK_IMPORTED_MODULE_2__["default"])('上报地址：' + this.url)

    const mergeData = data.map(i => this.extendData(i))
    this.sendInfo(mergeData)
  }
  //扩展一些固定数据 比如url 和 seraphId等
  extendData(data) {
    const { appId, channel, traceId, openId, localInitTime, initServerTime, reportVersion } = _utils__WEBPACK_IMPORTED_MODULE_1__["default"].getConfigData()
    const timeDiff = Date.now() - (localInitTime || Date.now()) // 如果本地时间没有 则差值为0
    let siteInfo = {
      source: _baseConfig__WEBPACK_IMPORTED_MODULE_0__["APP_CHANNEL_ALIAS"], // 渠道
      version: _utils__WEBPACK_IMPORTED_MODULE_1__["default"].getSDKVersions(), // sdk 版本号
      eventTime: (initServerTime || Date.now()) + timeDiff, // 发送时间 如果服务器时间没有则取当前时间
      deviceId: openId,
      appId,
      iseeBiz: traceId,
      reportVersion: data.reportVersion ? data.reportVersion : reportVersion
    }
    let _data = { ...data, ...siteInfo }
    // 渠道
    if (channel) _data.channel = channel
    Object(_base_console__WEBPACK_IMPORTED_MODULE_2__["default"])('report data =', _data)
    return _data
  }
  /**
   * 发送消息
   */
  sendInfo(data) {
    // 如果没有iseeBiz 则失败
    if (!Array.isArray(data) && (!data.iseeBiz || data.iseeBiz == '')) { // 请求中的iseeBiz是traceId
      console.error("traceId 不能为空 请确保是否调用startReport ！")
      return
    }

    let encodeData
    // 获取加密信息
    const { key, iv, secret } = _utils__WEBPACK_IMPORTED_MODULE_1__["default"].getConfigData()

    // 数据加密
    try {

      encodeData = _utils__WEBPACK_IMPORTED_MODULE_1__["default"].AES_Encode(JSON.stringify(data), key, iv)

    } catch (e) {
      console.error("加密错误,请检查上送数据中是否有特殊字符")
    }
    // 数据发送
    try {
      wx.request({
        method: 'POST',
        url: this.url, //接口地址
        data: encodeData, // 扩展地址
        header: {
          'content-type': 'application/json', // 默认值
          'accessKey': secret
        },
        success(res) {
          const { header } = res
          console.log(header)
          _utils__WEBPACK_IMPORTED_MODULE_1__["default"].setConfigData({
            iseeBiz: header.iseebiz || header.Iseebiz // 设置ISSBIZ
          })
          Object(_base_console__WEBPACK_IMPORTED_MODULE_2__["default"])(res.data)
        }
      })
    } catch (error) {
      console.error('发送数据发生异常:' + error)
    }
  }

  /**
   * 通过img方式上报信息
   */
  reportByImg(data) {
    if (!this.checkUrl(this.url)) {
      console.error('上报信息url地址格式不正确,url=', this.url)
      return
    }
    try {
      var img = new Image()
      img.src = this.url + '?v=' + new Date().getTime() + '&' + this.formatParams(data)
    } catch (error) {
      console.error(error)
    }
  }

  /*
   *格式化参数
   */
  formatParams(data) {
    var arr = []
    for (var name in data) {
      arr.push(encodeURIComponent(name) + '=' + encodeURIComponent(data[name]))
    }
    return arr.join('&')
  }

  /**
   * 检测URL
   */
  checkUrl(url) {
    if (!url) {
      return false
    }
    var urlRule = /^[hH][tT][tT][pP]([sS]?):\/\//
    return urlRule.test(url)
  }
}
/* harmony default export */ __webpack_exports__["default"] = (API);


/***/ }),

/***/ "./src/base/baseConfig.js":
/*!********************************!*\
  !*** ./src/base/baseConfig.js ***!
  \********************************/
/*! exports provided: EventTypeEnum, CONFIG_ALIAS, APP_CHANNEL_ALIAS, SERVER_URL, REPORT_URL */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "EventTypeEnum", function() { return EventTypeEnum; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "CONFIG_ALIAS", function() { return CONFIG_ALIAS; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "APP_CHANNEL_ALIAS", function() { return APP_CHANNEL_ALIAS; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "SERVER_URL", function() { return SERVER_URL; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "REPORT_URL", function() { return REPORT_URL; });

/**
 * 上报类型枚举
 */
class EventTypeEnum {
  /**
   * 启动
   */
  static get START() {
    return '0000'
  }

  /**
   * 手动埋点上报
   */
  static get REPORT() {
    return '1000'
  }
  /**
   * 自动埋点上报
   */
  static get AUTO_REPORT() {
    return '2000'
  }
}
// storage Key 名
const CONFIG_ALIAS = '__ISEE_CONFIG__'
// 上传端标识
const APP_CHANNEL_ALIAS = 'WX-MINI'
// 服务地址
let SERVER_URL = 'https://isee-uat.zhongan.com'
//默认上传地址 判断是否是测试环境 并且是否有手动配置过上传地址
let REPORT_URL = `/record/report`


/***/ }),

/***/ "./src/base/baseRecord.js":
/*!********************************!*\
  !*** ./src/base/baseRecord.js ***!
  \********************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _taskQueue_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./taskQueue.js */ "./src/base/taskQueue.js");
/* harmony import */ var _base_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../base/utils */ "./src/base/utils.js");



/**
 * 基类
 */
class BaseRecord {
  /**
   * 上报错误地址
   */
  constructor() {
    this.path = '' // 页面地址
    this.eventType = '' // 类型
    this.reportCode = '' // 上传编码
    this.reportDesc = '' // 上传描述
    this.reportVersion = '' // 上传版本
    this.reportPage = '' //
    this.properties = {} //扩展信息
  }

  /**
   * 记录错误信息
   */
  recordSend() {
    this.handleInitParam()
    //延迟记录日志
    setTimeout(() => {
      _taskQueue_js__WEBPACK_IMPORTED_MODULE_0__["default"].fire()
    }, 500)
  }

  /**
   * 处理记录日志
   */
  handleInitParam() {
    try {
      let info = this.handleRecordInfo()

      //添加发送记录
      _taskQueue_js__WEBPACK_IMPORTED_MODULE_0__["default"].add(info)
    } catch (error) {
      console.error(error)
    }
  }

  /**
   * 处理发送信息
   */
  handleRecordInfo() {

    let recordInfo = {
      eventType: this.eventType, //信息
      reportCode: this.reportCode, // 上传编码
      reportDesc: this.reportDesc, // 上传描述
      reportVersion: this.reportVersion, // 上传版本
      reportPage: this.reportPage, //
    }
    const properties = _base_utils__WEBPACK_IMPORTED_MODULE_1__["default"].getExtendsInfo(this.properties)
    if (_base_utils__WEBPACK_IMPORTED_MODULE_1__["default"].isObject(properties) && JSON.stringify(properties) != '{}')
      recordInfo.properties = {
        path: this.path, // 页面地址
        ...properties
      }
    else
      recordInfo.properties = {
        path: this.path, // 页面地址
      }
    // recordInfo.deviceInfo = this.getDeviceInfo()//错误分类
    // recordInfo.device = deviceInfo //设备信息

    return recordInfo
  }
}
/* harmony default export */ __webpack_exports__["default"] = (BaseRecord);


/***/ }),

/***/ "./src/base/console.js":
/*!*****************************!*\
  !*** ./src/base/console.js ***!
  \*****************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ "./src/base/utils.js");


function Console() {
  if (_utils__WEBPACK_IMPORTED_MODULE_0__["default"].getConfigData("debug")) console.log.apply(this, arguments)
}

/* harmony default export */ __webpack_exports__["default"] = (Console);


/***/ }),

/***/ "./src/base/taskQueue.js":
/*!*******************************!*\
  !*** ./src/base/taskQueue.js ***!
  \*******************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api.js */ "./src/base/api.js");
/* harmony import */ var _baseConfig__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./baseConfig */ "./src/base/baseConfig.js");



/**
 * 消息队列
 */
var TaskQueue = {
  queues: [], //待处理消息列表
  timer: null,
  intervalTime: 1000, //节流间隔
  /**
   * 添加消息
   * @param {*} reportUrl 上报url
   * @param {*} data 上报数据
   */
  add: function (data) {
    // this.queues.push({ reportUrl, data })
    this.queues.push(data)
  },

  /**
   * 统一上报
   */
  fire: function () {
    if (!this.queues || this.queues.length === 0) {
      return
    }

    //做一次节流上传
    if (this.timer) clearInterval(this.timer)
    this.timer = setTimeout(() => {
      // 多条记录发送
      if (this.queues.length > 1) {
        this.multipleReport()
      } else {
        this.signReport()
      }

    }, this.intervalTime)

  },
  /**
   * 单条记录上送
   */
  signReport: function () {
    let item = this.queues[0]
    new _api_js__WEBPACK_IMPORTED_MODULE_0__["default"]().report(item)
    this.queues.splice(0, 1)
    this.fire() //递归
  },
  multipleReport: function () {
    new _api_js__WEBPACK_IMPORTED_MODULE_0__["default"]().reports(this.queues)
    this.queues = []
    this.fire() //递归
  }
}

/* harmony default export */ __webpack_exports__["default"] = (TaskQueue);


/***/ }),

/***/ "./src/base/utils.js":
/*!***************************!*\
  !*** ./src/base/utils.js ***!
  \***************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _baseConfig__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./baseConfig */ "./src/base/baseConfig.js");
/* harmony import */ var _cryptojs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../cryptojs */ "./src/cryptojs/index.js");
/* harmony import */ var _cryptojs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_cryptojs__WEBPACK_IMPORTED_MODULE_1__);




/* harmony default export */ __webpack_exports__["default"] = ({
  type(obj) {
    return Object.prototype.toString.call(obj).replace(/\[object\s|\]/g, '')
  },

  isFunction(func) {
    return this.type(func) === 'Function'
  },

  isArray(list) {
    return this.type(list) === 'Array'
  },
  /*
   * 是否为null
   * @param {String} str
   */
  isNull(str) {
    return str == undefined || str == '' || str == null
  },
  /**
   * 判断目标对象 是否是指定类型
   * @param {目标对象} e
   * @param {是否和类型相等} t
   */
  judgeType(e, t) {
    return 'Array' === t && Array.isArray
      ? Array.isArray(e)
      : Object.prototype.toString.call(e) === '[object ' + t + ']'
  },
  /**
   * 对象是否为空
   * @param {*} obj
   */
  objectIsNull(obj) {
    return JSON.stringify(obj) === '{}'
  },

  /**
   * 是否是对象
   * @param {*} obj
   */
  isObject(obj) {
    return this.type(obj) === 'Object'
  },
  /**
   * 获取扩展信息
   */
  getExtendsInfo(obj) {
    try {
      let ret = {}
      let extendsInfo = obj || {}
      let dynamicParams
      if (this.isFunction(extendsInfo.getDynamic)) {
        dynamicParams = extendsInfo.getDynamic() //获取动态参数
      }
      //判断动态方法返回的参数是否是对象
      if (this.isObject(dynamicParams)) {
        extendsInfo = { ...extendsInfo, ...dynamicParams }
      }
      //遍历扩展信息，排除动态方法
      for (var key in extendsInfo) {
        if (!this.isFunction(extendsInfo[key])) {
          //排除获取动态方法
          ret[key] = extendsInfo[key]
        }
      }
      return ret
    } catch (error) {
      console.error('call getExtendsInfo error', error)
      return {}
    }
  },
  /**
   * 生成一个UUID
   */
  uuid() {
    var s = [];
    var hexDigits = "0123456789ABCDEF";
    for (var i = 0; i < 36; i++) {
      s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
    }
    s[14] = "4";  // bits 12-15 of the time_hi_and_version field to 0010
    s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1);  // bits 6-7 of the clock_seq_hi_and_reserved to 01
    s[8] = s[13] = s[18] = s[23] = "-";

    var uuid = s.join("");
    return uuid;
  },
  /**
   * 创建traceId
   */
  createTraceId(ms) {
    let timeDiff = ms
    if (!timeDiff) {
      const { localInitTime, initServerTime } = this.getConfigData()
      timeDiff = (initServerTime || Date.now()) + Date.now() - (localInitTime || Date.now())// 如果本地时间没有 则差值为0
    }

    const START = 1577808000
    const INDEX = [8, 13, 18, 23, 36]
    const RADIX_TABLE = [
      '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
      'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm',
      'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
      'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',
      'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
      '.', '_'
    ];
    timeDiff = timeDiff / 1000 - START;
    //字符串转化为字符数组
    let chars = this.uuid().split('');
    let trace = [...chars];

    trace[chars.length] = '0';
    let i = 0;
    do {
      trace[INDEX[i++]] = RADIX_TABLE[parseInt(timeDiff % 64)];
      timeDiff /= 64;
    } while (timeDiff != 0);
    return trace.join('');
  },
  /**
   * 获取SDK 版本号
   */
  getSDKVersions() {
    return `${"1.0.0"}`
  },
  /**
   * 获取配置信息
   * @param {对应key名} key 
   */
  getConfigData(key) {
    try {
      const config = wx.getStorageSync(_baseConfig__WEBPACK_IMPORTED_MODULE_0__["CONFIG_ALIAS"])
      return key ? (config[key] || null) : config
    } catch (e) { console.error("读取配置失败:" + e) }
  },
  /**
   * 写入配置信息 *增量添加不是全量
   * @param {对象} c 
   */
  setConfigData(c) {
    try {
      const config = this.getConfigData()
      wx.setStorageSync(_baseConfig__WEBPACK_IMPORTED_MODULE_0__["CONFIG_ALIAS"], { ...config, ...c })
    } catch (e) { console.error("配置写入失败:" + e) }
  },
  /**
   * 清除配置文件
   */
  clearConfig() {
    try {
      wx.removeStorageSync(_baseConfig__WEBPACK_IMPORTED_MODULE_0__["CONFIG_ALIAS"])
    } catch (e) {
      console.error("配置写入失败:" + e)
    }
  },
  /** */
  AES_Encode(data, key, iv) { //加密
    if (!key || !iv) {
      console.error("加密错误! 请输入AES key，iv值")
      return data
    }
    let parseKey = _cryptojs__WEBPACK_IMPORTED_MODULE_1___default.a.enc.Utf8.parse(key);
    let parseIV = _cryptojs__WEBPACK_IMPORTED_MODULE_1___default.a.enc.Utf8.parse(iv);
    let encrypted = _cryptojs__WEBPACK_IMPORTED_MODULE_1___default.a.AES.encrypt(data, parseKey, {
      iv: parseIV,
      mode: _cryptojs__WEBPACK_IMPORTED_MODULE_1___default.a.mode.CBC,
      padding: _cryptojs__WEBPACK_IMPORTED_MODULE_1___default.a.pad.Pkcs7
    });
    let r = encrypted.toString()
    return r.replace(/\+/g, '.').replace(/\//g, '_').replace(/=/g, '');
  }
});


/***/ }),

/***/ "./src/cryptojs/index.js":
/*!*******************************!*\
  !*** ./src/cryptojs/index.js ***!
  \*******************************/
/*! no static exports found */
/***/ (function(module, exports) {

var CryptoJS = CryptoJS || function (u, p) {
  var d = {},
    l = d.lib = {},
    s = function () { },
    t = l.Base = {
      extend: function (a) {
        s.prototype = this;
        var c = new s;
        a && c.mixIn(a);
        c.hasOwnProperty("init") || (c.init = function () {
          c.$super.init.apply(this, arguments)
        });
        c.init.prototype = c;
        c.$super = this;
        return c
      },
      create: function () {
        var a = this.extend();
        a.init.apply(a, arguments);
        return a
      },
      init: function () { },
      mixIn: function (a) {
        for (var c in a) a.hasOwnProperty(c) && (this[c] = a[c]);
        a.hasOwnProperty("toString") && (this.toString = a.toString)
      },
      clone: function () {
        return this.init.prototype.extend(this)
      }
    },
    r = l.WordArray = t.extend({
      init: function (a, c) {
        a = this.words = a || [];
        this.sigBytes = c != p ? c : 4 * a.length
      },
      toString: function (a) {
        return (a || v).stringify(this)
      },
      concat: function (a) {
        var c = this.words,
          e = a.words,
          j = this.sigBytes;
        a = a.sigBytes;
        this.clamp();
        if (j % 4)
          for (var k = 0; k < a; k++) c[j + k >>> 2] |= (e[k >>> 2] >>> 24 - 8 * (k % 4) & 255) << 24 - 8 * ((j + k) % 4);
        else if (65535 < e.length)
          for (k = 0; k < a; k += 4) c[j + k >>> 2] = e[k >>> 2];
        else c.push.apply(c, e);
        this.sigBytes += a;
        return this
      },
      clamp: function () {
        var a = this.words,
          c = this.sigBytes;
        a[c >>> 2] &= 4294967295 <<
          32 - 8 * (c % 4);
        a.length = u.ceil(c / 4)
      },
      clone: function () {
        var a = t.clone.call(this);
        a.words = this.words.slice(0);
        return a
      },
      random: function (a) {
        for (var c = [], e = 0; e < a; e += 4) c.push(4294967296 * u.random() | 0);
        return new r.init(c, a)
      }
    }),
    w = d.enc = {},
    b = w.Latin1 = {
      stringify: function (a) {
        var c = a.words;
        a = a.sigBytes;
        for (var e = [], j = 0; j < a; j++) e.push(String.fromCharCode(c[j >>> 2] >>> 24 - 8 * (j % 4) & 255));
        return e.join("")
      },
      parse: function (a) {
        for (var c = a.length, e = [], j = 0; j < c; j++) e[j >>> 2] |= (a.charCodeAt(j) & 255) << 24 - 8 * (j % 4);
        return new r.init(e, c)
      }
    },
    x = w.Utf8 = {
      stringify: function (a) {
        try {
          return decodeURIComponent(escape(b.stringify(a)))
        } catch (c) {
          throw Error("Malformed UTF-8 data");
        }
      },
      parse: function (a) {
        return b.parse(unescape(encodeURIComponent(a)))
      }
    },
    q = l.BufferedBlockAlgorithm = t.extend({
      reset: function () {
        this._data = new r.init;
        this._nDataBytes = 0
      },
      _append: function (a) {
        "string" == typeof a && (a = x.parse(a));
        this._data.concat(a);
        this._nDataBytes += a.sigBytes
      },
      _process: function (a) {
        var c = this._data,
          e = c.words,
          j = c.sigBytes,
          k = this.blockSize,
          b = j / (4 * k),
          b = a ? u.ceil(b) : u.max((b | 0) - this._minBufferSize, 0);
        a = b * k;
        j = u.min(4 * a, j);
        if (a) {
          for (var q = 0; q < a; q += k) this._doProcessBlock(e, q);
          q = e.splice(0, a);
          c.sigBytes -= j
        }
        return new r.init(q, j)
      },
      clone: function () {
        var a = t.clone.call(this);
        a._data = this._data.clone();
        return a
      },
      _minBufferSize: 0
    });
  var n = d.algo = {};
  return d
}(Math);
(function () {
  var u = CryptoJS,
    p = u.lib.WordArray;
  u.enc.Base64 = {
    stringify: function (d) {
      var l = d.words,
        p = d.sigBytes,
        t = this._map;
      d.clamp();
      d = [];
      for (var r = 0; r < p; r += 3)
        for (var w = (l[r >>> 2] >>> 24 - 8 * (r % 4) & 255) << 16 | (l[r + 1 >>> 2] >>> 24 - 8 * ((r + 1) % 4) & 255) << 8 | l[r + 2 >>> 2] >>> 24 - 8 * ((r + 2) % 4) & 255, v = 0; 4 > v && r + 0.75 * v < p; v++) d.push(t.charAt(w >>> 6 * (3 - v) & 63));
      if (l = t.charAt(64))
        for (; d.length % 4;) d.push(l);
      return d.join("")
    },
    _map: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="
  }
})();

CryptoJS.lib.Cipher || function (u) {
  var p = CryptoJS,
    d = p.lib,
    l = d.Base,
    s = d.WordArray,
    t = d.BufferedBlockAlgorithm,
    r = p.enc.Base64,
    v = d.Cipher = t.extend({
      cfg: l.extend(),
      createEncryptor: function (e, a) {
        return this.create(this._ENC_XFORM_MODE, e, a)
      },
      createDecryptor: function (e, a) {
        return this.create(this._DEC_XFORM_MODE, e, a)
      },
      init: function (e, a, b) {
        this.cfg = this.cfg.extend(b);
        this._xformMode = e;
        this._key = a;
        this.reset()
      },
      reset: function () {
        t.reset.call(this);
        this._doReset()
      },
      process: function (e) {
        this._append(e);
        return this._process()
      },
      finalize: function (e) {
        e && this._append(e);
        return this._doFinalize()
      },
      keySize: 4,
      ivSize: 4,
      _ENC_XFORM_MODE: 1,
      _DEC_XFORM_MODE: 2,
      _createHelper: function (e) {
        return {
          encrypt: function (b, k, d) {
            return ("string" == typeof k ? c : a).encrypt(e, b, k, d)
          },
          decrypt: function (b, k, d) {
            return ("string" == typeof k ? c : a).decrypt(e, b, k, d)
          }
        }
      }
    });

  var b = p.mode = {},
    x = function (e, a, b) {
      var c = this._iv;
      c ? this._iv = u : c = this._prevBlock;
      for (var d = 0; d < b; d++) e[a + d] ^=
        c[d]
    },
    q = (d.BlockCipherMode = l.extend({
      createEncryptor: function (e, a) {
        return this.Encryptor.create(e, a)
      },
      createDecryptor: function (e, a) {
        return this.Decryptor.create(e, a)
      },
      init: function (e, a) {
        this._cipher = e;
        this._iv = a
      }
    })).extend();
  q.Encryptor = q.extend({
    processBlock: function (e, a) {
      var b = this._cipher,
        c = b.blockSize;
      x.call(this, e, a, c);
      b.encryptBlock(e, a);
      this._prevBlock = e.slice(a, a + c)
    }
  });
  b = b.CBC = q;
  q = (p.pad = {}).Pkcs7 = {
    pad: function (a, b) {
      for (var c = 4 * b, c = c - a.sigBytes % c, d = c << 24 | c << 16 | c << 8 | c, l = [], n = 0; n < c; n += 4) l.push(d);
      c = s.create(l, c);
      a.concat(c)
    },
    unpad: function (a) {
      a.sigBytes -= a.words[a.sigBytes - 1 >>> 2] & 255
    }
  };
  d.BlockCipher = v.extend({
    cfg: v.cfg.extend({
      mode: b,
      padding: q
    }),
    reset: function () {
      v.reset.call(this);
      var a = this.cfg,
        b = a.iv,
        a = a.mode;
      if (this._xformMode == this._ENC_XFORM_MODE) var c = a.createEncryptor;
      else c = a.createDecryptor, this._minBufferSize = 1;
      this._mode = c.call(a,
        this, b && b.words)
    },
    _doProcessBlock: function (a, b) {
      this._mode.processBlock(a, b)
    },
    _doFinalize: function () {
      var a = this.cfg.padding;
      if (this._xformMode == this._ENC_XFORM_MODE) {
        a.pad(this._data, this.blockSize);
        var b = this._process(!0)
      } else b = this._process(!0), a.unpad(b);
      return b
    },
    blockSize: 4
  });
  var n = d.CipherParams = l.extend({
    init: function (a) {
      this.mixIn(a)
    },
    toString: function (a) {
      return (a || this.formatter).stringify(this)
    }
  }),
    b = (p.format = {}).OpenSSL = {
      stringify: function (a) {
        var b = a.ciphertext;
        a = a.salt;
        return (a ? s.create([1398893684,
          1701076831
        ]).concat(a).concat(b) : b).toString(r)
      },
      parse: function (a) {
        a = r.parse(a);
        var b = a.words;
        if (1398893684 == b[0] && 1701076831 == b[1]) {
          var c = s.create(b.slice(2, 4));
          b.splice(0, 4);
          a.sigBytes -= 16
        }
        return n.create({
          ciphertext: a,
          salt: c
        })
      }
    },
    a = d.SerializableCipher = l.extend({
      cfg: l.extend({
        format: b
      }),
      encrypt: function (a, b, c, d) {
        d = this.cfg.extend(d);
        var l = a.createEncryptor(c, d);
        b = l.finalize(b);
        l = l.cfg;
        return n.create({
          ciphertext: b,
          key: c,
          iv: l.iv,
          algorithm: a,
          mode: l.mode,
          padding: l.padding,
          blockSize: a.blockSize,
          formatter: d.format
        })
      },
      decrypt: function (a, b, c, d) {
        d = this.cfg.extend(d);
        b = this._parse(b, d.format);
        return a.createDecryptor(c, d).finalize(b.ciphertext)
      },
      _parse: function (a, b) {
        return "string" == typeof a ? b.parse(a, this) : a
      }
    })
}();
(function () {
  for (var u = CryptoJS, p = u.lib.BlockCipher, d = u.algo, l = [], s = [], t = [], r = [], w = [], v = [], b = [], x = [], q = [], n = [], a = [], c = 0; 256 > c; c++) a[c] = 128 > c ? c << 1 : c << 1 ^ 283;
  for (var e = 0, j = 0, c = 0; 256 > c; c++) {
    var k = j ^ j << 1 ^ j << 2 ^ j << 3 ^ j << 4,
      k = k >>> 8 ^ k & 255 ^ 99;
    l[e] = k;
    s[k] = e;
    var z = a[e],
      F = a[z],
      G = a[F],
      y = 257 * a[k] ^ 16843008 * k;
    t[e] = y << 24 | y >>> 8;
    r[e] = y << 16 | y >>> 16;
    w[e] = y << 8 | y >>> 24;
    v[e] = y;
    y = 16843009 * G ^ 65537 * F ^ 257 * z ^ 16843008 * e;
    b[k] = y << 24 | y >>> 8;
    x[k] = y << 16 | y >>> 16;
    q[k] = y << 8 | y >>> 24;
    n[k] = y;
    e ? (e = z ^ a[a[a[G ^ z]]], j ^= a[a[j]]) : e = j = 1
  }
  var H = [0, 1, 2, 4, 8,
    16, 32, 64, 128, 27, 54
  ],
    d = d.AES = p.extend({
      _doReset: function () {
        for (var a = this._key, c = a.words, d = a.sigBytes / 4, a = 4 * ((this._nRounds = d + 6) + 1), e = this._keySchedule = [], j = 0; j < a; j++)
          if (j < d) e[j] = c[j];
          else {
            var k = e[j - 1];
            j % d ? 6 < d && 4 == j % d && (k = l[k >>> 24] << 24 | l[k >>> 16 & 255] << 16 | l[k >>> 8 & 255] << 8 | l[k & 255]) : (k = k << 8 | k >>> 24, k = l[k >>> 24] << 24 | l[k >>> 16 & 255] << 16 | l[k >>> 8 & 255] << 8 | l[k & 255], k ^= H[j / d | 0] << 24);
            e[j] = e[j - d] ^ k
          }
        c = this._invKeySchedule = [];
        for (d = 0; d < a; d++) j = a - d, k = d % 4 ? e[j] : e[j - 4], c[d] = 4 > d || 4 >= j ? k : b[l[k >>> 24]] ^ x[l[k >>> 16 & 255]] ^ q[l[k >>>
          8 & 255]] ^ n[l[k & 255]]
      },
      encryptBlock: function (a, b) {
        this._doCryptBlock(a, b, this._keySchedule, t, r, w, v, l)
      },
      _doCryptBlock: function (a, b, c, d, e, j, l, f) {
        for (var m = this._nRounds, g = a[b] ^ c[0], h = a[b + 1] ^ c[1], k = a[b + 2] ^ c[2], n = a[b + 3] ^ c[3], p = 4, r = 1; r < m; r++) var q = d[g >>> 24] ^ e[h >>> 16 & 255] ^ j[k >>> 8 & 255] ^ l[n & 255] ^ c[p++],
          s = d[h >>> 24] ^ e[k >>> 16 & 255] ^ j[n >>> 8 & 255] ^ l[g & 255] ^ c[p++],
          t =
            d[k >>> 24] ^ e[n >>> 16 & 255] ^ j[g >>> 8 & 255] ^ l[h & 255] ^ c[p++],
          n = d[n >>> 24] ^ e[g >>> 16 & 255] ^ j[h >>> 8 & 255] ^ l[k & 255] ^ c[p++],
          g = q,
          h = s,
          k = t;
        q = (f[g >>> 24] << 24 | f[h >>> 16 & 255] << 16 | f[k >>> 8 & 255] << 8 | f[n & 255]) ^ c[p++];
        s = (f[h >>> 24] << 24 | f[k >>> 16 & 255] << 16 | f[n >>> 8 & 255] << 8 | f[g & 255]) ^ c[p++];
        t = (f[k >>> 24] << 24 | f[n >>> 16 & 255] << 16 | f[g >>> 8 & 255] << 8 | f[h & 255]) ^ c[p++];
        n = (f[n >>> 24] << 24 | f[g >>> 16 & 255] << 16 | f[h >>> 8 & 255] << 8 | f[k & 255]) ^ c[p++];
        a[b] = q;
        a[b + 1] = s;
        a[b + 2] = t;
        a[b + 3] = n
      },
      keySize: 8
    });
  u.AES = p._createHelper(d)
})();

module.exports = CryptoJS

/***/ }),

/***/ "./src/index.js":
/*!**********************!*\
  !*** ./src/index.js ***!
  \**********************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _isee_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./isee.js */ "./src/isee.js");


// CommonJS
exports.ZAiSeeSDK = _isee_js__WEBPACK_IMPORTED_MODULE_0__["default"]
// es6
/* harmony default export */ __webpack_exports__["default"] = (_isee_js__WEBPACK_IMPORTED_MODULE_0__["default"]);

// 判断是否有window对象
window && (window.ZAiSeeSDK = _isee_js__WEBPACK_IMPORTED_MODULE_0__["default"])
// 判断是否有wx对象
wx && (wx.ZAiSeeSDK = _isee_js__WEBPACK_IMPORTED_MODULE_0__["default"])



/***/ }),

/***/ "./src/isee.js":
/*!*********************!*\
  !*** ./src/isee.js ***!
  \*********************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _record__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./record */ "./src/record/index.js");
/* harmony import */ var _base_baseConfig__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base/baseConfig */ "./src/base/baseConfig.js");
/* harmony import */ var _base_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./base/utils */ "./src/base/utils.js");





const window = wx ? wx : window // 在微信环境中 wx进行挂载

// 设置私有方法
const _PS = Symbol('PersistenceStorage');
const _Check = Symbol('AuthCheck');

class ZAiSeeSDK {
  constructor(options) {
    const { appId, openId, uploadUrl, key, secret } = options || {}
    // 必要参数验证
    if (!options || !appId || !openId) {
      console.error("初始化失败,appId、openId等为必传字段")
      return false
    }

    if (!uploadUrl) {
      console.error("初始化失败,uploadUrl请配置上报地址")
      return false
    }

    if (!key || !secret) {
      console.error("初始化失败,请配置加密秘钥key,secret等信息")
      return false
    }

    // 默认不上报
    this.reportFlag = false
    // 执行检查
    this[_Check](options)
  }
  /**
   * 本地持久化存储
   * @param {*} options 
   */
  [_PS](options) {
    // 清除之前的信息
    _base_utils__WEBPACK_IMPORTED_MODULE_2__["default"].clearConfig()
    // 信息持久化存储
    _base_utils__WEBPACK_IMPORTED_MODULE_2__["default"].setConfigData({
      debug: options.debug || false, //开发模式
      // properties: options.properties || undefined,  //扩展信息
      appId: options.appId || '',
      uploadUrl: options.uploadUrl || '', //上传地址
      // channel: options.channel || '', //渠道信息
      initServerTime: +this.initServerTime, // SDK 初始化时间点 (服务端)
      localInitTime: Date.now(), //SDK 初始化本地时间
      reportVersion: options.reportVersion || '',// 业务方版本号
      openId: options.openId || '',
      key: options.key,
      secret: options.secret,
      iv: options.iv || 'f3k9f5z7f8u1nb20' // 默认偏移量
    })
  }
  /**
   * 合法性校验
   * @param {参数} options 
   */
  [_Check](options) {
    const { appId, openId, uploadUrl, } = options || {}

    // 发送请求
    window.request({
      url: `${uploadUrl || _base_baseConfig__WEBPACK_IMPORTED_MODULE_1__["SERVER_URL"]}/init`,
      method: 'POST',
      data: {
        appID: appId,
        version: _base_utils__WEBPACK_IMPORTED_MODULE_2__["default"].getSDKVersions(),
        deviceId: openId,
        source: _base_baseConfig__WEBPACK_IMPORTED_MODULE_1__["APP_CHANNEL_ALIAS"]
      },
      success: ({ data }) => {
        const { returnFlag, resultContent } = data
        if (!returnFlag) {
          console.error("appId 校验失败！")
          return false
        }
        // 是否上报
        this.reportFlag =  false || returnFlag
        //SDK 初始化时间点 (服务端)
        this.initServerTime = resultContent

        // 本地持久化存储
        this[_PS](options)
        // 初始化
        // this.init(options)
        // 阻止对象修改
        Object.freeze(this)
      }
    })

  }
  /**
   * 获取TraceId
   */
  getISeeBiz() {
    const MAX = 10

    return new Promise((resolve, reject) => {
      let count = 0;
      let bizId
      let getBizId = () => {
        setTimeout(() => {
          bizId = _base_utils__WEBPACK_IMPORTED_MODULE_2__["default"].getConfigData("iseeBiz")
          if (count++ <= MAX) {
            bizId ? resolve(bizId) : getBizId()
          }
        }, 1000)

        if (count >= MAX && !bizId) reject("获取iseeBiz失败,请确保是否调用startRecord")
      }

      getBizId()
    }).catch((e) => {
      console.error(e)
    })
  }
  /**
   * 设置traceId
   * @param {*} traceId 
   */
  setISeeBiz(iseeBiz) {
    return _base_utils__WEBPACK_IMPORTED_MODULE_2__["default"].setConfigData({ iseeBiz })
  }
  getIseeTraceId() {
    return this.getISeeBiz()
  }
  /**
   * 设置TraceId
   * @param {*} traceId 
   */
  setIseeTraceId(iseeBiz) {
    return this.setISeeBiz(iseeBiz)
  }
  /**
   * 发送记录
   * @param {*} param 
   */
  track(param) {
    if (!this.reportFlag) return false
    new _record__WEBPACK_IMPORTED_MODULE_0__["TrackRecord"]().handleSend(param)
  }
  /**
   * 设置OpenId
   * @param {*} openId 
   */
  setOpenId(openId) {
    if (typeof openId !== 'string') {
      console.error("请输入正确的OpenId !")
      return false
    }
    return _base_utils__WEBPACK_IMPORTED_MODULE_2__["default"].setConfigData({ openId })
  }
  /**
   * 开始记录
   */
  startRecord(param) {
    if (!this.reportFlag) return false
    new _record__WEBPACK_IMPORTED_MODULE_0__["StartRecord"]().handleSend(param)
  }
}

/* harmony default export */ __webpack_exports__["default"] = (ZAiSeeSDK);




/***/ }),

/***/ "./src/record/index.js":
/*!*****************************!*\
  !*** ./src/record/index.js ***!
  \*****************************/
/*! exports provided: StartRecord, TrackRecord */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _startRecord_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./startRecord.js */ "./src/record/startRecord.js");
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "StartRecord", function() { return _startRecord_js__WEBPACK_IMPORTED_MODULE_0__["default"]; });

/* harmony import */ var _trackRecord_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./trackRecord.js */ "./src/record/trackRecord.js");
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "TrackRecord", function() { return _trackRecord_js__WEBPACK_IMPORTED_MODULE_1__["default"]; });







/***/ }),

/***/ "./src/record/startRecord.js":
/*!***********************************!*\
  !*** ./src/record/startRecord.js ***!
  \***********************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _base_baseRecord_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../base/baseRecord.js */ "./src/base/baseRecord.js");
/* harmony import */ var _base_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../base/utils */ "./src/base/utils.js");
/* harmony import */ var _base_baseConfig__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../base/baseConfig */ "./src/base/baseConfig.js");



/**
 * 开始记录信息
 */
class StartRecord extends _base_baseRecord_js__WEBPACK_IMPORTED_MODULE_0__["default"] {
  constructor(params) {
    super(params)
    _base_utils__WEBPACK_IMPORTED_MODULE_1__["default"].setConfigData({ traceId: _base_utils__WEBPACK_IMPORTED_MODULE_1__["default"].createTraceId() })
  }

  /**
   * 注册onerror事件
   */
  handleSend({ path, reportCode, reportDesc, reportPage, reportVersion, properties }) {
    try {
      this.path = path
      this.reportCode = reportCode // 上传编码
      this.reportDesc = reportDesc // 上传描述
      this.reportVersion = reportVersion // 上传版本
      this.reportPage = reportPage //
      this.properties = properties //扩展信息
      this.eventType = _base_baseConfig__WEBPACK_IMPORTED_MODULE_2__["EventTypeEnum"].START
      this.recordSend()
    } catch (error) {
      console.error('handleSend:记录错误', error)
    }
  }
}
/* harmony default export */ __webpack_exports__["default"] = (StartRecord);


/***/ }),

/***/ "./src/record/trackRecord.js":
/*!***********************************!*\
  !*** ./src/record/trackRecord.js ***!
  \***********************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _base_baseRecord_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../base/baseRecord.js */ "./src/base/baseRecord.js");
/* harmony import */ var _base_baseConfig__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../base/baseConfig */ "./src/base/baseConfig.js");


/**
 * 发送记录信息
 */
class Track extends _base_baseRecord_js__WEBPACK_IMPORTED_MODULE_0__["default"] {
  constructor(params) {
    super(params)
  }

  /**
   * 注册onerror事件
   */
  handleSend({ path, reportCode, reportDesc, reportPage, reportVersion, properties }) {
    try {
      this.path = path
      this.reportCode = reportCode // 上传编码
      this.reportDesc = reportDesc // 上传描述
      this.reportVersion = reportVersion // 上传版本
      this.reportPage = reportPage //
      this.properties = properties //扩展信息
      this.eventType = _base_baseConfig__WEBPACK_IMPORTED_MODULE_1__["EventTypeEnum"].REPORT
      this.recordSend()
    } catch (error) {
      console.error('handleSend:记录错误', error)
    }
  }
}
/* harmony default export */ __webpack_exports__["default"] = (Track);


/***/ })

/******/ });
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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