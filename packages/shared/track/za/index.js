var _={};var zaWeMini={};zaWeMini.para={name:"zaWeMini",server_url:"https://xflowcloud.zhongan.io/nginx/cloud_xcx_sdk",send_timeout:1e3,max_string_length:300,datasend_timeout:6e3,source_channel:[],autoTrack:{appLaunch:true,appShow:true,appHide:true,pageShow:true,pageShare:true,mpClick:true,mpFavorite:true},is_persistent_save:false,app_name:"",app_id:"",source_id:"",ABTestUrl:"",tenantCode:""};_.ilog4EventName="xcxIlog4Custom";_.ilog4LatestDesc="ilog5单边发送ilog4自定义事件";_.ilog4BothDesc="ilog5两边发送ilog4自定义事件";var hasAdaptedIlog4=false;var adaptIlog4=function(e,t,i){var a=e&&e.adaptIlog4;try{if(t===true){if(a==="both"||a==="latest"){var n=i&&i.zaTitle;var r=i&&i.zaIdx;if(n||r){zaWeMini.customTrack({event_type:n||r,event_value:_.ilog4EventName,event_description:"ilog5点击发送ilog4自定义事件"})}}return}var o=getApp();if(a==="both"){for(var s=0;s<10;s++){setTimeout(function(){try{var e=o.zast&&o.zast.sendEvent;if(_.isObject(o.zast)&&_.isFunction(e)&&!hasAdaptedIlog4){hasAdaptedIlog4=true;if(o.zast){o.zast.sendEvent=function(){zaWeMini.customTrack({event_type:arguments&&arguments[0],event_value:_.ilog4EventName,event_description:_.ilog4BothDesc});e.apply(o.zast.__proto__,arguments)}}}}catch(e){logger.info("adaptIlog4 error",e)}},s*1e3)}}else if(a==="latest"){o.zast=o.zast||{};o.zast.sendEvent=function(){zaWeMini.customTrack({event_type:arguments&&arguments[0],event_value:_.ilog4EventName,event_description:_.ilog4LatestDesc})}}}catch(e){logger.info("adaptIlog4 error",e)}};zaWeMini._proxy={type:{$MPLaunch:"launch",$MPShow:"show",$MPShare:"share",$MPViewScreen:"page",$MPHide:"hide",$MPClick:"click",$MPCustom:"custom",$MPLogin:"SignUp",$MPABTest:"ABTestTrigger",$MPAddFavorites:"MPAddFavorites"},clickEventTypes:["tap","longpress","longtap"]};var mpHook={data:1,onLoad:1,onShow:1,onReady:1,onPullDownRefresh:1,onShareAppMessage:1,onShareTimeline:1,onReachBottom:1,onPageScroll:1,onResize:1,onTabItemTap:1,onHide:1,onUnload:1};var logger=typeof logger==="object"?logger:{};logger.info=function(){if(zaWeMini.para.debug){if(typeof console==="object"&&console.log){try{return console.log.apply(console,arguments)}catch(e){console.log(arguments[0])}}}};zaWeMini.setPara=function(e){zaWeMini.para=_.extend2Lev(zaWeMini.para,e);var t=[];if(_.isArray(zaWeMini.para.source_channel)){var i=zaWeMini.para.source_channel.length;var a=" utm_source utm_medium utm_campaign utm_content utm_term sa_utm ";for(var n=0;n<i;n++){if(a.indexOf(" "+zaWeMini.para.source_channel[n]+" ")===-1){t.push(zaWeMini.para.source_channel[n])}}}zaWeMini.para.source_channel=t;if(_.isObject(zaWeMini.para.register)){_.extend(_.info.properties,zaWeMini.para.register)}if(typeof zaWeMini.para.send_timeout!=="number"){zaWeMini.para.send_timeout=1e3}var r={send_timeout:6e3,max_length:6};if(zaWeMini.para.batch_send===true){zaWeMini.para.batch_send=_.extend({},r)}else if(typeof zaWeMini.para.batch_send==="object"){zaWeMini.para.batch_send=_.extend({},r,zaWeMini.para.batch_send)}if(!zaWeMini.para.server_url){logger.info("请使用 setPara() 方法设置 server_url 数据接收地址");return}if(zaWeMini.para.batch_send&&zaWeMini.para.server_url.indexOf("cloud_xcx_sdk_batch")===-1){zaWeMini.para.server_url=zaWeMini.para.server_url.replace("cloud_xcx_sdk","cloud_xcx_sdk_batch")}};zaWeMini.status={};var ArrayProto=Array.prototype,FuncProto=Function.prototype,ObjProto=Object.prototype,slice=ArrayProto.slice,toString=ObjProto.toString,hasOwnProperty=ObjProto.hasOwnProperty,LIB_VERSION="2.0.2",LIB_NAME="MiniProgram";var source_channel_standard="utm_campaign utm_source utm_medium utm_term utm_content bizOrigin messageNo taskCode clickid ABTVersion";var latest_source_channel=["$latest_utm_campaign","$latest_utm_source","$latest_utm_campaign","$latest_utm_term","$latest_bizOrigin"];var mp_scene={1001:"发现栏小程序主入口,'最近使用'列表",1005:"顶部搜索框的搜索结果页",1006:"发现栏小程序主入口搜索框的搜索结果页",1007:"单人聊天会话中的小程序消息卡片",1008:"群聊会话中的小程序消息卡片",1011:"扫描二维码",1012:"长按图片识别二维码",1013:"手机相册选取二维码",1014:"小程序模版消息",1017:"前往体验版的入口页",1019:"微信钱包",1020:"公众号 profile 页相关小程序列表",1022:"聊天顶部置顶小程序入口",1023:"安卓系统桌面图标",1024:"小程序 profile 页",1025:"扫描一维码",1026:"附近小程序列表",1027:"顶部搜索框搜索结果页'使用过的小程序'列表",1028:"我的卡包",1029:"卡券详情页",1030:"自动化测试下打开小程序",1031:"长按图片识别一维码",1032:"手机相册选取一维码",1034:"微信支付完成页",1035:"公众号自定义菜单",1036:"App 分享消息卡片",1037:"小程序打开小程序",1038:"从另一个小程序返回",1039:"摇电视",1042:"添加好友搜索框的搜索结果页",1043:"公众号模板消息",1044:"带 shareTicket 的小程序消息卡片（详情)",1045:"朋友圈广告",1046:"朋友圈广告详情页",1047:"扫描小程序码",1048:"长按图片识别小程序码",1049:"手机相册选取小程序码",1052:"卡券的适用门店列表",1053:"搜一搜的结果页",1054:"顶部搜索框小程序快捷入口",1056:"音乐播放器菜单",1057:"钱包中的银行卡详情页",1058:"公众号文章",1059:"体验版小程序绑定邀请页",1064:"微信连Wi-Fi状态栏",1067:"公众号文章广告",1068:"附近小程序列表广告",1069:"移动应用",1071:"钱包中的银行卡列表页",1072:"二维码收款页面",1073:"客服消息列表下发的小程序消息卡片",1074:"公众号会话下发的小程序消息卡片",1077:"摇周边",1078:"连Wi-Fi成功页",1079:"微信游戏中心",1081:"客服消息下发的文字链",1082:"公众号会话下发的文字链",1084:"朋友圈广告原生页",1089:"微信聊天主界面下拉",1090:"长按小程序右上角菜单唤出最近使用历史",1091:"公众号文章商品卡片",1092:"城市服务入口",1095:"小程序广告组件",1096:"聊天记录",1097:"微信支付签约页",1099:"页面内嵌插件",1102:"公众号 profile 页服务预览",1103:"发现栏小程序主入口,'我的小程序'列表",1104:"微信聊天主界面下拉,'我的小程序'栏",1106:"聊天主界面下拉,从顶部搜索结果页,打开小程序",1124:"扫'一物一码'打开小程序",1125:"长按图片识别'一物一码'",1126:"扫描手机相册中选取的'一物一码'",1129:"微信爬虫访问",1131:"浮窗打开小程序"};var globalTitle={};var page_route_map=[];var mpshow_time=null;var is_first_launch=false;zaWeMini.lib_version=LIB_VERSION;(function(){var e=FuncProto.bind,o=ArrayProto.forEach,a=ArrayProto.indexOf,t=Array.isArray,s={};var n=_.each=function(e,t,i){if(e==null){return false}if(o&&e.forEach===o){e.forEach(t,i)}else if(e.length===+e.length){for(var a=0,n=e.length;a<n;a++){if(a in e&&t.call(i,e[a],a,e)===s){return false}}}else{for(var r in e){if(hasOwnProperty.call(e,r)){if(t.call(i,e[r],r,e)===s){return false}}}}};_.logger=logger;_.extend=function(i){n(slice.call(arguments,1),function(e){for(var t in e){if(e[t]!==void 0){i[t]=e[t]}}});return i};_.extend2Lev=function(i){n(slice.call(arguments,1),function(e){for(var t in e){if(e[t]!==void 0){if(_.isObject(e[t])&&_.isObject(i[t])){_.extend(i[t],e[t])}else{i[t]=e[t]}}}});return i};_.coverExtend=function(i){n(slice.call(arguments,1),function(e){for(var t in e){if(e[t]!==void 0&&i[t]===void 0){i[t]=e[t]}}});return i};_.isArray=t||function(e){return toString.call(e)==="[object Array]"};_.isFunction=function(e){try{return/^\s*\bfunction\b/.test(e)}catch(e){return false}};_.isArguments=function(e){return!!(e&&hasOwnProperty.call(e,"callee"))};_.toArray=function(e){if(!e){return[]}if(e.toArray){return e.toArray()}if(_.isArray(e)){return slice.call(e)}if(_.isArguments(e)){return slice.call(e)}return _.values(e)};_.values=function(e){var t=[];if(e==null){return t}n(e,function(e){t[t.length]=e});return t};_.include=function(e,t){var i=false;if(e==null){return i}if(a&&e.indexOf===a){return e.indexOf(t)!=-1}n(e,function(e){if(i||(i=e===t)){return s}});return i}})();_.trim=function(e){return e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")};_.isObject=function(e){if(e===undefined||e===null){return false}else{return toString.call(e)=="[object Object]"}};_.isEmptyObject=function(e){if(_.isObject(e)){for(var t in e){if(hasOwnProperty.call(e,t)){return false}}return true}return false};_.isUndefined=function(e){return e===void 0};_.setDefaultValue=function(e){var t=e;for(var i in t){if(hasOwnProperty.call(t,i)){if(_.isUndefined(t[i])&&i!=="accountId"){t[i]=""}}}return t};_.handleUnfinedValue=function(e){if(typeof e=="undefined"||e===""){return""}else{return e}};_.isString=function(e){return toString.call(e)=="[object String]"};_.isDate=function(e){return toString.call(e)=="[object Date]"};_.isBoolean=function(e){return toString.call(e)=="[object Boolean]"};_.isNumber=function(e){return toString.call(e)=="[object Number]"&&/[\d\.]+/.test(String(e))};_.isJSONString=function(e){try{JSON.parse(e)}catch(e){return false}return true};_.decodeURIComponent=function(t){var i="";try{i=decodeURIComponent(t)}catch(e){i=t}return i};_.encodeDates=function(i){_.each(i,function(e,t){if(_.isDate(e)){i[t]=_.formatDate(e)}else if(_.isObject(e)){i[t]=_.encodeDates(e)}});return i};_.formatDate=function(e,t){function i(e){return e<10?"0"+e:e}return e.getFullYear()+"-"+i(e.getMonth()+1)+"-"+i(e.getDate())+" "+i(e.getHours())+":"+i(e.getMinutes())+":"+i(e.getSeconds())+(t===true?"":"."+i(e.getMilliseconds()))};_.searchObjDate=function(i){if(_.isObject(i)){_.each(i,function(e,t){if(_.isObject(e)){_.searchObjDate(i[t])}else{if(_.isDate(e)){i[t]=_.formatDate(e)}}})}};_.formatString=function(e){if(e.length>zaWeMini.para.max_string_length){logger.info("字符串长度超过限制，已经做截取--"+e);return e.slice(0,zaWeMini.para.max_string_length)}else{return e}};_.searchObjString=function(i){if(_.isObject(i)){_.each(i,function(e,t){if(_.isObject(e)){_.searchObjString(i[t])}else{if(_.isString(e)){i[t]=_.formatString(e)}}})}};_.unique=function(e){var t,i=[],a={};for(var n=0;n<e.length;n++){t=e[n];if(!(t in a)){a[t]=true;i.push(t)}}return i};_.strip_empty_properties=function(e){var i={};_.each(e,function(e,t){if(e!=null){i[t]=e}});return i};_.getCurrentPage=function(){var e={};try{var t=getCurrentPages();e=t[t.length-1]}catch(e){logger.info(e)}return e};_.getCurrentPath=function(){var e="未取到";try{var t=getCurrentPages();var i=t[t.length-1];e=i.route}catch(e){logger.info(e)}return e};_.getPath=function(e){if(typeof e==="string"){e=e.replace(/^\//,"")}else{e="取值异常"}return e};zaWeMini.initialState={queue:[],isComplete:false,systemIsComplete:false,storeIsComplete:false,checkIsComplete:function(){if(this.systemIsComplete&&this.storeIsComplete){this.isComplete=true;if(this.queue.length>0){_.each(this.queue,function(e){zaWeMini[e[0]].apply(zaWeMini,slice.call(e[1]))});this.queue=[]}}}};try{var oldSetNavigationBarTitle=wx.setNavigationBarTitle;Object.defineProperty(wx,"setNavigationBarTitle",{get:function(){return function(e){var t=_.getCurrentPath();e=_.isObject(e)?e:{};globalTitle[t]=e.title;oldSetNavigationBarTitle.call(this,e)}}})}catch(e){logger.info(e)}_.setRefPage=function(){var e={route:"直接打开",title:""};try{var t=_.getCurrentPage();if(t&&t.route){var i=t.route;var a=_.getPageTitle(i);e.route=i;e.title=a;var n=page_route_map.length,r="";if(n>=1){r=page_route_map[n-1].route}if(r!==i){if(n>=2){page_route_map.shift();page_route_map.push(e)}else{page_route_map.push(e)}}}}catch(e){logger.info(e)}};_.getRefPage=function(){var e={route:"直接打开",title:""};if(page_route_map.length>1){e.title=page_route_map[0].title;e.route=page_route_map[0].route}return e};_.setPageRefData=function(e){var t=_.getRefPage();if(_.isObject(e)){e.$referrer=t.route;e.$referrer_title=t.title}};_.getPageTitle=function(i){if(i==="未取到"||!i){return false}var a="";try{if(__wxConfig){var e=__wxConfig;var t=__wxConfig.page||{};var n=t[i]||t[i+".html"];var r={},o={};if(e.global&&e.global.window&&e.global.window.navigationBarTitleText){r.titleVal=e.global.window.navigationBarTitleText}if(n&&n.window&&n.window.navigationBarTitleText){o.titleVal=n.window.navigationBarTitleText}if(!o.titleVal&&__wxAppCode__){var s=__wxAppCode__[i+".json"];if(s&&s["navigationBarTitleText"]){o.titleVal=s["navigationBarTitleText"]}}_.each(globalTitle,function(e,t){if(t===i){return a=e}});if(a.length===0){var u=_.extend(r,o);a=u.titleVal||""}}}catch(e){logger.info(e)}return a};_.getMethods=function(e){var t=[];for(var i in e){if(typeof e[i]==="function"&&!mpHook[i]){t.push(i)}}return t};_.getCustomUtmFromQuery=function(e,t,i,a){if(!_.isObject(e)){return{}}var n={};if(e["sa_utm"]){for(var r in e){if(r==="sa_utm"){n[a+r]=e[r];continue}if(_.include(zaWeMini.para.source_channel,r)){n[i+r]=e[r]}}}else{for(var r in e){if((" "+source_channel_standard+" ").indexOf(" "+r+" ")!==-1){var o={[r]:e[r]};zaWeMini.registerApp(o);n[t+r]=e[r];continue}if(_.include(zaWeMini.para.source_channel,r)){n[i+r]=e[r]}}}return n};_.getObjFromQuery=function(e){var t=e.split("?");var i=[];var a={};if(t&&t[1]){_.each(t[1].split("&"),function(e){var t=e.split("=");if(t[0]&&t[1]){a[t[0]]=t[1]}})}else{return{}}return a};_.setStorageSync=function(e,t){var i=function(){wx.setStorageSync&&wx.setStorageSync(e,t)};try{i()}catch(e){logger.info("set Storage fail --",e);try{i()}catch(e){logger.info("set Storage fail again --",e)}}};_.getStorageSync=function(t){var i="";try{i=wx.getStorageSync&&wx.getStorageSync(t)}catch(e){try{i=wx.getStorageSync&&wx.getStorageSync(t)}catch(e){logger.info("getStorage fail")}}return i};_.getMPScene=function(e){if(typeof e==="number"||typeof e==="string"&&e!==""){e=String(e);return mp_scene[e]||e}else{return undefined}};_.detectOptionQuery=function(e){if(!e||!_.isObject(e.query)){return{}}var t={};t.query=_.extend({},e.query);if(typeof t.query.scene==="string"&&i(t.query)){t.scene=t.query.scene;delete t.query.scene}if(e.query.q&&e.query.scancode_time&&String(e.scene).slice(0,3)==="101"){t.q=String(t.query.q);delete t.query.q;delete t.query.scancode_time}function i(e){var t=["utm_source","utm_content","utm_medium","utm_campaign","utm_term","sa_utm"];var i=t.concat(zaWeMini.para.source_channel);var a=new RegExp("("+i.join("|")+")%3D","i");var n=Object.keys(e);if(n.length===1&&n[0]==="scene"&&a.test(e.scene)){return true}else{return false}}return t};_.getMixedQuery=function(e){var t=_.detectOptionQuery(e);var i=t.scene;var a=t.q;var n=t.query;for(var r in n){n[r]=_.decodeURIComponent(n[r])}if(i){i=_.decodeURIComponent(i);if(i.indexOf("?")!==-1){i="?"+i.replace(/\?/g,"")}else{i="?"+i}_.extend(n,_.getObjFromQuery(i))}if(a){_.extend(n,_.getObjFromQuery(_.decodeURIComponent(a)))}return n};_.setUtm=function(e,t){var i={};var a=_.getMixedQuery(e);var n=_.getCustomUtmFromQuery(a,"$","_","$");var r=_.getCustomUtmFromQuery(a,"$latest_","_latest_","$latest_");i.pre1=n;i.pre2=r;_.extend(t,n);return i};_.wxrequest=function(e){var t=wx.request&&wx.request(e);setTimeout(function(){if(_.isObject(t)&&_.isFunction(t.abort)){t.abort()}},zaWeMini.para.datasend_timeout)};_.info={currentProps:{},properties:{$lib:LIB_NAME,$lib_version:String(LIB_VERSION)},getSystem:function(){var t=this.properties;var e=this;function i(){wx.getNetworkType&&wx.getNetworkType({success:function(e){t.$network_type=e["networkType"]},complete:n})}function a(e){var t=e.toLowerCase();if(t==="ios"){return"iOS"}else if(t==="android"){return"Android"}else{return e}}function n(){wx.getSystemInfo&&wx.getSystemInfo({success:function(e){t.$manufacturer=e["brand"];t.$model=e["model"];t.$screen_width=Number(e["screenWidth"]);t.$screen_height=Number(e["screenHeight"]);t.$os=a(e["platform"]);t.$os_version=e["system"].indexOf(" ")>-1?e["system"].split(" ")[1]:e["system"];t.$wx_version=e["version"];t.$language=e["language"]},complete:function(){zaWeMini.initialState.systemIsComplete=true;zaWeMini.initialState.checkIsComplete()}})}i()}};zaWeMini._=_;zaWeMini.prepareData=function(e,t){var i={distinct_id:this.store.getDistinctId(),properties:{}};_.extend(i,e);if(_.isObject(e.properties)&&!_.isEmptyObject(e.properties)){_.extend(i.properties,e.properties)}if(!e.type||e.type.slice(0,7)!=="profile"){if(zaWeMini.para.batch_send){i._track_id=Number(String(Math.random()).slice(2,5)+String(Math.random()).slice(2,4)+String(Date.now()).slice(-4))}i.properties=_.extend({},_.info.properties,zaWeMini.store.getProps(),_.info.currentProps,i.properties);if(typeof zaWeMini.store._state==="object"&&typeof zaWeMini.store._state.first_visit_day_time==="number"&&zaWeMini.store._state.first_visit_day_time>(new Date).getTime()){i.properties.$is_first_day=true}else{i.properties.$is_first_day=false}}else{i.properties=_.extend({},_.info.properties,zaWeMini.store.getProps(),_.info.currentProps);i.profile_properties=e.properties}_.searchObjDate(i);_.searchObjString(i);zaWeMini.sendStrategy.send(i,t)};zaWeMini.store={verifyDistinctId:function(e){if(typeof e==="number"){e=String(e);if(!/^\d+$/.test(e)){e="unexpected_id"}}if(typeof e!=="string"||e===""){e="unexpected_id"}return e},storageInfo:null,getUUID:function(){return""+Date.now()+"-"+Math.floor(1e7*Math.random())+"-"+Math.random().toString(16).replace(".","")+"-"+String(Math.random()*31242).replace(".","").slice(0,8)},getStorage:function(){if(this.storageInfo){return this.storageInfo}else{this.storageInfo=zaWeMini._.getStorageSync("_za_sdk_wechat")||"";return this.storageInfo}},_state:{},mem:{mdata:[],getLength:function(){return this.mdata.length},add:function(e){this.mdata.push(e)},clear:function(e){this.mdata.splice(0,e)}},toState:function(e){var t=null;if(_.isJSONString(e)){t=JSON.parse(e);if(t.distinct_id){this._state=t;this.set("isnew",false)}else{this.set("distinct_id",this.getUUID());this.set("isnew",true)}}else if(_.isObject(e)){t=e;if(t.distinct_id){this._state=t;this.set("isnew",false)}else{this.set("distinct_id",this.getUUID());this.set("isnew",true)}}else{this.set("isnew",true);this.set("distinct_id",this.getUUID())}},getFirstId:function(){return this._state.first_id},getDistinctId:function(){return this._state.distinct_id},getProps:function(){return this._state.props||{}},setProps:function(e,t){var i=this._state.props||{};if(!t){_.extend(i,e);this.set("props",i)}else{this.set("props",e)}},set:function(e,t){var i={};if(typeof e==="string"){i[e]=t}else if(typeof e==="object"){i=e}this._state=this._state||{};for(var a in i){this._state[a]=i[a]}this.save()},change:function(e,t){this._state[e]=t},save:function(){zaWeMini._.setStorageSync("_za_sdk_wechat",this._state)},init:function(){var e=this.getStorage();if(e){this.toState(e)}else{is_first_launch=true;var t=new Date;var i=t.getTime();t.setHours(23);t.setMinutes(59);t.setSeconds(60);this.set({distinct_id:this.getUUID(),uid:this.getUUID(),first_visit_time:i,first_visit_day_time:t.getTime(),isnew:true,app_name:zaWeMini.para.app_name})}}};zaWeMini.setProfile=function(e,t){if(!_.isObject(e)){return false}zaWeMini.prepareData({type:"profile_set",properties:e},t)};zaWeMini.setOnceProfile=function(e,t){if(!_.isObject(e)){return false}zaWeMini.prepareData({type:"profile_set_once",properties:e},t)};zaWeMini.appendProfile=function(i,e){if(!_.isObject(i)){return false}_.each(i,function(e,t){if(_.isString(e)){i[t]=[e]}else if(_.isArray(e)){}else{delete i[t];logger.info("appendProfile属性的值必须是字符串或者数组")}});zaWeMini.prepareData({type:"profile_append",properties:i},e)};zaWeMini.incrementProfile=function(e,t){if(!_.isObject(e)){return false}var i=e;if(_.isString(e)){e={};e[i]=1}zaWeMini.prepareData({type:"profile_increment",properties:e},t)};zaWeMini.deleteProfile=function(e,t){zaWeMini.prepareData({type:"profile_delete"},t)};zaWeMini.unsetProfile=function(e,t){var i=e;if(_.isString(e)){e=[i]}if(_.isArray(e)){const a={};_.each(e,function(e){if(_.isString(e)){a[e]=true}else{logger.info("profile_unset给的数组里面的值必须时string,已经过滤掉",e)}});e=a}else{logger.info("profile_unset的参数是数组");return false}zaWeMini.prepareData({type:"profile_unset",properties:e},t)};zaWeMini.track=function(e,t,i){this.prepareData({type:"track",event:e,properties:t},i)};zaWeMini.customTrack=function(e,t){e=e||{};var i=_.extend({},e);if(zaWeMini.para&&zaWeMini.para.isPlugin&&zaWeMini.dynamicInfo&&zaWeMini.dynamicInfo.$url_path&&_.getCurrentPath()==="未取到"){e.$url_path=zaWeMini.dynamicInfo.$url_path}else{e.$url_path=_.getCurrentPath()}this.prepareData({type:"track",event:"$MPCustom",properties:e,originalProperties:i},t)};zaWeMini.triggerABTest=function(e,t,i,a,n){a=a||{};var r=_.extend({},a);if(zaWeMini.para&&zaWeMini.para.isPlugin&&zaWeMini.dynamicInfo&&zaWeMini.dynamicInfo.$url_path&&_.getCurrentPath()==="未取到"){a.$url_path=zaWeMini.dynamicInfo.$url_path}else{a.$url_path=_.getCurrentPath()}r.experiment_code=e;r.ABTVersion=t;r.control_content=i;this.prepareData({type:"track",event:"$MPABTest",properties:a,originalProperties:r},n)};zaWeMini.fetchABTestWithCode=function(t,e){try{if(t){const i=zaWeMini.abTestData||[];if(_.isArray(i)){const a=i.find(e=>{return e.ec===t});if(e&&a){zaWeMini.triggerABTest(a.ec,a.esc,"",{})}return a}return null}return null}catch(e){return null}};zaWeMini.fetchABTestWithParamName=function(t,e){try{if(t){const i=zaWeMini.abTestData||[];if(_.isArray(i)){const a=i.find(e=>{if(_.isArray(e.paramList)){return e.paramList.find(e=>e.name===t)}return false});if(e&&a){zaWeMini.triggerABTest(a.ec,a.esc,"",{})}return a}return null}return null}catch(e){return null}};zaWeMini.pageTrack=function(e,t){e=e||{};if(zaWeMini.para&&zaWeMini.para.isPlugin&&e.$url_path){zaWeMini.dynamicInfo=zaWeMini.dynamicInfo||{};zaWeMini.dynamicInfo.$url_path=e.$url_path}else{e.$url_path=_.getCurrentPath()}this.prepareData({type:"track",event:"MPViewScreen",properties:e},t)};zaWeMini.setLogin=function(e,t){if(typeof e!=="string"&&typeof e!=="number"||!e)return;zaWeMini.registerApp&&zaWeMini.registerApp({accountId:e});this.prepareData({type:"track",event:"$MPLogin",properties:{accountId:e}},t)};zaWeMini.getSDKData=function(){function e(e,t){if(!e)return"";return e+"="+t+";"}var t=";";var i=zaWeMini.store.getStorage&&zaWeMini.store.getStorage().openid;t=t+e("_xflow_traceid",_.handleUnfinedValue(_.info&&_.info.currentProps&&_.info.currentProps.trace_id))+e("_xflow_session_id",_.handleUnfinedValue(_.info&&_.info.currentProps&&_.info.currentProps.session_id))+e("_xflow_session_time",_.handleUnfinedValue(_.info&&_.info.currentProps&&_.info.currentProps.session_time))+e("_xflow_uid",_.handleUnfinedValue(zaWeMini.store.getStorage&&zaWeMini.store.getStorage().uid))+(i?e("_xflow_openid",_.handleUnfinedValue(i)):"");return t};zaWeMini.registerApp=function(e){if(_.isObject(e)&&!_.isEmptyObject(e)){_.info.currentProps=_.extend(_.info.currentProps,e)}};zaWeMini.register=function(e){if(_.isObject(e)&&!_.isEmptyObject(e)){zaWeMini.store.setProps(e)}};zaWeMini.clearAllRegister=function(){zaWeMini.store.setProps({},true)};zaWeMini.clearAllProps=function(i){var e=zaWeMini.store.getProps();var a={};if(_.isArray(i)){_.each(e,function(e,t){if(!_.include(i,t)){a[t]=e}});zaWeMini.store.setProps(a,true)}};zaWeMini.clearAppRegister=function(i){if(_.isArray(i)){_.each(_.info.currentProps,function(e,t){if(_.include(i,t)){delete _.info.currentProps[t]}})}};zaWeMini.setLatestChannel=function(e){if(!_.isEmptyObject(e)){if(t(e,latest_source_channel)){zaWeMini.clearAppRegister(latest_source_channel);zaWeMini.clearAllProps(latest_source_channel)}zaWeMini.para.is_persistent_save?zaWeMini.register(e):zaWeMini.registerApp(e)}function t(e,t){var i=false;for(var a in t){if(e[t[a]]){i=true}}return i}};zaWeMini.initial=function(){this._.info.getSystem();this.store.init()};zaWeMini.init=function(e){if(this.hasInit===true){return false}zaWeMini.registerApp&&zaWeMini.registerApp({session_id:zaWeMini.store&&zaWeMini.store.getUUID&&zaWeMini.store.getUUID(),session_time:_.formatDate(new Date,true),trace_id:zaWeMini.store&&zaWeMini.store.getUUID&&zaWeMini.store.getUUID()});this.hasInit=true;zaWeMini.setPara(e);if(zaWeMini.para.batch_send){wx.getStorage({key:"za_mp_prepare_data",complete:function(e){var t=e.data&&_.isArray(e.data)?e.data:[];zaWeMini.store.mem.mdata=t.concat(zaWeMini.store.mem.mdata);zaWeMini.sendStrategy.syncStorage=true}});zaWeMini.sendStrategy.batchInterval()}if(zaWeMini.para.ABTestUrl){const t=zaWeMini.store.getStorage().openid||zaWeMini.store.getStorage().uid;const i={};if(zaWeMini.para.tenantCode){i["webgw-tenant-id"]=zaWeMini.para.tenantCode}_.wxrequest({url:zaWeMini.para.ABTestUrl+"?sf="+t,method:"GET",header:i,success:function(e){if(e&&e.data&&e.data.status===200){zaWeMini.abTestData=e.data.data||[]}}})}zaWeMini.initialState.storeIsComplete=true;zaWeMini.initialState.checkIsComplete()};function omitEmptyValue(e){var t={};for(var i in e){if(hasOwnProperty.call(e,i)&&e[i]){t[i]=e[i]}}return t}zaWeMini.dataToWebview=function(e){if(!this.hasInit){logger.info("SDK needs to be initialized first, please use this method after zaWeMini.init();");return""}try{var t=_.info&&_.info.currentProps||{};var i=zaWeMini.store.getStorage&&zaWeMini.store.getStorage()||{};var a=_.extend({uid:i.uid,trace_id:t.trace_id,session_id:t.session_id,session_time:t.session_time,open_id:i.openid,union_id:i.unionId||t.$unionId},e===true?omitEmptyValue(_.extend({sdk_type:"xcx",xcx_sdk_source:"wechat",web_handle_xcx:1,accountId:t.accountId},getUTMMixObj(t))):{});var n="xflow_d_t_wv="+encodeURIComponent(JSON.stringify(a));return n}catch(e){}return""};zaWeMini.getPresetProperties=function(){if(_.info&&_.info.properties&&_.info.properties.$lib){var i={};_.each(_.info.currentProps,function(e,t){if(t.indexOf("$")===0){i[t]=e}});var e=_.extend(i,{$url_path:_.getCurrentPath()},_.info.properties,zaWeMini.store.getProps());delete e.$lib;return e}else{return{}}};function getUTMMixObj(e){if(!e)return{};var t={};try{var i=source_channel_standard.split(" ");for(var a=0;a<i.length;a++){var n=i[a];if(n&&e[n]){t[n]=e[n]}}}catch(e){}return t}function getExtendsInfo(e){try{var t={};var i=_.extend({},zaWeMini.para.extendsInfo||{});var a={};if(typeof i.getDynamic==="function"){try{a=i.getDynamic(e)}catch(e){}}if(typeof a==="object"){i={...i,...a}}for(var n in i){if(hasOwnProperty.call(i,n)){if(typeof i[n]!=="function"){t[n]=i[n]}else if(typeof i[n]==="function"&&n!=="getDynamic"){try{t[n]=i[n]()}catch(e){logger.info("动态配置项执行错误")}}}}return t}catch(e){logger.info("call getExtendsInfo error",e);return{}}}zaWeMini.sendStrategy={dataHasSend:true,dataHasChange:false,syncStorage:false,failTime:0,onAppHide:function(){if(zaWeMini.para.batch_send){this.batchSend()}},send:function(e,t){if(!zaWeMini.para.server_url){return false}const i=this.transformData(e);if(zaWeMini.para.noLog===true)return;if(zaWeMini.para.batch_send){this.dataHasChange=true;if(zaWeMini.store.mem.getLength()>=500){logger.info("数据量存储过大，有异常");zaWeMini.store.mem.mdata.shift()}zaWeMini.store.mem.add(i);if(zaWeMini.store.mem.getLength()>=zaWeMini.para.batch_send.max_length){this.batchSend()}}else{this.sendData(i,t)}zaWeMini.store.set("isnew",false);zaWeMini.store.storageInfo.isnew=false},transformData:function(e){const t=!e.type||e.type.slice(0,7)!=="profile";if(t){e=this.conversionData(e)}else{e=this.conversionProfileData(e)}if(e&&e.event_name===undefined&&zaWeMini.para.isPlugin)return;if(t){try{e=_.extend({type:"event",event_name:e.event_name||e.type,source_id:e.source_id,sdk_type:"xcx",xcx_sdk_source:"wechat",trace_id:e.trace_id,debug:zaWeMini.para.debug?"true":undefined},_.setDefaultValue(e.common),e.infos,e.extendsInfo)}catch(e){}}if(zaWeMini.para.debug){logger.info("open_id",e.open_id);logger.info(e)}return e},sendData:function(e,t){if(zaWeMini.para.noLog===true)return;e=JSON.stringify(e);_.wxrequest({url:zaWeMini.para.server_url,method:"POST",data:encodeURIComponent(e),success:function(e){},complete:function(){typeof t==="function"&&t()}})},wxrequest:function(e){if(_.isArray(e.data)&&e.data.length>0){var t=Date.now();e.data.forEach(function(e){e._flush_time=t});e.data=JSON.stringify(e.data);_.wxrequest({url:zaWeMini.para.server_url,method:"POST",dataType:"text",data:encodeURIComponent(e.data),success:function(){e.success(e.len)},fail:function(){e.fail()}})}else{e.success(e.len)}},batchSend:function(){if(this.dataHasSend){var e,t,i=zaWeMini.store.mem.mdata;if(i.length>=100){e=i.slice(0,100)}else{e=i}t=e.length;if(t>0){this.dataHasSend=false;if(zaWeMini.para.noLog===true)return;this.wxrequest({data:e,len:t,success:this.batchRemove.bind(this),fail:this.sendFail.bind(this)})}}},sendFail:function(){this.dataHasSend=true;this.failTime++},batchRemove:function(e){zaWeMini.store.mem.clear(e);this.dataHasSend=true;this.dataHasChange=true;this.batchWrite();this.failTime=0},is_first_batch_write:true,batchWrite:function(){var e=this;if(this.dataHasChange){if(this.is_first_batch_write){this.is_first_batch_write=false;setTimeout(function(){e.batchSend()},1e3)}this.dataHasChange=false;if(this.syncStorage){zaWeMini._.setStorageSync("za_mp_prepare_data",zaWeMini.store.mem.mdata)}}},batchInterval:function(){var e=this;function t(){setTimeout(function(){e.batchWrite();t()},500)}function i(){setTimeout(function(){e.batchSend();i()},zaWeMini.para.batch_send.send_timeout*Math.pow(2,e.failTime))}t();i()},conversionData:function(e){var t=e.properties;var i=this.conversionType(e.event);var a=i==="custom"?_.handleUnfinedValue(t&&t.event_value)||"custom":i;var n=getExtendsInfo(a);var r=getUTMMixObj(t);var o={type:"event",event_name:a,source_id:zaWeMini.para.source_id,trace_id:t.trace_id,common:_.extend({resolution:t.$screen_width+"x"+t.$screen_height,device_model:t.$model,network:t.$network_type,event_time:_.formatDate(new Date,true),event_timestamp:Date.now(),sdk_version:t.$lib_version,language:t.$language,wx_xcx_version:t.$wx_version,wx_xcx_id:zaWeMini.para.appid,wx_xcx_name:zaWeMini.para.app_name,url:t.$url_path,url_param:t.$url_query||"",title:t.$title||"",refer_url:t.$referrer||"",referrer_title:t.$referrer_title||"",open_id:zaWeMini.store.getStorage().openid,uid:zaWeMini.store.getStorage().uid,session_id:t.session_id,session_time:t.session_time,channel:t.$latest_scene,channel_id:t.$latest_scene_id,device_id:zaWeMini.store.getStorage().openid||zaWeMini.store.getStorage().uid,device_brand:t.$manufacturer,os_type:t.$os,platform_os_version:t.$os_version,accountId:t.accountId||undefined,union_id:zaWeMini.store.getStorage().unionId||t.$unionId},r),infos:this.setInfoByType(t,i,e),extendsInfo:n||{}};return o},conversionProfileData:function(e){var t=e.properties;var i={type:"profile",source_id:zaWeMini.para.source_id,event_name:"modify_profile",profile_name:e.type,event_time:_.formatDate(new Date,true),event_timestamp:Date.now(),sdk_type:"xcx",xcx_sdk_source:"wechat",sdk_version:t.$lib_version,accountId:t.accountId||undefined,device_id:zaWeMini.store.getStorage().openid||zaWeMini.store.getStorage().uid,open_id:zaWeMini.store.getStorage().openid,union_id:zaWeMini.store.getStorage().unionId||t.$unionId,uid:zaWeMini.store.getStorage().uid,properties:e.profile_properties,debug:zaWeMini.para.debug?"true":undefined};return i},conversionType:function(e){return zaWeMini._proxy.type[e]!=undefined?zaWeMini._proxy.type[e]:zaWeMini.para.isPlugin?undefined:"page"},setInfoByType:function(e,t,i){var a={};try{if(t==="share"){return{share_title:_.handleUnfinedValue(e.shareTitle||e.$share_title),description:_.handleUnfinedValue(e.shareDesc||e.$share_description),share_from:_.handleUnfinedValue(e.$from),share_method:_.handleUnfinedValue(e.$share_method)}}if(t==="click"){var n=e.datasets||{};var r={};_.each(Object.keys(n),function(e){if(e&&n[e]){r[e]=_.handleUnfinedValue(n[e])}});return _.extend({element_content:_.handleUnfinedValue(e.$element_content),element_type:_.handleUnfinedValue(e.$element_type),click_id:_.handleUnfinedValue(n.id)},r)}if(t==="custom"){var o=i&&i.originalProperties||{};return _.extend({custom_event_type:_.handleUnfinedValue(e.event_type),custom_event_value:_.handleUnfinedValue(e.event_value),custom_event_description:_.handleUnfinedValue(e.event_description)},o||{})}if(t==="ABTestTrigger"){var o=i&&i.originalProperties||{};return o}return a}catch(e){return a}}};zaWeMini.setOpenid=function(e){zaWeMini.store.set("openid",e);if(zaWeMini.store&&zaWeMini.store.storageInfo){zaWeMini.store.storageInfo.openid=e}};zaWeMini.setUnionId=function(e){zaWeMini.store.set("unionId",e);if(zaWeMini.store&&zaWeMini.store.storageInfo){zaWeMini.store.storageInfo.unionId=e}zaWeMini.registerApp({$unionId:e||undefined})};_.each(["setProfile","setOnceProfile","track","incrementProfile","appendProfile","deleteProfile","unsetProfile"],function(e){var t=zaWeMini[e];zaWeMini[e]=function(){if(zaWeMini.initialState.isComplete){t.apply(zaWeMini,arguments)}else{zaWeMini.initialState.queue.push([e,arguments])}}});_.setQuery=function(e,i){var t="";if(e&&_.isObject(e)&&!_.isEmptyObject(e)){var a=[];_.each(e,function(e,t){if(!(t==="q"&&_.isString(e)&&e.indexOf("http")===0)&&t!=="scene"&&t!=="__key_"){if(i){a.push(t+"="+e)}else{a.push(t+"="+_.decodeURIComponent(e))}}});return a.join("&")}else{return t}};_.getUtmFromPage=function(){var e={};try{var t=getCurrentPages();var i=t[t.length-1].options;e=_.getCustomUtmFromQuery(i,"$","_","$")}catch(e){logger.info(e)}return e};function mp_proxy(e,t,i){var a=zaWeMini.autoTrackCustom[i];if(e[t]){var n=e[t];e[t]=function(){if(t==="onLaunch"){this[zaWeMini.para.name]=zaWeMini}if(!zaWeMini.para.autoTrackIsFirst||_.isObject(zaWeMini.para.autoTrackIsFirst)&&!zaWeMini.para.autoTrackIsFirst[i]){n.apply(this,arguments);a.apply(this,arguments)}else if(zaWeMini.para.autoTrackIsFirst===true||_.isObject(zaWeMini.para.autoTrackIsFirst)&&zaWeMini.para.autoTrackIsFirst[i]){a.apply(this,arguments);n.apply(this,arguments)}}}else{e[t]=function(){if(t==="onLaunch"){this[zaWeMini.para.name]=zaWeMini}a.apply(this,arguments)}}}function clickTrack(e){try{var t=e.currentTarget;var i=e.type;if(t&&zaWeMini._proxy.clickEventTypes.indexOf(i)!=-1){var a=zaWeMini.para.autoTrack["mpClick"];if(typeof a==="function"&&a(arguments[0])===false){return false}var n={};n.datasets=t&&t.dataset;if(zaWeMini.para&&zaWeMini.para.isPlugin&&zaWeMini.dynamicInfo&&zaWeMini.dynamicInfo.$url_path&&_.getCurrentPath()==="未取到"){n.$url_path=zaWeMini.dynamicInfo.$url_path}else{n.$url_path=_.getCurrentPath()}if(zaWeMini.para&&zaWeMini.para.adaptIlog4){adaptIlog4(zaWeMini.para,true,n.datasets)}zaWeMini.track("$MPClick",n)}}catch(e){}}function click_proxy(e,t){var i=e[t];e[t]=function(){var e=i.apply(this,arguments);var t=arguments[0];if(_.isObject(t)){clickTrack(t)}return e}}function tabProxy(e){var i=e["onTabItemTap"];e["onTabItemTap"]=function(e){if(i){i.apply(this,arguments)}var t={};if(e){t["$element_content"]=e.text}t["$element_type"]="tabBar";t["$url_path"]=_.getCurrentPath();zaWeMini.autoTrackCustom.trackCustom("mpClick",t,"$MPClick")}}zaWeMini.autoTrackCustom={trackCustom:function(e,t,i){var a=zaWeMini.para.autoTrack[e];var n="";if(zaWeMini.para.autoTrack&&a){if(typeof a==="function"){n=a();if(n===false){return}if(_.isObject(n)){_.extend(t,n)}}else if(_.isObject(a)){_.extend(t,a);zaWeMini.para.autoTrack[e]=true}_.setPageRefData(t);zaWeMini.track(i,t)}},appLaunch:function(e,t){if(typeof this==="object"&&!this["trackCustom"]){this[zaWeMini.para.name]=zaWeMini}var i={};if(e&&e.path){i.$url_path=_.getPath(e.path)}var a=_.setUtm(e,i);zaWeMini.setLatestChannel(a.pre2);e.scene=e.scene||"未取到值";i.$scene=_.getMPScene(e.scene);i.$scene_id=e.scene;zaWeMini.registerApp({$latest_scene:i.$scene,$latest_scene_id:i.$scene_id,$url_query:_.setQuery(e.query)});if(t){i=_.extend(i,t);zaWeMini.track("$MPLaunch",i)}else if(zaWeMini.para.autoTrack&&zaWeMini.para.autoTrack.appLaunch){zaWeMini.autoTrackCustom.trackCustom("appLaunch",i,"$MPLaunch")}},appShow:function(e,t){var i={};mpshow_time=(new Date).getTime();if(e&&e.path){i.$url_path=_.getPath(e.path)}var a=_.setUtm(e,i);zaWeMini.setLatestChannel(a.pre2);e.scene=e.scene||"未取到值";i.$scene=_.getMPScene(e.scene);zaWeMini.registerApp({$latest_scene:i.$scene,$url_query:_.setQuery(e.query)});if(t){i=_.extend(i,t);zaWeMini.track("$MPShow",i)}else if(zaWeMini.para.autoTrack&&zaWeMini.para.autoTrack.appShow){zaWeMini.autoTrackCustom.trackCustom("appShow",i,"$MPShow")}},appHide:function(e){var t=(new Date).getTime();var i={};i.$url_path=_.getCurrentPath();if(mpshow_time&&t-mpshow_time>0&&(t-mpshow_time)/36e5<24){i.event_duration=(t-mpshow_time)/1e3}if(e){i=_.extend(i,e);zaWeMini.track("$MPHide",i)}else if(zaWeMini.para.autoTrack&&zaWeMini.para.autoTrack.appHide){zaWeMini.autoTrackCustom.trackCustom("appHide",i,"$MPHide")}zaWeMini.sendStrategy.onAppHide()},pageLoad:function(e){if(e&&_.isObject(e)){this.za_mp_url_query=_.setQuery(e)}},pageShow:function(){var e={};var t=_.getCurrentPath();var i=_.getPageTitle(t);_.setRefPage();e.$url_path=t;var a={$url_query:this.za_mp_url_query?this.za_mp_url_query:"",$title:i};_.setPageRefData(a);zaWeMini.registerApp(a);e=_.extend(e,_.getUtmFromPage());if(zaWeMini.para.onshow){zaWeMini.para.onshow(zaWeMini,t,this)}else if(zaWeMini.para.autoTrack&&zaWeMini.para.autoTrack.pageShow){zaWeMini.autoTrackCustom.trackCustom("pageShow",e,"$MPViewScreen")}},pageShare:function(e){var n=e.onShareAppMessage;e.onShareAppMessage=function(e,t,i){var a=n.apply(this,arguments);if(zaWeMini.para.autoTrack&&zaWeMini.para.autoTrack.pageShare){zaWeMini.autoTrackCustom.trackCustom("pageShare",{$url_path:_.getCurrentPath(),$share_title:a&&a.title||undefined,$share_description:a&&a.description||undefined,$from:e.from,$target:e.target,$share_method:"转发消息卡片"},"$MPShare")}return a}},pageShareTimeline:function(e){var n=e.onShareTimeline;e.onShareTimeline=function(e,t,i){var a=n.apply(this,arguments);if(zaWeMini.para.autoTrack&&zaWeMini.para.autoTrack.pageShare){zaWeMini.autoTrackCustom.trackCustom("pageShare",{$url_path:_.getCurrentPath(),$share_title:a&&a.title||undefined,$share_description:a&&a.description||undefined,$from:e.from,$target:e.target,$share_method:"朋友圈分享"},"$MPShare")}return a}},pageAddFavorites:function(){var e={};e.$url_path=_.getCurrentPath();if(zaWeMini.para.autoTrack&&zaWeMini.para.autoTrack.mpFavorite){zaWeMini.autoTrackCustom.trackCustom("mpFavorite",e,"$MPAddFavorites")}}};var oldApp=App;App=function(e){mp_proxy(e,"onLaunch","appLaunch");mp_proxy(e,"onShow","appShow");mp_proxy(e,"onHide","appHide");oldApp.apply(this,arguments);if(zaWeMini.para&&zaWeMini.para.adaptIlog4){adaptIlog4(zaWeMini.para)}};var oldPage=Page;Page=function(e){var t=zaWeMini.para.autoTrack&&zaWeMini.para.autoTrack.mpClick&&_.getMethods(e);if(t){for(var i=0,a=t.length;i<a;i++){click_proxy(e,t[i])}}if(zaWeMini.para.autoTrack&&zaWeMini.para.autoTrack.mpClick){tabProxy(e)}mp_proxy(e,"onLoad","pageLoad");mp_proxy(e,"onShow","pageShow");mp_proxy(e,"onAddToFavorites","pageAddFavorites");if(typeof e.onShareAppMessage==="function"){zaWeMini.autoTrackCustom.pageShare(e)}if(typeof e.onShareTimeline==="function"){zaWeMini.autoTrackCustom.pageShareTimeline(e)}oldPage.apply(this,arguments)};var oldComponent=Component;Component=function(e){try{var t=zaWeMini.para.autoTrack&&zaWeMini.para.autoTrack.mpClick&&_.getMethods(e.methods);if(t){for(var i=0,a=t.length;i<a;i++){click_proxy(e.methods,t[i])}}if(zaWeMini.para.autoTrack&&zaWeMini.para.autoTrack.mpClick){tabProxy(e.methods)}mp_proxy(e.methods,"onLoad","pageLoad");mp_proxy(e.methods,"onShow","pageShow");mp_proxy(e,"onAddToFavorites","pageAddFavorites");if(typeof e.methods.onShareAppMessage==="function"){zaWeMini.autoTrackCustom.pageShare(e.methods)}if(typeof e.methods.onShareTimeline==="function"){zaWeMini.autoTrackCustom.pageShareTimeline(e.methods)}oldComponent.apply(this,arguments)}catch(e){oldComponent.apply(this,arguments)}};zaWeMini.initial();module.exports=zaWeMini;