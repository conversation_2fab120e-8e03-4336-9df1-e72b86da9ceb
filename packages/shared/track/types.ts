import { EVENT_TYPE, EVENT_VALUE, EVENT_COMMON_PROPERTY } from './const';

export type EventType =
  | typeof EVENT_TYPE[keyof typeof EVENT_TYPE]
  | (string & {});

export type EventValue = typeof EVENT_VALUE[keyof typeof EVENT_VALUE];

export type EventCommonProperty =
  typeof EVENT_COMMON_PROPERTY[keyof typeof EVENT_COMMON_PROPERTY];

export type EventCommonPropertyParams = {
  [P in EventCommonProperty]?: any;
};

export interface EventExtendsParams {
  /* eslint-disable-next-line camelcase */
  extra_infomation?: any;
  [key: string]: any;
}

export type EventParams = EventCommonPropertyParams & EventExtendsParams;
