/* eslint-disable camelcase */
import { EventValue, EventParams, EventType, EventCommonProperty } from './types';
import { EVENT_TYPE, EVENT_VALUE_DESCRIPTION_MAP, EVENT_COMMON_PROPERTY } from './const';

import za from './za';

const isObject = (obj: any): obj is object => {
  return Object.prototype.toString.call(obj) === '[object Object]';
};

export const push = <T extends EventValue>(eventType: EventType, eventValue: T, params?: EventParams, callback?: Function) => {
  za?.customTrack(
    {
      event_type: eventType,
      event_value: eventValue,
      event_description: EVENT_VALUE_DESCRIPTION_MAP[eventValue],
      ...params,
    },
    callback,
  );
};

export const preset = (eventType: EventType, eventValue: EventValue, eventCommonProperty?: EventCommonProperty) => {
  return (value: any, params?: EventParams, callback?: Function) => {
    // 兼容legency
    /**
     * track.click({})
     */
    console.warn(value, params);
    if (eventType === EVENT_TYPE.CLICK && isObject(value)) {
      return push(eventType, eventValue, { ...value, ...params }, callback);
    }
    if (eventCommonProperty && params) {
      params[eventCommonProperty] = value;
    }
    return push(eventType, eventValue, params ?? { [EVENT_COMMON_PROPERTY.CLICK]: value }, callback);
  };
};

export const setOpenid = (openId: string) => {
  za.setOpenid(openId);
};

export const setUnionId = (unionId: string) => {
  za.setUnionId(unionId);
};

export const init = () => {
  za.init();
};

export const setPara = (para: {
  source_id?: string;
  app_name?: string;
  appid?: string;
  name?: string;
  extendsInfo?: {
    ENV?: string;
    [key: string]: any;
  };
  server_url?: string;
}) => {
  za.setPara(para);
};
