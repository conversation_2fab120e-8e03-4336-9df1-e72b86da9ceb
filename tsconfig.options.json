{"compilerOptions": {"composite": true, "target": "es2017", "module": "commonjs", "moduleResolution": "node", "removeComments": false, "preserveConstEnums": true, "experimentalDecorators": true, "noImplicitAny": false, "allowSyntheticDefaultImports": true, "noUnusedLocals": true, "noUnusedParameters": true, "strictNullChecks": true, "skipLibCheck": true, "sourceMap": true, "allowJs": true, "resolveJsonModule": true, "jsx": "react", "jsxFactory": "React.createElement", "jsxFragmentFactory": "React.Fragment", "declaration": true, "declarationMap": true, "emitDeclarationOnly": true, "incremental": true, "noEmit": false, "noEmitOnError": true}}