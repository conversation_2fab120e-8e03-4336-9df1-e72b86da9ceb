{"name": "za-asclepius-applet", "version": "1.0.0", "private": true, "description": "互联网医院小程序", "templateInfo": {"name": "default", "typescript": true, "css": "sass"}, "scripts": {"preinstall": "npx only-allow pnpm", "build": "pnpm --filter hy build:weapp", "dev": "pnpm --filter hy dev:weapp", "lint": "pnpm run lint:script && npm run lint:style", "test": "jest --config jest.config.js --no-cache", "prepare": "husky install", "lint:script": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:style": "stylelint \"**/*.scss\""}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "peerDependencies": {"postcss": "^8.4.31"}, "engines": {"node": ">=16.0.0"}, "devDependencies": {"@commitlint/cli": "^17.3.0", "@commitlint/config-conventional": "^17.3.0", "@typescript-eslint/eslint-plugin": "^5.61.0", "@typescript-eslint/parser": "^5.61.0", "babel-eslint": "^11.0.0-beta.2", "eslint": "7.10.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-config-za": "^2.4.0-alpha.5", "eslint-plugin-import": "2.22.1", "eslint-plugin-jsx-a11y": "6.4.1", "eslint-plugin-react": "7.21.5", "eslint-plugin-react-hooks": "4.1.2", "eslint-plugin-taro": "^3.3.20", "husky": "^8.0.0", "jest": "^27.3.1", "lint-staged": "^10.5.3", "postcss-jsx": "^0.36.4", "source-map-loader": "^4.0.1", "stylelint": "^15.11.0", "stylelint-config-recommended-scss": "^13.1.0", "ts-jest": "^27.0.7", "typescript": "5.1.6"}, "staticResolve": {"projectId": "5f2a5e5bec449b001de90834", "token": "Bearer IqMKIqiAcIOWNUs9MJZta04KhsT6qghq", "cssPath": "src/pages/**/*(*.module.scss)", "staticPath": "src/static"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": "eslint", "*.scss": "stylelint"}, "volta": {"node": "22.14.0"}}