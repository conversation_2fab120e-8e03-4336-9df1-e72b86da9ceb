## 迁移需求

za-asclepius-applet 这个项目是 wepy 搭建的项目, 目前我需要重构成 Taro 项目 。
applet-taro 这个项目是使用 Taro 搭建的项目用于重构 za-asclepius-applet 的项目, 目前结构已经搭建完成,后续需要你和我一起来完成重构。

### **Wepy 项目迁移到 Taro 实战指南 (函数组件版)**

#### **1. 入口与全局配置**
##### **1.1 入口文件迁移**
- **Wepy**: `src/app.wpy`  
- **Taro**: `src/app.tsx` (推荐函数组件)  

**迁移方式**:
- Wepy 的 `config` 字段内容 → 迁移到 `src/app.config.ts`。  
- 生命周期方法（如 `onLaunch`, `onShow`）→ 替换为 Taro 的 Hooks（如 `useLaunch`, `useDidShow`）。

##### **1.2 配置文件迁移**
- 直接复制 `config` 到 `app.config.ts`，注意路径大小写。

---

#### **2. API 替换**
##### **2.1 `wx` → `Taro`**
- 全局搜索 `wx.`，替换为 `Taro.`。  
- 文件顶部添加：  
  ```typescript
  import Taro from '@tarojs/taro';
  ```

##### **2.2 Wepy API 替换**
- `wepy.showToast` → `Taro.showToast`  
- `wepy.request` → `Taro.request`  
- `wepy.navigateTo` → `Taro.navigateTo`  

---

#### **3. 全局变量与状态管理**
##### **3.1 避免挂载到 `wx`**
- **Wepy 写法**：  
  ```javascript
  wx.$store = store;
  ```
- **Taro 推荐**：  
  - 使用 `Redux`/`MobX`/`Context`。  
  - 直接 `import` 工具模块。

##### **3.2 Redux 用法**
- 在根组件中包裹 `Provider`：  
  ```typescript
  <Provider store={store}>
    {/* 子组件 */}
  </Provider>
  ```
- 使用 `useSelector` 和 `useDispatch`：  
  ```typescript
  const state = useSelector((s) => s);
  const dispatch = useDispatch();
  ```

---

#### **4. 组件与页面迁移**
##### **4.1 文件结构**
- `.wpy` → 拆分为 `.tsx` (逻辑) + `.scss` (样式)。  

##### **4.2 模板语法迁移**
- `{{xxx}}` → `{xxx}`  
- `wx:if` → `{condition && ...}`  
- `wx:for` → `{array.map(item => ...)}`  
- `@tap` → `onClick`  

##### **4.3 组件导出**
- **Wepy**:  
  ```javascript
  export default class extends wepy.component {}
  ```
- **Taro (函数组件)**:  
  ```typescript
  export default function Component() {}
  ```

---

#### **5. 生命周期迁移 (函数组件)**
| Wepy/小程序       | Taro (函数组件)          | 说明                     |
|-------------------|-------------------------|--------------------------|
| `onLaunch`        | `useLaunch`             | 小程序初始化             |
| `onShow`          | `useDidShow`            | 页面显示                 |
| `onHide`          | `useDidHide`            | 页面隐藏                 |
| `onLoad`          | `useEffect` (依赖 `[]`) | 组件挂载                 |
| `onUnload`        | `useEffect` 清理函数     | 组件卸载                 |

**示例**:
```typescript
useLaunch(() => {
  console.log('App launched');
});

useDidShow(() => {
  console.log('Page shown');
});
```

---

#### **6. 样式与资源**
- `<style lang="less">` → 迁移到 `.scss` 文件。  
- 资源路径使用 `@/` 别名或绝对路径。

---

#### **7. 分包配置**
- `subPackages` 配置直接复制到 `app.config.ts`。

---

#### **8. 插件与第三方库**
- 删除 Wepy 插件（如 `this.use('requestfix')`）。  
- 第三方 SDK 需检查兼容性。

---

#### **9. 事件与方法**
- `$emit`/`$invoke` → 使用 `props` 或 `Context`。  
- `$apply` → 直接调用 `setState` 或 Hooks。

---

#### **10. 迁移步骤建议**
1. **初始化 Taro 项目**，迁移全局配置。  
2. **替换 API**：`wx` → `Taro`。  
3. **逐页迁移**：模板改 JSX，逻辑用函数组件。  
4. **状态管理**：迁移到 `Redux`/`Context`。  
5. **测试修复**：逐步验证功能。

---

### **注意事项**
- 使用 `Taro 4.x` 及以上版本。  
- 函数组件需返回有效的 JSX（如 `null` 或实际内容）。  
- 复杂逻辑优先使用 `useEffect` 和 `useMemo` 优化。
- 如果碰到 StaticToast 可以使用 Taro.showToast 来替代
- 碰到使用 wx.$webviewUrl wx.$mallWebviewUrl 可以只用使用 import {webviewUrl , mallWebviewUrl} from '@/utils/config'
- 碰到使用 wepy.$instance.globalData.webviewUrl 可以替换成 import {globalData} from '@/utils/global' 使用 globalData.webviewUrl


## 目前进展

- utils 目录已经整体迁移过去 
- images 目录已经整体迁移过去 
- 入口组件已经完成 
- 

