# 小程序说明

## 环境说明
小程序提供 4 套环境选择 `test` `pre` `uat` `prd`.
`uat` 等同与 `prd`，用于环境配置同步。

## lock 文件说明
为了多人开发时的依赖一致性，依赖统一使用 `yarn` 管理。

## pocket 配置说明

```json
{
  // @废弃 请用 bops 中的广告位代替
  // @提醒 配置可移除
  "banner": [
    {
      "color": "#D85A46",
      "img": "https://xxx",
      "urlType": "MiniProgramPage",
      "url": "/pages/xman/native/index?xCode=andb9819"
    }
  ],
  "home_activity": [
    {
      "img": "https://xxx",
      "urlType": "MiniProgramPage",
      "url": "/pages/xman/native/index?xCode=andb9819"
    }
  ],
  // 热卖产品
  "home_top_product": [
    {
      "color": "#20BA9D",
      "img": "https://xxx",
      "url": "https://xxx"
    }
  ],
  // 主推产品，大家都在买
  // @废弃 请用 bops 中的广告位代替
  // @提醒 配置可移除
  "home_main_product": [
    {
      "color": "#20BA9D",
      "img": "https://xxx",
      "url": "https://xxx"
    }
  ],
  // 产品列表扩展信息
  "home_product_extension": {
    "links": [
      {
        "text": "保险如何保护我们？",
        "url": "https://xxx"
      },
      {
        "text": "保险如何保护我们？",
        "url": "https://xxx"
      }
    ],
    "banners": [
      {
        "img": "https://xxx",
        "url": "https://xxx"
      }
    ]
  },
  // 分组产品。生产请勿使用。
  "home_group_product": [
    {
      "title": "测试",
      "subTitle": "测试 测试 测试",
      "products": [
        {
          "title": "开发工具",
          "description": "开发人员使用",
          "img": "https://xxx",
          "urlType": "MiniProgramPage",
          "url": "/pages/test/index"
        },
        {
          "img": "https://xxx",
          "title": "fiss64620",
          "description": "fiss64620",
          "urlType": "MiniProgramPage",
          "url": "/pages/main/xman/index?xCode=fiss64620"
        },
        {
          "img": "https://xxx",
          "url": "https://xxx",
          "title": "抗疫保险",
          "description": "众安免费送你新冠肺炎专属保障",
          "before": "",
          "money": "免费",
          "after": "",
          "unit": ""
        },
        {
          "img": "https://xxx",
          "url": "https://xxx",
          "title": "尊享e生2021版（月缴版）",
          "description": "明星产品！最高600万保额，疾病意外住院都能赔",
          "before": "首月",
          "money": "1",
          "after": "元",
          "unit": ""
        }
      ],
      "links": [
        {
          "text": "保险如何保护我们？",
          "url": "https://xxx"
        },
        {
          "text": "保险如何保护我们？",
          "url": "https://xxx"
        }
      ],
      "banners": [
        {
          "img": "https://xxx",
          "url": "https://xxx",
          "needLogin": 1
        }
      ]
    }
  ],
  // 推荐位
  "recommend_position": [
    {
      "img": "https://xxx",
      "url": "https://xxx",
      "needLogin": 1
    },
    {
      "img": "https://xxx",
      "url": "https://xxx",
      "needLogin": 1
    }
  ],
  // 图文
  // 类型 1、2、3 对应信息请看代码
  "news": [
    {
      "type": 1,
      "title": "摊上事儿了！医保卡外借竟踩到了这些雷",
      "desc": "近来，好多小伙伴买保险前跑来问小马，“我的医保卡借出去过，影响买保险吗？”。答案是：当然有影响！那么，到底会有什么影响呢？已经借出去过了，该怎么办呢？小马今天就来跟大家说说“医保卡外借”的二三事。",
      "image": "https://xxx",
      "url": "https://xxx"
    }
  ],
  // 底部
  // 原始键值 popup_banner, 需要代码兼容
  "home_popup_banner": [
    {
      "img": "https://xxx",
      "url": "https://xxx"
    }
  ],
  //首页弹窗banner配置
  "home_cover_banner": [
    {
      //banner图片
      "img": "https://cdn-health.zhongan.com/magiccube/resource/rl/f9nMtyqD2W.png"
    }
  ],
  // 签到页面推荐列表配置
  "sign_recommend_product": [
    {
      //推荐区域展示的图片
      "img": "https://cdn-health.zhongan.com/magiccube/resource/rl/kCsh9cUq-c.png",
      //点击图片跳转的URL地址
      "url": "https://ihealth.zhongan.com/insure/gt?goodsCode=G20200911002&channelCode=**********&channelSource=T202103040200375048&version=V1&sign=7c6r1GhYGQCvyGqKKOeANA"
    },
    {
      "img": "https://cdn-health.zhongan.com/magiccube/resource/rl/LWaTMqrCiV.png",
      "url": "https://ihealth.zhongan.com/insure/gt?goodsCode=G202010120001&channelCode=**********&channelSource=T202103040200375148&version=V1&sign=yrza9y2iGVTwCf-Vzwp3ag"
    }
  ],
  // 订阅模板配置
   "subscribe_tmplIds": {
    "sign": {
      "title": "签到提醒",
      "description": "开启订阅,现金红包每天领！",
      "ids": [
        "PVC_HBtp0DZ0GilG754C91bTRE-DocYJ9S2SCwAEddI",
        "vFAXb3vAvhnTBRvPjZ71XefILhAlaRNnsCr_GKRcOyY"
      ]
    },
    "claim": {
      "title": "理赔进度提醒",
      "description": "及时知晓理赔各进度,可以查看理赔详情",
      "ids": [
        "DRjS7e-qPfxsUL0ZTos4xAHauZE-AOrzE3I-kN9mqzAI"
      ]
    }
  },
  // 请查看看代码
  "free_song": [
    {
      "name": "免费重疾保障周周领",
      "isHot": "Y",
      "title": "最高可领取68000元保额",
      "subTitle": "确诊即赔 保100种重疾",
      "url": "https://xxx",
      "needLogin": 1
    }
  ]
}
```

### 'unocss'

w-100 width: 100px
h-200 height: 200px

m-10 margin: 10px