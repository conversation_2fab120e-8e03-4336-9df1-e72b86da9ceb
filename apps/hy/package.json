{"name": "hy", "version": "1.0.0", "private": true, "description": "互联网医院小程序", "templateInfo": {"name": "default", "typescript": true, "css": "sass"}, "scripts": {"preinstall": "npx only-allow pnpm", "build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "lint": "npm run lint:script && npm run lint:style", "lint:script": "eslint --ext .js,.jsx,.ts,.tsx src;  exit 0", "lint:style": "stylelint **/*.module.scss; exit 0", "pre-commit": "tsc --noEmit && lint-staged", "test": "jest --config jest.config.js --no-cache"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "peerDependencies": {"postcss": "^8.0.0"}, "engines": {"node": ">=16.0.0"}, "dependencies": {"@babel/eslint-parser": "^7.21.8", "@babel/eslint-plugin": "^7.19.1", "@babel/runtime": "^7.24.4", "@galacean/appx-adapter": "^0.3.3", "@galacean/effects": "^2.4.3", "@galacean/effects-plugin-multimedia": "^2.4.3", "@galacean/effects-plugin-spine": "2.4.3", "@tanstack/react-query": "4.3.9", "@tarojs/components": "4.0.8", "@tarojs/helper": "4.0.8", "@tarojs/plugin-framework-react": "4.0.8", "@tarojs/plugin-html": "4.0.8", "@tarojs/plugin-platform-alipay": "4.0.8", "@tarojs/plugin-platform-h5": "4.0.8", "@tarojs/plugin-platform-harmony-hybrid": "4.0.8", "@tarojs/plugin-platform-jd": "4.0.8", "@tarojs/plugin-platform-qq": "4.0.8", "@tarojs/plugin-platform-swan": "4.0.8", "@tarojs/plugin-platform-tt": "4.0.8", "@tarojs/plugin-platform-weapp": "4.0.8", "@tarojs/react": "4.0.8", "@tarojs/runtime": "4.0.8", "@tarojs/shared": "4.0.8", "@tarojs/taro": "4.0.8", "@tbmp/mp-cloud-sdk": "^1.5.10", "@testing-library/react": "^12.1.2", "@za/hfe-library-track": "^0.0.2-alpha.10", "ahooks": "^3.9.0", "babel-jest": "^27.3.1", "classnames": "^2.3.1", "js-base64": "^3.7.7", "lodash.debounce": "^4.0.8", "lodash.isequal": "^4.5.0", "lodash.throttle": "^4.1.1", "lottie-miniprogram": "^1.0.12", "react": "^18.0.0", "react-dom": "^18.0.0", "react-redux": "^9.2.0", "react-testing-library": "^8.0.1", "redux": "^5.0.1", "redux-actions": "^3.0.3", "redux-logger": "^3.0.6", "redux-promise": "^0.6.0", "redux-thunk": "^3.1.0", "save": "^2.4.0", "shared": "workspace:*"}, "devDependencies": {"@babel/core": "^7.24.4", "@babel/plugin-proposal-class-properties": "7.14.5", "@babel/preset-react": "^7.24.1", "@babel/runtime": "^7.7.7", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@tarojs/cli": "4.0.8", "@tarojs/plugin-inject": "4.0.8", "@tarojs/taro-loader": "4.0.8", "@tarojs/webpack5-runner": "4.0.8", "@types/node": "^18", "@types/react": "^18.0.0", "@types/webpack-env": "^1.13.6", "@unocss/webpack": "^0.53.5", "babel-eslint": "^11.0.0-beta.2", "babel-preset-taro": "4.0.8", "cross-env": "^7.0.2", "eslint": "^8.57.0", "eslint-config-taro": "4.0.8", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.4.0", "miniprogram-automator": "^0.12.1", "postcss": "^8.4.38", "postcss-jsx": "^0.36.4", "react-refresh": "^0.14.0", "sass": "^1.75.0", "source-map-loader": "^4.0.1", "stylelint": "^16.4.0", "ts-jest": "^27.0.7", "tsconfig-paths-webpack-plugin": "^4.1.0", "typescript": "^5.4.5", "unocss": "^0.53.5", "webpack": "5.91.0"}, "staticResolve": {"projectId": "5f2a5e5bec449b001de90834", "token": "Bearer IqMKIqiAcIOWNUs9MJZta04KhsT6qghq", "cssPath": "src/pages/**/*(*.module.scss)", "staticPath": "src/static"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"{src,config}/**/*.{js,jsx,ts,tsx}": "eslint", "src/**/*.scss": "stylelint"}}