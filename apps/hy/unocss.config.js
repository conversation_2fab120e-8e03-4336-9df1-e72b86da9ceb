import { defineConfig } from 'unocss';

const directionMap = {
  r: 'right',
  l: 'left',
  t: 'top',
  b: 'bottom',
};

export const myPreset = {
  name: 'my-preset',
  rules: [
    [/^flex-basis-([.\d]+)$/, ([_, num]) => ({ flex: `0 0 ${num}%` })],
    [/^flex-([.\d]+)$/, ([_, num]) => ({ flex: `${num}` })],
    [/^w-([.\d]+)$/, ([_, num]) => ({ width: `${num}rpx` })],
    [/^h-([.\d]+)$/, ([_, num]) => ({ height: `${num}rpx` })],
    [/^wp-([.\d]+)$/, ([_, num]) => ({ width: `${num}%` })],
    [/^hp-([.\d]+)$/, ([_, num]) => ({ height: `${num}%` })],
    [/^mw-([.\d]+)$/, ([_, num]) => ({ 'max-width': `${num}rpx` })],
    [/^mh-([.\d]+)$/, ([_, num]) => ({ 'max-height': `${num}rpx` })],
    [/^lh-([.0-9]+)$/, ([_, num]) => ({ 'line-height': `${num}rpx` })],
    [/^lh-([a-zA-Z]+)$/, ([_, num]) => ({ 'line-height': `${num}` })],
    [/^([rltbse])(?:-)([.\d]+)$/, ([_, direction, num]) => ({ [`${directionMap[direction]}`]: `${num}rpx` })],
    [/^m-?([rltbse])(?:-)([.\d]+)$/, ([_, direction, num]) => ({ [`margin-${directionMap[direction]}`]: `${num}rpx` })],
    [/^m-?([rltbse])(?:-)([-.\d]+)$/, ([_, direction, num]) => ({ [`margin-${directionMap[direction]}`]: `${num}rpx` })],
    [/^p-?([rltbse])(?:-)([.\d]+)$/, ([_, direction, num]) => ({ [`padding-${directionMap[direction]}`]: `${num}rpx` })],
    [/^m-?([rltbse])(?:-)([a-zA-Z]+)$/, ([_, direction, num]) => ({ [`margin-${directionMap[direction]}`]: `${num}` })],
    [/^m-([.\d]+)$/, ([_, num]) => ({ margin: `${num}rpx` })],
    [/^p-([.\d]+)$/, ([_, num]) => ({ padding: `${num}rpx` })],
    [/^pv-([.\d]+)$/, ([_, num]) => ({ 'padding-top': `${num}rpx`, 'padding-bottom': `${num}rpx` })],
    [/^mv-([.\d]+)$/, ([_, num]) => ({ 'margin-top': `${num}rpx`, 'margin-bottom': `${num}rpx` })],
    [/^ph-([.\d]+)$/, ([_, num]) => ({ 'padding-left': `${num}rpx`, 'padding-right': `${num}rpx` })],
    [/^mh-([.\d]+)$/, ([_, num]) => ({ 'margin-left': `${num}rpx`, 'margin-right': `${num}rpx` })],
    [/^mh-([a-zA-Z]+)$/, ([_, num]) => ({ 'margin-left': `${num}`, 'margin-right': `${num}` })],
    [/^text-([.\d]+)$/, ([_, num]) => ({ 'font-size': `${num}rpx` })],
    [/^color-(([a-zA-Z0-9]+))$/, ([_, num]) => ({ color: `#${num}` })],
    [/^fw-([.\d]+)$/, ([_, num]) => ({ 'font-weight': `${num}` })],
    [/^fw-([.\w]+)$/, ([_, text]) => ({ 'font-weight': `${text}` })],
    [/^bd-([.\d]+)$/, ([_, num]) => ({ border: `${num}rpx` })],
    [/^bdc-([a-zA-Z0-9]+)$/, ([_, num]) => ({ 'border-color': `#${num}` })],
    [/^bds-([a-zA-Z0-9]+)$/, ([_, num]) => ({ 'border-style': `${num}` })],
    [/^bd-?([rltb])(?:-)([.\d]+)$/, ([_, direction, num]) => ({ [`border-${directionMap[direction]}-width`]: `${num}rpx` })],
    [/^bdc-?([rltb])(?:-)([a-zA-Z0-9]+)$/, ([_, direction, num]) => ({ [`border-${directionMap[direction]}-color`]: `#${num}` })],
    [/^bds-?([rltb])(?:-)([.\w]+)$/, ([__, direction, num]) => ({ [`border-${directionMap[direction]}-style`]: `${num}` })],
    [/^b-rd(?:-)([.\d]+)$/, ([_, num]) => ({ 'border-radius': `${num}rpx` })],
    [/^b-rd-?([rltb])-?([rltb])(?:-)([a-zA-Z0-9]+)$/, ([_, d1, d2, num]) => ({ [`border-${directionMap[d1]}-${directionMap[d2]}-radius`]: `${num}rpx` })],
    [/^bg-([a-zA-Z0-9]+)$/, ([_, num]) => ({ 'background-color': `#${num}` })],
    [/^bg-([a-zA-Z0-9]+)-([a-zA-Z0-9]+)$/, ([_, n1, n2]) => ({ 'background-size': `${n1}rpx ${n2}rpx` })],
    [/^va-([a-zA-Z]+)$/, ([_, num]) => ({ 'vertical-align': `${num}` })],
    [/^va-([-\d]+)$/, ([_, num]) => ({ 'vertical-align': `${num}rpx` })],
    [/^white-space-([a-zA-Z]+)$/, ([_, num]) => ({ 'white-space': `${num}` })],
    [/^text-([a-zA-Z]+)$/, ([_, num]) => ({ 'text-align': `${num}` })],
    [/^zIndex-(\d+)$/, ([_, num]) => ({ 'z-index': `${num}` })],
    [/^align-([a-zA-Z]+)$/, ([_, num]) => ({ 'align-items': `${num}` })],
    [/^overflow-([a-zA-Z]+)$/, ([_, num]) => ({ overflow: `${num}` })],
    [/^overflow-?([xy])(?:-)([a-zA-Z]+)$/, ([_, direction, num]) => ({ [`overflow-${direction}`]: `${num}` })],

    // [/^jc-([a-zA-Z]+)$/, ([_, num]) => ({ 'justify-content': `${num}` })]
    // [/^fw-([.\d]+)$/, ([_, num]) => ({ 'font-weight': `${num}` })],
  ],
  variants: [/* ... */],
  shortcuts: [{
    // block: ''
  }],
  preflights: [
    {
      getCSS: ({ theme }) => {
        return `
          .block {
            display: block;
          }
          .flex {
            display: flex;
          }
          .flex-col {
            flex-direction: column;
          }
          .flex-row {
            flex-direction: row;
          }
          .align-center {
            align-items: center;
          }
          .content-center {
            justify-content: center;
            align-items: center;
            align-content: center;
          }
          .flex-wrap {
            flex-wrap: wrap;
          }
          .relative {
            position: relative;
          }
          .absolute {
            position: absolute;
          }
          .space-between {
            justify-content: space-between;
          }
          .flex-end {
            justify-content: flex-end;
          }
          .inline-block{
            display: inline-block;
          }
          .border-box {
            box-sizing: border-box;
          }
          .text-ellipsis {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap
          }
          .absolute {
            position: absolute;
          }
        `;
      },
    },
  ],
  // ...
};

export default defineConfig({
  presets: [
    myPreset, // your own preset
  ],
});
