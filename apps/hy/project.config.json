{"miniprogramRoot": "dist/", "projectname": "hfe-zunxiang-mp", "description": "za-hospital", "appid": "wx3c564538ea8e3905", "setting": {"urlCheck": true, "es6": false, "enhance": false, "postcss": false, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "bundle": false, "useIsolateContext": true, "useCompilerModule": true, "userConfirmedUseCompilerModuleSwitch": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": false}, "compileType": "miniprogram", "condition": {}, "libVersion": "2.32.3", "srcMiniprogramRoot": "dist/", "packOptions": {"ignore": [], "include": []}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "requiredPrivateInfos": ["getLocation", "<PERSON><PERSON><PERSON><PERSON>"]}