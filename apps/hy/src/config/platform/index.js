export default {
  configScene: 'weapp_zunxiang',
  appCode: 'ONAPP2020121400001',
  defaultChannelCode: 'c20195660470001',
  defaultChannelSource: 'T202101130100910047',
  appId: 'wxbac45cc1588a5a75',
  platformName: 'zunxiangSmallProgram',
  zaimAcctType: 108,
  bannerEnum: {
    banner: 'ONB20201222000001',
    home_main_product: 'ONB20201222000003',
    home_float_banner: 'ONB20201222000006',
    home_cover: 'ONB20210622000002', // 首页弹窗
  },
  bannerLocEnum: {
    banner: 'home_page_head', // 'ONB20201222000001',
    home_main_product: 'home_page_everyone_is_buying', // 'ONB20201222000003',
    home_float_banner: 'home_page_window', // 'ONB20201222000006', 飘窗
    home_cover: 'home_page_tip', // 'O 弹窗
  },
  track: {
    source_id: 'slsdhlpdy0ayex6v',
    app_name: '尊享小程序',
    appid: 'wxbac45cc1588a5a75',
  },
};
