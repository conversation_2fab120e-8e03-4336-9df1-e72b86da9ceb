import Taro from '@tarojs/taro';
import test from './env/test';
import pre from './env/pre';
import prd from './env/prd';
import platform from './platform';

const denv = process.env.DEPLOY_ENV ? process.env.DEPLOY_ENV : 'prd';

const getEnv = () => {
  if (process.env.TARO_ENV === 'weapp') {
    try {
      /* global __wxConfig */
      if (__wxConfig?.envVersion !== 'release') {
        return Taro.getStorageSync('API_ENV') || denv;
      }
    } catch (e) {
      // ignore
    }
  }

  if (process.env.TARO_ENV === 'alipay' || process.env.TARO_ENV === 'tt') {
    return Taro.getStorageSync('API_ENV') || denv;
  }

  return denv;
};

const env = getEnv();

const configs = {
  test: { ...test },
  pre: { ...pre },
  prd: { ...prd },
};

type ValueType<T> = T[keyof T];
type IConfig = ValueType<typeof configs> & typeof platform & { env: string };

const config: IConfig = { ...configs[env], ...platform };

config.denv = denv;
config.env = env;

export { denv, env, configs };

export default config;
