import config, { configs } from '@/config';
import fetchAPI, { once, MPPATH, IResponse } from '@/utils/fetch';



const { env, defaultChannelCode, platformName } = config;

export interface IRelationShipItem {
  value: number;
  name: string;
}

export const fetchRelationShip = () => {
  return fetchAPI.get<IRelationShipItem[]>({
    server: 'lemon',
    path: `/v1/familyRelation/allRelation/${defaultChannelCode}`,
    login: true,
  });
};
