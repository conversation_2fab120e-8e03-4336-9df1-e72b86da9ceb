import React, { PropsWithChildren } from 'react';
import Taro, { useLaunch, useDidShow } from '@tarojs/taro';
import storage from '@/utils/storage';
import config from '@/utils/config';
import format from '@/utils/format';
import ilog from '@/utils/ilog';
import initSeraph from '@/utils/initSeraph';
import getChannelInfo from '@/utils/getChannelInfo';
import { globalData } from '@/utils/global';

import { Provider } from 'react-redux'
import store from './store'

import './app.scss';

// Wepy-era initializations
// These are executed once when the app module is loaded.
ilog.config();
initSeraph({});


function App({ children }: PropsWithChildren) {
  const ilogInit = () => {
    ilog.setConfig({
      ZAHLWYY_accountId: storage.get('maskActId'),
      ZAHLWYY_userId: storage.get('maskusId'),
      ZAHLWYY_channelSource: getChannelInfo.channelSource(),
      ZAHLWYY_channelResourceCode: getChannelInfo.channelResourceCode(),
      ZAHLWYY_openId: storage.get('openId'),
      ZAHLWYY_channelOrigin: storage.get('channelOrigin'),
      ZAHLWYY_channelCode: storage.get('channelCode'),
    });
    ilog.start();
  };

  useLaunch(params => {
    ilogInit();

    if ((Taro.getSystemInfoSync() as any).inFinChat) {
      (Taro as any).getAppDeviceInfo().then(res => {
        console.log(res, Taro.getAccountInfoSync());
        Taro.setStorageSync('DEBUG_ENV', res.appEnv === 'test' ? 'dev' : res.appEnv);
      }).catch(err => {
        console.log('获取信息失败', err);
      });
    }

    const { scene = '', query = {} } = params || {};
    //标记一下小程序打开的场景 首页显示添加到我的小程序时使用
    globalData.scene = scene;

    if (query && query.from === 'public') {
      const bindPublic = storage.get('bindPublic');
      if (bindPublic) {
        return;
      }
      Taro.navigateTo({ url: `./pages/webview?src=${encodeURIComponent('/hospital/oauth')}` });
    }
    // 获取小程序的基础账号信息 用于传入webview内判断，是否在自有小程序内
    const ownAppletAppid = storage.get('ownAppletAppid');
    if (Taro.canIUse('getAccountInfoSync') && !ownAppletAppid) {
      const { miniProgram: { appId = '' } = {} } = Taro.getAccountInfoSync() || {};
      appId && storage.set('ownAppletAppid', appId, undefined);
    }
    if (Taro.canIUse('getMenuButtonBoundingClientRect')) {
      let sysInfo = Taro.getSystemInfoSync();
      let rect = Taro.getMenuButtonBoundingClientRect() || {};
      const { bottom = 0 } = rect || {};
      let navBarHeight = bottom + 16 / sysInfo.pixelRatio;
      // 存储胶囊位置信息
      storage.set('menuButtonRect', rect, undefined);
      // 存储自定义导航栏的高度
      storage.set('navBarHeight', navBarHeight, undefined);
    } else {
      Taro.showToast({
        title: '您的微信版本过低，界面可能会显示不正常',
        icon: 'none',
        duration: 4000,
      });
    }

    // Set global webview URL
    globalData.webviewUrl = config.baseAPI.zaApi.webview;
  });

  useDidShow(params => {
    console.log('==app onshow====', params);

    const { scene = '', query = {} } = params || {};
    const { payParams = '' } = query;

    // 设置渠道参数
    // 扫码进入的场景值 直接清除本地渠道的缓存 其他情况下在小程序没有冷启动的时候会读取缓存值
    // 增加资源位的概念 资源位的逻辑同之前channelsource的逻辑 有资源位时先通过资源位获取到channelSource 登录时候带上资源位置其余暂时还是使用channelSource
    if (payParams) {
      return;
    }

    // console.log("进入场景值2", Taro.getEnterOptionsSync());
    const {
        channelSource: queryChannelSource = '',
        src = '',
        channelResourceCode: queryChannelResourceCode = '',
        channelOrigin: queryChannelOrigin = '',
        channelCode: queryChannelCode = ''
    } = query; // 扫码进入的渠道携带的channelSource
    const { channelSource: urlChannelSource = '', channelResourceCode: urlChannelResourceCode = '' } = format.deserialize(decodeURIComponent(src)); // 从订阅消息跳转进来携带的channelSourcen或channelResourceCode

    const defaultCS = 'wechat_applet';  //默认的channelSourceCode

    const oldCS = storage.get('channelSource') || '';
    const oldCRC = storage.get('channelResourceCode') || '';

    let newCS = urlChannelSource || queryChannelSource;  //上面👆两种方式进来的channelSource统一
    const newCRC = urlChannelResourceCode || queryChannelResourceCode || (!newCS && oldCRC) || '';  //上面👆两种方式进来的channelSourceCode统一

    if (
      scene === 1011 ||
      scene === 1012 ||
      scene === 1013 ||
      scene === 1047 ||
      scene === 1048 ||
      scene === 1049
    ) {
      if(newCRC) {
        storage.remove('channelSource');
        storage.remove('channelResourceCode');
      }
    }

    // 保存健康险串联标识，需要落库。每次更新后 12 小时有效期
    if (queryChannelOrigin) {
      storage.set('channelOrigin', queryChannelOrigin, 720);
    }

    // 保存健康险串联标识，无需落库。每次更新后 12 小时有效期
    if (queryChannelCode) {
      storage.set('channelCode', queryChannelCode, 720);
    }

    //1、链接有资源位的时候取资源位，通过资源位获取到channelSource
    //2、链接没有资源位的有channelSource的时候, 应该清除缓存资源位，取channelSource
    //3、链接都没有的时候，此时会取到storage保存的资源位和渠道
    //4、链接都没有的时候且特殊场景进入的时候取渠defaultCRC

    console.log(`--------------处理CS和CRC的逻辑--------------`);
    console.log(`--------------oldCS: ${oldCS}--------------newCS: ${newCS}`);
    console.log(`--------------oldCRC: ${oldCRC}--------------newCRC: ${newCRC}`);

    if (newCRC) {
      storage.set('channelResourceCode', newCRC, undefined);
      if (newCRC !== oldCRC) {
        storage.remove('Token');
        storage.remove('Long_Token');
        storage.remove('userInfo');
        storage.remove('channelSource');
        storage.set('channelResourceCode', newCRC, undefined);
      }
    } else {
      newCS = newCS || oldCS || defaultCS;
      if (newCS === oldCS) {
        storage.set('channelSource', newCS, undefined);
      } else {
        storage.remove('Token');
        storage.remove('Long_Token');
        storage.remove('userInfo');
        storage.remove('channelResourceCode');
        storage.set('channelSource', newCS, undefined);
      }
    }

    // 未注册的设置每次打开小程序的时候自动弹窗提示注册
    storage.set('openapp', true, undefined);
  });

  // children 是 Page 实例
  return <Provider store={store}>{children}</Provider>;
}

export default App;
