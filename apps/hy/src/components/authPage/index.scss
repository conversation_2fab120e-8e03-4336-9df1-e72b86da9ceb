.auth_page {
  height: 100vh;
  background: #fff;

  .header {
    position: relative;
    height: 386rpx;
    padding-top: 78rpx;
    background: #fafafa;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
    text-align: center;
    font-size: 28rpx;
    color: rgba(0, 0, 0, 0.7);
    line-height: 41rpx;

    .auth-tips {
      margin: 0 60px;
    }

    &::after {
      content: '';
      position: absolute;
      width: 24rpx;
      height: 24rpx;
      bottom: -14rpx;
      left: 50%;
      margin-left: -12rpx;
      border-left: 1rpx solid rgba(0, 0, 0, 0.05);
      border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
      transform: rotate(-45deg);
      background: inherit;
    }

    .auth_company {
      margin-bottom: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .icon_logo {
      width: 120rpx;
      height: 120rpx;
      border-radius: 20rpx;
    }

    .icon_tranfor {
      position: relative;
      width: 54rpx;
      margin: 0 30rpx;
      height: 8rpx;
      transform: scale(0.7);

      &::before {
        content: '';
        left: 0;
        position: absolute;
        width: 54rpx;
        height: 4rpx;
        background: #8d8988;
        border-radius: 1rpx;
      }

      &::after {
        content: '';
        right: 10rpx;
        top: -18rpx;
        position: absolute;
        width: 4rpx;
        height: 28rpx;
        background: #8d8988;
        border-radius: 1rpx;
        transform: rotate(-60deg);
      }

      &.next_line {
        transform: rotate(180deg) scale(0.7);
      }
    }
  }

  .main {
    margin: 50rpx auto 0;
    width: 630rpx;

    .auth_title {
      font-size: 36rpx;
      font-weight: bold;
      margin-bottom: 16rpx;
    }

    .auth_item {
      position: relative;
      padding-left: 16rpx;
      margin-top: 8rpx;
      font-size: 28rpx;
      line-height: 40rpx;
      color: rgba(38, 34, 31, 0.7);

      &::before {
        position: absolute;
        content: '';
        top: 16rpx;
        left: 0;
        width: 6rpx;
        height: 6rpx;
        border-radius: 50%;
        background: #cbcbcb;
      }
    }
  }

  .agreement_box {
    position: relative;
    margin-top: 60rpx;
    font-size: 24rpx;
    color: rgba(51, 51, 51, 0.6);

    .shanzhen-label {
      font-size: 28px;
      margin-left: 20px;
    }
  }

  .za-checkbox {
    position: absolute;
    left: 0;
    top: 0;
    transform: scale(0.8);
  }

  .za-checkbox__inner {
    border-radius: 50%;
  }

  .link {
    color: #00bc70;
    display: inline;
  }

  .btn_auth {
    width: 100%;
    margin-top: 40rpx;
    height: 88rpx;
    border-radius: 44rpx;
    background-color: #00bc70;
    color: #fff;

    &.za-button--disabled {
      background: rgba(0, 0, 0, 0.3);
      border: none;
      opacity: 1;
    }
  }

  .checkbox_wrap {
    text-align: left;

    .checkbox {
      transform: scale(0.7) translateY(-5rpx);
    }

    .check-content {
      display: flex;
      align-self: center;
    }
  }

  .check-box {
    width: 32rpx;
    margin-right: 20rpx;
  }
}

.auth-modal {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 9999;

  &__container {
    position: absolute;
    left: 50%;
    bottom: 30%;
    transform: translateX(-50%);
    width: 70%;
    box-sizing: border-box;
    background: #fff;
    text-align: center;
    transition: all 0.3s;
    border-radius: 5px;
    padding: 0px 20px 20px;
  }

  &__title {
    font-size: 36rpx;
    font-weight: bold;
    color: #1e1e1e;
    padding: 20px 15px 0 15px;
    text-align: center;
  }

  &__content {
    font-size: 24rpx;
    color: #999;
    line-height: 34rpx;
    text-align: left;
    padding-top: 20px;
  }

  .link {
    color: #00bc70;
  }

  &__footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-top: 50rpx;
  }

  &__btn {
    padding: 16rpx 40rpx;
    text-align: center;
    width: 45%;
    border-radius: 40rpx;
    border: 1px solid #00bc70;
    color: #00bc70;

    &.agree {
      background-color: #00bc70;
      border: 0;
      font-size: 36rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #fff;
    }
  }
}

checkbox .wx-checkbox-input {
  border-radius: 50%;
}

checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  border: none;
  background: url('https://cdn-qcloud.zhongan.com/a00000/za-asclepius/applets/guide/tick.png') no-repeat center center #00bc70;
  background-size: 100% 100%;
}

checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
  width: 30rpx;
  height: 30rpx;
  line-height: 30rpx;
  text-align: center;
  font-size: 0;
  color: #00bc70;
  background: transparent;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
}
