import React, { useState, useEffect, useCallback } from 'react';
import { View, Image, CheckboxGroup, Checkbox, Button } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { fetchJson } from '@/utils/fetch';
import './index.scss';

// 定义清晰的 Props 接口，并添加 onSuccess 回调
interface AuthPageProps {
  thirdPlatformCode?: string;
  businessType?: string;
  needSaveAuth?: 'Y' | 'N';
  needGenerateToken?: 'Y' | 'N';
  onSuccess: (tokenOrInfo: string | object) => void; // 代替原有的 $emit
}

const AuthPage: React.FC<AuthPageProps> = (props) => {
  // 使用 es6 默认参数来处理可选 props
  const {
    thirdPlatformCode = 'ZA_UMBRELLA',
    businessType = 'index',
    needSaveAuth = 'Y',
    needGenerateToken = 'Y',
    onSuccess,
  } = props;

  // 使用 useState 管理所有组件状态
  const [info, setInfo] = useState<any>({});
  const [permissions, setPermissions] = useState<string[]>([]);
  const [checked, setChecked] = useState(false);
  const [visible, setVisible] = useState(false); // 二次确认弹窗的状态

  // 使用 useEffect 实现组件初始化数据加载 (替代原 init() 方法)
  useEffect(() => {
    const fetchAuthConfig = async () => {
      try {
        const res = await fetchJson({
          url: '/zaApi/v1/patient/partner/cfg/query/list',
          method: 'POST',
          data: {
            option: {
              needAttachment: true,
            },
            partnerCode: thirdPlatformCode,
            businessType: businessType,
          },
          needLogin: needSaveAuth === 'Y',
        });

        if (res && res.code === '0') {
          const result = (res.result && res.result[0]) || {};
          // 兼容原 attchmentList 的处理逻辑
          (result.attchmentList || []).forEach((item: any) => {
            result[item.attachmentType] = item.attachmentDownloadUrl;
          });
          result.authNotice = (result.attchmentList || []).filter((item: any) => item.attachmentType === 'authNotice');
          setInfo(result);
          setPermissions(result.extraInfo ? result.extraInfo.split(';') : []);
        }
      } catch (error) {
        console.error("获取授权配置失败", error);
      }
    };
    fetchAuthConfig();
  }, [thirdPlatformCode, businessType, needSaveAuth]);

  // 将所有 methods 转换为 useCallback 封装的函数
  const handleGoPdf = useCallback((item: any) => {
    Taro.navigateTo({
      url: `/pages/bridgeWebview/index?target=${encodeURIComponent(`/hospital/pdf?url=${encodeURIComponent(item.attachmentUrl)}`)}`,
    });
  }, []);

  const handleCheckboxChange = useCallback((e: any) => {
    setChecked(e.detail.value.length > 0);
  }, []);

  const handleReject = useCallback(() => {
    setVisible(false);
  }, []);

  const handleSaveAuth = useCallback(async () => {
    if (needSaveAuth === 'N') {
      onSuccess(info);
      return;
    }

    try {
      await fetchJson({
        url: '/zaApi/v1/patient/partner/user/auth/saveUserAuth',
        method: 'POST',
        data: { thirdPlatformCode },
        needLogin: true,
      });

      if (needGenerateToken === 'N') {
        onSuccess(info);
        return;
      }

      const tokenRes = await fetchJson({
        url: '/zaApi/v1/patient/partner/user/auth/generateToken',
        method: 'POST',
        data: { thirdPlatformCode, businessType, target: '' },
        needLogin: true,
      });

      if (tokenRes && tokenRes.code === '0') {
        onSuccess(tokenRes.result.split('?')[1]);
      }
    } catch (error) {
      console.error("授权或生成Token失败", error);
    }
  }, [needSaveAuth, needGenerateToken, onSuccess, info, thirdPlatformCode, businessType]);

  const handleMainAuthClick = useCallback(() => {
    if (!checked) {
      setVisible(true);
      return;
    }
    handleSaveAuth();
  }, [checked, handleSaveAuth]);

  const handleAgreeInModal = useCallback(() => {
    setChecked(true);
    setVisible(false);
    // 直接调用核心授权函数，不再需要从 methods 中访问
    handleSaveAuth();
  }, [handleSaveAuth]);

  return (
    <View className='auth_page'>
      <View className='header'>
        <View className='auth_company'>
          <Image className='icon_logo' src={info.ourImage} />
          <View>
            <View className='icon_tranfor' />
            <View className='icon_tranfor next_line' />
          </View>
          <Image className='icon_logo' src={info.partnerImage} />
        </View>
        <View>即将跳转至{info.partnerName}提供的页面</View>
        <View>后续服务由{info.partnerName}提供</View>
      </View>
      <View className='main'>
        <View className='auth_title'>同意授权以下权限可继续使用服务</View>
        {permissions.map((item) => (
          <View key={item} className='auth_item'>{item}</View>
        ))}
        <View className='agreement_box'>
          <View className="checkbox_wrap">
            <CheckboxGroup onChange={handleCheckboxChange}>
              <View className="check-content">
                <View className="check-box">
                  <Checkbox className="checkbox" value="notice" checked={checked} />
                </View>
                <View>
                  我已仔细阅读
                  {(info.authNotice || []).map((item: any) => (
                    <View key={item.attachmentName} className='link' onClick={() => handleGoPdf(item)}>
                      {item.attachmentName}
                    </View>
                  ))}
                  继续即表示我已知悉相关规则与风险并同意相关条款。
                </View>
              </View>
            </CheckboxGroup>
          </View>
        </View>
        <Button className='btn_auth' onClick={handleMainAuthClick}>
          授权
        </Button>
      </View>
      {visible && (
        <View className='auth-modal'>
          <View className='auth-modal__container'>
            <View className="auth-modal__title">信息授权告知</View>
            <View className='auth-modal__content'>请您仔细阅读
              {(info.authNotice || []).map((item: any) => (
                <View key={item.attachmentName} className='link' onClick={() => handleGoPdf(item)}>
                  {item.attachmentName}
                </View>
              ))}
              ，点击"同意"即表示您已阅读并同意相关规则条款。
            </View>
            <View className='auth-modal__footer'>
              <View className='auth-modal__btn' onClick={handleReject}>不同意</View>
              <View className='auth-modal__btn agree' onClick={handleAgreeInModal}>同意</View>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

export default AuthPage;
