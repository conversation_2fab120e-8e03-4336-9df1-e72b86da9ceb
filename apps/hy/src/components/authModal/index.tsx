import Taro from '@tarojs/taro';
import React, { useState } from 'react';
import { View, Image, Button, Checkbox, CheckboxGroup, Label } from '@tarojs/components';
import { CDN_PREFIX } from '@/utils/staticData';
import { globalData } from '@/utils/global';
import storage from '@/utils/storage';
import { bindUserPhone } from '@/utils/fetch';
import closeIcon from '@/images/icon_close.png';
import './index.scss';

interface AuthModalProps {
  show: boolean;
  butlerClick?: boolean;
  authResourceCode?: 'ZA' | 'ZA_MALL';
  onClose: () => void;
  onAuthSuccess: (phone: string) => void;
  onButlerAuthSuccess?: () => void;
}

const AuthModal: React.FC<AuthModalProps> = (props) => {
  const {
    show,
    butlerClick = false,
    authResourceCode = 'ZA',
    onClose,
    onAuthSuccess,
    onButlerAuthSuccess,
  } = props;

  const [checked, setChecked] = useState(false);

  const handleUserPhone = async (e: any) => {
    if (e.detail.encryptedData && e.detail.iv) {
      try {
        await bindUserPhone({
          encryptedData: e.detail.encryptedData,
          iv: e.detail.iv,
          eventType: 1,
        });

        const { accountPhone = '' } = storage.get('userInfo');
        if (!accountPhone) {
          Taro.showToast({
            icon: 'none',
            title: '获取手机号码出错',
          });
        } else {
          onAuthSuccess(accountPhone);
          onClose();
          if (butlerClick && onButlerAuthSuccess) {
            onButlerAuthSuccess();
          }
        }
      } catch (error) {
        Taro.showToast({
          icon: 'none',
          title: '授权失败，请重试',
        });
      }
    }
  };

  const handleRadio = () => {
    if (!checked) {
      Taro.showToast({
        icon: 'none',
        title: '请先阅读并勾选协议',
      });
    }
  };

  const goToAgreement = (type: 'agreement' | 'private') => {
    const url = `${globalData.webviewUrl}/hospital/${type}?channelResourceCode=HYXCX`;
    Taro.navigateTo({
      url: `/pages/common/webview/index?src=${encodeURIComponent(url)}`,
    });
  };

  const handleCheckboxChange = (e: any) => {
    setChecked(e.detail.value.length > 0);
  };

  const modalClasses = `auth_modal ${show ? 'show' : ''}`;
  const contentClasses = `auth_modal__content ${show ? 'show' : ''}`;

  if (!show) {
      return null
  }

  return (
    <View className={modalClasses}>
      <View className={contentClasses}>
        <Image className="auth_modal__img" src={`${CDN_PREFIX}auth_modal_img.png`} />
        <View>为保证更好的使用体验请登录同步数据</View>

        {checked ? (
           <Button
            className="auth_modal__btn"
            hoverClass="active_auth_button"
            openType="getPhoneNumber"
            onGetPhoneNumber={handleUserPhone}
          >
            使用手机号快捷登录
          </Button>
        ) : (
          <Button className="auth_modal__btn" hoverClass="active_auth_button" onClick={handleRadio}>
            使用手机号快捷登录
          </Button>
        )}

        {authResourceCode === 'ZA' && (
          <Image className="auth_modal__close" src={closeIcon} onClick={onClose} />
        )}

        <View className="checkbox_wrap">
          <CheckboxGroup onChange={handleCheckboxChange}>
            <Label className="flex">
              <View className="check-box">
                <Checkbox className="checkbox custom-checkbox" value="notice" checked={checked} />
              </View>
              <View className="auth_modal__agreement">
                <View className="text">我已阅读并同意</View>
                <View className="link" onClick={() => goToAgreement('agreement')}>
                  《众安用户注册协议》
                </View>
                <View className="text">和</View>
                <View className="link" onClick={() => goToAgreement('private')}>
                  《个人隐私政策协议》
                </View>
                <View className="text">，未注册绑定的手机号验证成功后将自动注册。</View>
              </View>
            </Label>
          </CheckboxGroup>
        </View>
      </View>
    </View>
  );
};

export default AuthModal;
