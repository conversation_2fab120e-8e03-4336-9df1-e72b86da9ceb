import React, { useState, useEffect } from 'react';
import { View, Image } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { fetchJson } from '@/utils/fetch';
import { xflowPushEvent } from '@/utils/pageTrack';
import { createSkeletonPlaceholderList } from '@/utils/tools';
import { CDN_PREFIX } from '@/utils/staticData';
import format from '@/utils/format';
import './index.scss';

const GoldSteel: React.FC = () => {
  const [goldSteelList, setGoldSteelList] = useState<any[]>(createSkeletonPlaceholderList(8));
  const [visible, setVisible] = useState(true);
  const [skeletonVisible, setSkeletonVisible] = useState(true);

  const jumpTo = (item: any) => {
    xflowPushEvent({
      eventTag: 'ZAHLWYY_SY',
      text: '首页',
      attributes: '首页_金钢位_' + item.contentName,
    });
    Taro.navigateTo({
      url: `/pages/webview/index?src=${encodeURIComponent(item.contentHref)}`,
    });
  };

  const jumToServiceList = () => {
    xflowPushEvent({
      eventTag: 'ZAHLWYY_SY',
      text: '首页',
      attributes: '首页_金钢位_查看更多',
    });
    Taro.navigateTo({
      url: `/pages/webview/index?src=${encodeURIComponent('/hospital/allservice')}`,
    });
  };

  const getGoldSteelList = () => {
    fetchJson({
      url: '/zaApi/v1/patient/classify/queryContentList',
      method: 'POST',
      needLoading: true,
      needLogin: false,
      data: {
        serviceType: 'normal',
        bizScene: 'applet',
      },
    }).then((res: any) => {
      setSkeletonVisible(false);
      if (res.code === '0') {
        let { result = [] } = res || {};
        result = result.map((item: any) => {
          item.attachmentDomain = item.attachmentDomainList.find(
            (item: any) => item.attachmentType === 'serviceContentPicture'
          ) || {};
          item.attachmentFlagDomain = item.attachmentDomainList.find(
            (item: any) => item.attachmentType === 'serviceContentFlagPicture'
          ) || {};
          item.attachmentDomain.attachmentDownloadUrl = (format.imageUrl as any)(
            item.attachmentDomain.attachmentDownloadUrl
          );
          item.attachmentFlagDomain.attachmentDownloadUrl = (format.imageUrl as any)(
            item.attachmentFlagDomain.attachmentDownloadUrl
          );
          return item;
        });
        console.log('result', result);
        setGoldSteelList(result.slice(0, result.length >= 7 ? 7 : 4));
        setVisible(!!result.length);
      }
    });
  };

  useEffect(() => {
    getGoldSteelList();
  }, []);

  if (!visible) {
    return null;
  }

  return (
    <View className={`goldsteel-component flex ${skeletonVisible ? 'goldsteel-skeleton' : ''}`}>
      {goldSteelList.map((item, index) => (
        <View key={index} className="goldsteel-component__item" onClick={() => jumpTo(item)}>
          <View className="postRlt">
            <Image className="icon" src={item.attachmentDomain.attachmentDownloadUrl} />
            {item.attachmentFlagDomain && item.attachmentFlagDomain.attachmentDownloadUrl && (
              <Image
                className="hotIcon"
                src={item.attachmentFlagDomain.attachmentDownloadUrl}
              />
            )}
          </View>
          <View className="goldsteel-component__name ellipsis">{item.contentName}</View>
        </View>
      ))}
      {goldSteelList.length >= 7 && !skeletonVisible && (
        <View className="goldsteel-component__item" onClick={jumToServiceList}>
          <Image className="icon" src={`${CDN_PREFIX}common/more-icon.png`} />
          <View className="goldsteel-component__name">查看更多</View>
        </View>
      )}
    </View>
  );
};

export default GoldSteel;
