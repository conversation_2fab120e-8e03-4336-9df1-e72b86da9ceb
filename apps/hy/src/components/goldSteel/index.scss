.goldsteel-component {
  flex-flow: row wrap;
  align-content: start;
  padding: 0 20rpx 30rpx;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  transition: all 0.3s;

  &__item {
    flex: 0 0 25%;
    text-align: center;
    padding: 30rpx 10rpx 0;
    line-height: 40rpx;
    font-size: 28rpx;
    color: #333;
    overflow: hidden;

    .icon {
      width: 80rpx;
      height: 80rpx;
      margin-bottom: 10rpx;
    }
  }
  &.goldsteel-skeleton {
    .icon,
    .goldsteel-component__name {
      border-radius: 6rpx;
      background: linear-gradient(
        90deg,
        rgba(190, 190, 190, 0.1) 25%,
        rgba(129, 129, 129, 0.14) 37%,
        rgba(190, 190, 190, 0.1) 63%
      );
      background-size: 400% 100%;
      -webkit-animation: commonSkeletonLoading 1.4s ease infinite;
      animation: commonSkeletonLoading 1.4s ease infinite;
    }
    .goldsteel-component__name {
      height: 35rpx;
    }
    .icon {
      border-radius: 50%;
    }
  }
  .postRlt {
    position: relative;
  }
  .hotIcon {
    width: 56rpx;
    height: 24rpx;
    display: block;
    position: absolute;
    top: -7rpx;
    right: -2rpx;
  }
}
