import Taro, { useDidShow } from '@tarojs/taro';
import React, { useState } from 'react';
import { View, Image } from '@tarojs/components';
import { useSelector, useDispatch } from 'react-redux';
import { CDN_PREFIX } from '@/utils/staticData';
import storage, { everyDayOnceExpiredStorage } from '@/utils/storage';
import config from '@/utils/config';
import { globalData } from '@/utils/global';
import { xflowPushEvent } from '@/utils/pageTrack';
import {
  fetchFamilyDoctorSigned,
  fetchFamilyDoctorService,
} from '@/store/actions/familyDoctor';
import './index.scss';

// 将帮助函数移出组件，使其成为纯函数
const BJ_DRUG_MALL_GROUP_LINK = '/goods/equity?groupId=6QwWgQ&channelPoint=2jOKKzmp';
const YR_DRUG_MALL_GROUP_LINK = '/goods/equity?groupId=gQYXrM&channelPoint=BBbprB3y&rightsNo=RIG235002';

const getHyMallGroupLink = (prdLink: string = BJ_DRUG_MALL_GROUP_LINK) => {
  const GroupLink = {
    dev: '/goods/equity?groupId=aMNY70&channelPoint=MXwiowGh',
    uat: '/goods/equity?groupId=WQ23gj&channelPoint=VCsS4B3y',
    prd: prdLink,
  }[config.ENV];

  const authPrefix = `/hospital/preauth?partnerCode=HY_MALL&businessType=index&target=`;
  return `${authPrefix}${encodeURIComponent(GroupLink || '')}`;
};

const ISDOCTOR_NORMAL_SERVICE = 'isDoctorNormalService';

const GoldCore: React.FC = () => {
  const dispatch = useDispatch();
  const [doctorNormalServiceVisible, setDoctorNormalServiceVisible] = useState(false);

  // 1. 使用 useSelector 替代 @connect
  const { isDoctorNormalService, isBindDoctor, isBuyedServpack, SYSTEMMODE } = useSelector((state: any) => ({
    isDoctorNormalService: state.familyDoctors.isDoctorNormalService,
    isBindDoctor: state.familyDoctors.isBindDoctor,
    isBuyedServpack: state.familyDoctors.isBuyedServpack,
    SYSTEMMODE: state.resource.SYSTEMMODE,
  }));

  // 2. 使用 useDidShow 替代父组件的 $invoke('goldCoreInit')
  useDidShow(() => {
    const ZA_TOKEN = storage.get('Token') || '';
    if (ZA_TOKEN) {
      if (!isBindDoctor) {
        dispatch(fetchFamilyDoctorSigned());
      }
      if (isBuyedServpack === '') {
        dispatch(fetchFamilyDoctorService());
      }
    }
  });

  const goToHyMall = () => {
    xflowPushEvent({
      eventTag: 'ZAHLWYY_MALL',
      text: '首页',
      attributes: '首页_购药商城',
    });
    Taro.navigateTo({
      url: `/pages/webview/index?src=${encodeURIComponent(getHyMallGroupLink(BJ_DRUG_MALL_GROUP_LINK))}`,
    });
  };

  const goToInquiryFlow = () => {
    xflowPushEvent({
      eventTag: 'ZAHLWYY_RJ',
      text: '首页',
      attributes: '首页_仁济卡片',
    });
    const src = SYSTEMMODE?.[0]?.resCode === 'supervision'
      ? encodeURIComponent(`${globalData.webviewUrl}/hospital/choosedepartment`)
      : encodeURIComponent(`${globalData.webviewUrl}/hospital/inquiryform?isChangeInquiry=Y`);

    Taro.navigateTo({ url: `/pages/webview/index?src=${src}` });
  };

  const goToWuYou = () => {
    xflowPushEvent({
      eventTag: 'ZAHLWYY_YRDRUG',
      text: '首页',
      attributes: '首页_原研进口药',
    });
    Taro.navigateTo({
      url: `/pages/webview/index?src=${encodeURIComponent(getHyMallGroupLink(YR_DRUG_MALL_GROUP_LINK))}`,
    });
  };

  const handleGoToChatFamilyDoctor = () => {
    xflowPushEvent({
      eventTag: 'ZAHLWYY_SY',
      text: '首页',
      attributes: '首页_家庭医生',
    });
    const isHasAlertDoctorNormalService = storage.get(ISDOCTOR_NORMAL_SERVICE) || false;

    if (isDoctorNormalService === 'N' && !isHasAlertDoctorNormalService) {
      setDoctorNormalServiceVisible(true);
      everyDayOnceExpiredStorage(ISDOCTOR_NORMAL_SERVICE);
      return;
    }

    setDoctorNormalServiceVisible(false);
    Taro.navigateTo({
      url: `/pages/webview/index?src=${encodeURIComponent('/hospital/familydoctor/pre')}`,
    });
  };

  return (
    <>
      <View className="goldcore-component flex">
        <View className="goldcore-component__col family-doctor" onClick={goToHyMall}>
          <View className="family-core rj-core" />
        </View>
        <View className="goldcore-component__col flex-wrap flex">
          <View className="col-row" onClick={goToWuYou}>
            <Image className="img" src={`${CDN_PREFIX}home/wu-you_7-10.webp`} />
          </View>
          <View className="col-row" onClick={goToInquiryFlow}>
            <Image className="img" src={`${CDN_PREFIX}home/rj-card_7-3.webp`} />
          </View>
        </View>
      </View>

      {doctorNormalServiceVisible && (
        <View className="home-popup-container popup-alert flex-xy">
          <View className="content">
            <View className="popup-bg">
              <View className="alert_content">非常抱歉，您的家庭医生将无法继续为您提供服务，请重新签约！</View>
              <View className="cancel-btn" onClick={handleGoToChatFamilyDoctor}>我知道了</View>
            </View>
          </View>
        </View>
      )}
    </>
  );
};

export default GoldCore;
