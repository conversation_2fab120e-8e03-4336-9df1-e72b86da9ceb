.goldcore-component {
  &__col {
    flex: 1;
    height: 316rpx;

    & + & {
      margin-left: 20rpx;
    }
    &.flex-wrap {
      flex-wrap: wrap;
      align-content: space-between;
    }

    &.family-doctor {
      background: linear-gradient(153deg, #ffeed1 0%, #ecca93 100%);
      position: relative;
      border-radius: 16rpx;
      overflow: hidden;
      .family-core {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        top: 0;
        padding: 30rpx 0 0 24rpx;
        color: #5f3312;
        font-size: 12px;
        line-height: 40rpx;
        background-position: 0 0;
        background-repeat: no-repeat;
        background-size: 100% auto;

        &.bind {
          background-size: 190rpx auto;
          background-position: 117% 100%;
        }

        &.rj-core {
          background-image: url(https://cdn-qcloud.zhongan.com/a00000/za-asclepius/applets/home/<USER>
        }
      }
    }

    .col-row {
      font-size: 0;
      width: 100%;
      min-height: 148rpx;
      border-radius: 10rpx;
      background-color: rgba(249, 227, 220, 0.5);
      .img {
        width: 100%;
        height: 148rpx;
      }
      &:last-child{
        background-color: rgba(252, 242, 204, .5);
      }
    }
  }
}
.home-popup-container.popup-alert {
    // These styles target a global class, so we keep it this way
  .popup-bg {
    background: #fff;
    border-radius: 16rpx;
    position: static !important;
    display: block !important;
  }
  .content{
    height: auto !important;
  }
  .alert_content{
    padding:60rpx 40rpx;
    color: #666;
  }
  .cancel-btn {
    line-height: 90rpx;
    text-align: center;
    border-top: 1rpx solid #e6e6e6;
    color: #00A864;
  }
}
