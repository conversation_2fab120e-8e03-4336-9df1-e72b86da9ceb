import { View, ScrollView, Image, Text } from '@tarojs/components';
import Taro, { useDidShow } from '@tarojs/taro';
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';

// 1. 直接使用您已迁移的 utils 工具函数，并更新为绝对路径导入
import { fetchJson } from '@/utils/fetch';
import { xflowPushEvent } from '@/utils/pageTrack';
import { CDN_PREFIX } from '@/utils/staticData';
import { webviewUrl } from '@/utils/config'; // 引入 config 以获取 webviewUrl

import './index.scss';

// 类型定义
interface Doctor {
  staffNo: string;
  headPortrait: string;
  staffName: string;
  staffProfessionalName?: string;
  workDepartmentName?: string;
  originalWorkOrgName?: string;
  workSeniority?: number;
  workDepartment: string;
  staffProfessionalTitle: string;
}

interface RootState {
  resource: {
    PROFESSIONALTITLE_OBJ: { [key: string]: string };
    DEPARTMENT_OBJ: { [key: string]: string };
  };
}

const GoodDoctor: React.FC = () => {
  const [doctorList, setDoctorList] = useState<Doctor[]>([]);
  const [visible, setVisible] = useState(false);
  const [isShowMore, setIsShowMore] = useState(false);

  const { PROFESSIONALTITLE_OBJ, DEPARTMENT_OBJ } = useSelector(
    (state: RootState) => state.resource || {}
  );

  // 从 config 中获取 webviewUrl
  useEffect(() => {
    // setWebviewUrl(config.webviewUrl);
  }, []);

  const getDoctorList = async () => {
    try {
      const res = await fetchJson({
        url: '/zaApi/v1/patient/doctor/list/page',
        method: 'POST',
        needLoading: false, // 遵循原组件逻辑
        data: {
          option: { isQueryMedicalStaffService: true, isQueryHeadPortrait: true },
          isInquiry: 'Y',
          productId: 1,
          currentPage: 1,
          pageSize: 3,
          staffTypes: ['doctor'],
          staffAttributeList: [1, 2],
        },
      });

      if (res.code === '0' && res.result) {
        const { resultList = [], totalItem = 0 } = res.result;
        const processedList = resultList.map((k: Doctor) => ({
          ...k,
          workDepartmentName: DEPARTMENT_OBJ?.[k.workDepartment],
          staffProfessionalName: PROFESSIONALTITLE_OBJ?.[k.staffProfessionalTitle],
        }));

        setDoctorList(processedList);
        setIsShowMore(totalItem > 3);
        setVisible(processedList.length > 0);
      }
    } catch (error) {
      console.error('获取医生列表失败:', error);
      setVisible(false);
    }
  };

  useDidShow(() => {
    getDoctorList();
  });

  const handleJumpToDoctor = (item: Doctor) => {
    xflowPushEvent({
      eventTag: 'ZAHLWYY_SY',
      text: '首页',
      attributes: `首页_众安好医_${item.staffName}医生`,
    });
    // 2. 遵循您要求的跳转逻辑，保持和原来一致
    Taro.navigateTo({
      url: `/pages/webview/index?src=${encodeURIComponent(
        `/hospital/doctordetail?staffNo=${item.staffNo}`
      )}`,
    });
  };

  const handleJumpToDoctorList = () => {
    xflowPushEvent({
      eventTag: 'ZAHLWYY_SY',
      text: '首页',
      attributes: '首页_众安好医_查看更多',
    });
    Taro.navigateTo({
      url: `/pages/webview/index?src=${encodeURIComponent('/hospital/doctorlist')}`,
    });
  };

  if (!visible) {
    return null;
  }

  return (
    <View
      className="gooddoctor-component flex"
      style={{ backgroundImage: `url(${CDN_PREFIX}common/doctor-bg.png)` }}
    >
      <View className="gooddoctor-component__body">
        <ScrollView className="gooddoctor-component__scroll" scrollX>
          <View className="gooddoctor-component__list">
            {doctorList.map((item) => (
              <View className="item" key={item.staffNo} onClick={() => handleJumpToDoctor(item)}>
                <View className="doctor-item flex">
                  <Image
                    className="doctor-icon"
                    src={`${webviewUrl}${item.headPortrait}@s5`}
                  />
                  <View className="doctor-base">
                    <View className="doctor-flex flex">
                      <Text className="name">{item.staffName}</Text>
                      {item.staffProfessionalName && (
                        <Text className="tag">{item.staffProfessionalName}</Text>
                      )}
                      {item.workDepartmentName && (
                        <Text className="tag">{item.workDepartmentName}</Text>
                      )}
                    </View>
                    <View className="doctor-intro ellipsis">
                      {item.originalWorkOrgName ? (
                        `原${item.originalWorkOrgName}`
                      ) : item.workSeniority ? (
                        <Text>从业<Text className="light">{item.workSeniority}</Text>余年</Text>
                      ) : null}
                    </View>
                  </View>
                </View>
              </View>
            ))}
            {isShowMore && (
              <View className="item more-doctor" onClick={handleJumpToDoctorList}>
                <View>查看</View>
                更多
              </View>
            )}
          </View>
        </ScrollView>
      </View>
    </View>
  );
};

export default GoodDoctor;
