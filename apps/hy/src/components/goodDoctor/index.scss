.gooddoctor-component {
  background-size: 100% auto;
  padding: 95rpx 30rpx 0;
  height: 280rpx;
  margin-top: 30rpx;

  &__body {
    overflow: hidden;
  }

  &__list {
    white-space: nowrap;
    min-width: 100%;

    .item {
      display: inline-block;
      background: #fff;
      min-width: 460rpx;
      border-radius: 4px;
      border: 1px solid #f0f0f0;
      padding: 30rpx 20rpx 24rpx;
      overflow: hidden;
      min-height: 150rpx;
      &:not(:last-child) {
        margin-right: 20rpx;
      }
    }

    .doctor-item {
      display: flex;
    }

    .doctor-icon {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
    }

    .doctor-base {
      margin-left: 25rpx;
      line-height: 40rpx;
      overflow: hidden;
    }

    .doctor-flex {
      display: flex;
      align-content: center;

      .name {
        font-size: 34rpx;
      }

      .tag {
        line-height: 36rpx;
        border-radius: 4rpx;
        border: 1px solid #00bc70;
        color: #00bc70;
        padding: 0 10rpx;
        font-size: 24rpx;
        transform: scale(0.9);
      }
    }

    .doctor-intro {
      margin-top: 12rpx;
      font-size: 26rpx;
      height: 40rpx;
    }
    .more-doctor {
      height: 150rpx;
      width: 100rpx;
      padding-left: 10rpx;
      padding-right: 10rpx;
      text-align: center;
      color: #999;
      font-size: 26rpx;
    }
  }
}
