import React, { useState, useEffect, useCallback } from 'react';
import { View, Image, Text } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useSelector, useDispatch } from 'react-redux';
import { xflowPushEvent } from '@/utils/pageTrack';
import { CDN_PREFIX } from '@/utils/staticData';
import { updateFamilyDoctorInfo } from '@/store/actions/familyDoctor';
import storage, { everyDayOnceExpiredStorage } from '@/utils/storage';
import format from '@/utils/format';
import './index.scss';

const CONTINUED_FEE_POPUP_STATIC_VARIABLE = 'isHasContinuedFeePopup';

const useCountdown = (endTimeStr, onTick, onEnd) => {
  useEffect(() => {
    if (!endTimeStr) return;

    const endTime = new Date(endTimeStr.replace(/-/g, '/')).getTime();

    const intervalId = setInterval(() => {
      const now = Date.now();
      const diff = endTime - now;

      if (diff <= 0) {
        clearInterval(intervalId);
        onEnd();
        return;
      }

      const baseDiffer = diff / 1000;
      const hour = format.patch(Math.floor(baseDiffer / 60 / 60));
      const minute = format.patch(Math.floor((baseDiffer / 60) % 60));
      const seconds = format.patch(Math.floor(baseDiffer % 60));
      onTick({ hour, minute, seconds });
    }, 1000);

    return () => {
      clearInterval(intervalId);
    };
  }, [endTimeStr, onTick, onEnd]);
};


const FamilyContinuedFee: React.FC = () => {
  const favourCountdownTime = useSelector((state: any) => state.familyDoctors.favourCountdownTime);
  const dispatch = useDispatch();

  const [continuedFeeVisible, setContinuedFeeVisible] = useState(false);
  const [isFirstDraw, setIsFirstDraw] = useState('');
  const [countTimeData, setCountTimeData] = useState({ hour: '00', minute: '00', seconds: '00' });

  useEffect(() => {
    const firstDrawStatus = storage.get('isFirstDrawTrialServpack') || 'N';
    setIsFirstDraw(firstDrawStatus);

    if (firstDrawStatus === 'Y') {
      setContinuedFeeVisible(true);
      return;
    }

    const isHasPopupToday = storage.get(CONTINUED_FEE_POPUP_STATIC_VARIABLE) || 0;
    if (favourCountdownTime && !isHasPopupToday) {
      const endTime = new Date(favourCountdownTime.replace(/-/g, '/')).getTime();
      if (endTime > Date.now()) {
        everyDayOnceExpiredStorage(CONTINUED_FEE_POPUP_STATIC_VARIABLE);
        setContinuedFeeVisible(true);
      } else {
        dispatch(updateFamilyDoctorInfo({ tryOutOrContinuedFeePoup: false }));
      }
    }
  }, [favourCountdownTime, dispatch]);

  const closePopup = useCallback(() => {
    setContinuedFeeVisible(false);
    dispatch(updateFamilyDoctorInfo({ tryOutOrContinuedFeePoup: false }));
    if (isFirstDraw === 'Y') {
        storage.set('isFirstDrawTrialServpack', 'N');
    }
  }, [dispatch, isFirstDraw]);

  useCountdown(
    continuedFeeVisible && isFirstDraw !== 'Y' ? favourCountdownTime : null,
    setCountTimeData,
    closePopup
  );

  const goToChatfamilyDoctor = useCallback((value: string) => {
    xflowPushEvent({
      eventTag: 'ZAHLWYY_SY',
      text: '首页',
      attributes: `首页_家庭医生弹窗_${value}`,
    });
    closePopup();
    Taro.navigateTo({
      url: `/pages/webview/index?src=${encodeURIComponent('/hospital/familydoctor/pre')}`,
    });
  }, [closePopup]);

  const closeModal = useCallback(() => {
    xflowPushEvent({
      eventTag: 'ZAHLWYY_SY',
      text: '首页',
      attributes: '首页_家庭医生弹窗_再考虑',
    });
    closePopup();
  }, [closePopup]);

  useEffect(() => {
    return () => {
      dispatch(updateFamilyDoctorInfo({ tryOutOrContinuedFeePoup: false }));
      storage.set('isFirstDrawTrialServpack', 'N');
    }
  }, [dispatch]);


  if (!continuedFeeVisible) {
    return null;
  }

  return (
    <View className="home-popup-container flex-xy">
      <View className="content">
        {isFirstDraw === 'Y' ? (
          <View className="popup-bg">
            <Image className="exclusive-bg" src={`${CDN_PREFIX}common/family-exclusive-poup.png`} />
            <View className="bottom-btn fee-close" onClick={() => goToChatfamilyDoctor('我知道了')}></View>
          </View>
        ) : (
          <View className="popup-bg ad-continued-fee">
            <View
              className="fee-countdown flex"
              style={{ backgroundImage: `url(${CDN_PREFIX}common/family-fee-poup01.png)` }}
            >
              <View className="fee-time"><Text>{countTimeData.hour}</Text>时</View>
              <View className="fee-time minute"><Text>{countTimeData.minute}</Text>分</View>
              <View className="fee-time"><Text>{countTimeData.seconds}</Text>秒</View>
            </View>
            <Image className="fee-bg" src={`${CDN_PREFIX}common/family-fee-poup02.png`} />
            <View className="fee-footer flex-y">
              <View className="ft-btn" onClick={closeModal}> 再考虑 </View>
              <View className="ft-btn confirm" onClick={() => goToChatfamilyDoctor('立即开通')}> 立即开通 </View>
            </View>
          </View>
        )}
      </View>
    </View>
  );
};

export default FamilyContinuedFee;
