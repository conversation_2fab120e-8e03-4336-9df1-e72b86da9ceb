.home-popup-container {
  .fee-close {
    bottom: 40rpx !important;
  }
  .exclusive-bg {
    width: 600rpx;
    height: 760rpx;
  }

  .ad-continued-fee {
    position: relative;
    padding-top: 34rpx;
    height: auto !important;

    .fee-bg {
      height: 565rpx;
      width: 600rpx;
    }

    .fee-countdown {
      position: absolute;
      top: 0;
      left: 40rpx;
      right: 40rpx;
      background-repeat: no-repeat;
      background-size: 100% auto;
      height: 98rpx;
      z-index: 2;
      padding: 0 20rpx 12rpx 0;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }

    .fee-time {
      width: 50px;
      text-align: center;
      font-size: 12px;
      color: #fff;

      &.minute {
        margin-left: 10rpx;
      }

      strong {
        font-size: 32rpx;
        padding-right: 8rpx;
      }
    }

    .fee-footer {
      position: absolute;
      left: 35rpx;
      right: 35rpx;
      bottom: 50rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .ft-btn {
        line-height: 88rpx;
        width: 228rpx;
        border-radius: 100rpx;
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.8);
        font-weight: 600;
        font-size: 32rpx;
        color: #fff;

        &.confirm {
          width: 288rpx;
          background: linear-gradient(270deg, #ffcb62 0%, #ffefd4 100%);
          color: #934300;
          border: none;
        }
      }
    }
  }
}
