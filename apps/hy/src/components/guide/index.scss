.guide_wraper {
  position: fixed;
  width: 100%;
  top: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 101;
  display: none;

  &.show {
    display: block;
  }

  .guide_modal {
    position: fixed;
    width: 446rpx;
    height: 405rpx;
    background: #fff;
    border-radius: 16rpx;
    right: 30rpx;
    padding: 30rpx;
    box-sizing: border-box;
    font-size: 26rpx;
    color: rgba(0, 0, 0, 0.8);

    &::before {
      content: '';
      position: absolute;
      width: 20rpx;
      height: 20rpx;
      transform: rotate(45deg);
      right: 100rpx;
      top: -8rpx;
      background: inherit;
    }

    .right_top_icon {
      vertical-align: -5%;
      width: 44rpx;
      height: 24rpx;
      margin: 0 6rpx;
    }

    .img_guide {
      display: block;
      width: 243rpx;
      height: 178rpx;
      margin: 16rpx auto 40rpx;
    }

    .guide_num {
      position: absolute;
      left: 0;
      top: 4rpx;
      display: inline-block;
      width: 28rpx;
      height: 28rpx;
      border-radius: 50%;
      text-align: center;
      line-height: 28rpx;
      font-size: 20rpx;
      background: rgba(0, 0, 0, 0.6);
      color: #fff;
    }

    .guide_item {
      position: relative;
      padding-left: 40rpx;
      line-height: 1.5;
    }
  }
}
