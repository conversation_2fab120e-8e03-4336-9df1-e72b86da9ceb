import { View, Image, Text } from '@tarojs/components';
import Taro from '@tarojs/taro';
import React, { useState, useEffect } from 'react';

import rightTopIcon from '@/images/right_top_icon.svg';
import imgGuide from '@/images/img_guide.png';
import './index.scss';

interface GuideProps {
  show: boolean;
  onClose: () => void;
}

const Guide: React.FC<GuideProps> = ({ show, onClose }) => {
  const [modalTop, setModalTop] = useState(0);

  useEffect(() => {
    if (show) {
      if (Taro.canIUse('getMenuButtonBoundingClientRect')) {
        const sysInfo = Taro.getSystemInfoSync();
        const rect = Taro.getMenuButtonBoundingClientRect();

        if (rect && rect.bottom) {
          const navBarHeight = rect.bottom + 16 / (sysInfo.pixelRatio || 2);
          setModalTop(navBarHeight + 1);
        }
      }
    }
  }, [show]);

  const handleClose = () => {
    onClose();
  };

  const handleContentClick = (e: any) => {
    e.stopPropagation();
  };

  if (!show) {
    return null;
  }

  return (
    <View className="guide_wraper show" onClick={handleClose}>
      {modalTop > 0 && (
        <View
          className="guide_modal"
          onClick={handleContentClick}
          style={{ top: `${modalTop}px` }}
        >
          <View className="guide_item">
            <Text className="guide_num">1</Text>
            点击右上角按钮
            <Image className="right_top_icon" src={rightTopIcon} />
            选择添加到我的小程序
          </View>
          <Image className="img_guide" src={imgGuide} />
          <View className="guide_item">
            <Text className="guide_num">2</Text>
            添加后可快速咨询自己的医生
          </View>
        </View>
      )}
    </View>
  );
};

export default Guide;
