import React, { useState, useEffect } from 'react';
import { View, Image, Button } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { fetchJson } from '@/utils/fetch';
import { Serialize } from '@/utils/serialization';
import { webviewUrl } from '@/utils/config';
// import StaticToast from '@/components/common/toast';

import inquiryIcon from '@/images/icon_check_inquiry.png';
import './index.scss';

interface CheckInquiryProps {
  onStatusChange?: (hasInquiry: boolean) => void;
}

const CheckInquiry: React.FC<CheckInquiryProps> = ({ onStatusChange }) => {
  const [visible, setVisible] = useState(false);
  const [inquiryList, setInquiryList] = useState<any[]>([]);

  const textInquiryToAwait = (query: any) => {
    const url = '/hospital/await';
    const appletUrl = encodeURIComponent(`${webviewUrl}${url}`) + `&param=${JSON.stringify(query)}`;
    Taro.navigateTo({
      url: `/pages/webview/index?src=${appletUrl}`,
    });
  };

  const continueInquiry = () => {
    const firstInquiry = inquiryList[0] || {};
    const inquiryNum = inquiryList.length;
    const { inquiryStatus, inquiryType = '', inquiryId, inquiryNo, inquiryTimingType, platformCode } = firstInquiry;

    if (inquiryNum > 1 || inquiryStatus === 1) {
      Taro.navigateTo({
        url: `/pages/webview/index?src=${encodeURIComponent('/hospital/myinquiry')}`,
      });
    } else {
      if (!inquiryNo && !inquiryId) {
        // StaticToast.error('缺少问诊单号');
        Taro.showToast({ title: '缺少问诊单号', icon: 'none' });
        return;
      }
      const query = { inquiryId, inquiryNo };
      if (platformCode === 'ZA' && inquiryTimingType === 'appointment' && inquiryType === 'V') {
        const nextSearch = Serialize(query);
        const path = `/pages/video/selfChat?${nextSearch}&inquiryStatus=${inquiryStatus}`;
        Taro.navigateTo({
          url: path,
        });
      } else {
        textInquiryToAwait(query);
      }
    }
  };

  const getInquiryList = () => {
    fetchJson({
      url: '/zaApi/v1/patient/inquiry/list',
      method: 'POST',
      needLoading: false,
      data: {
        inquiryStatusList: [1, 2, 3, 7, 8, 10, 11],
        inquiryTypes: ["I", "V"],
        excludeInquiryTimingTypeList: ['drug_replenish', 'mentalConsult'],
      },
    }).then((res: any) => {
      if (res.code === '0') {
        const result = res.result || [];
        setInquiryList(result);
        const isVisible = !!result.length;
        setVisible(isVisible);
        // if (onStatusChange) {
        //   onStatusChange(isVisible);
        // }
      }
    });
  };

  useEffect(() => {
    getInquiryList();
  }, []);

  if (!visible) {
    return null;
  }

  return (
    <View className="check_inquiry_comp flex-y">
      <Image className='check_inquiry_img' src={inquiryIcon} />
      <p className='check_inquiry_text flex-grow'>您有{inquiryList.length}个未完成的问诊单</p>
      <Button className='btn_continue' onClick={continueInquiry}>前往查看</Button>
    </View>
  );
};

export default CheckInquiry;
