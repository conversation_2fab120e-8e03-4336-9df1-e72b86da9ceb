import { View, Image } from '@tarojs/components';
import Taro from '@tarojs/taro';
import React from 'react';

import closeIcon from '@/images/icon_close.png';
import './index.scss';

interface GJData {
  isInsured: boolean;
  productName?: string;
  effectiveDate?: string;
}

interface GJModalProps {
  show: boolean;
  gjData: GJData;
  onClose: () => void;
}

const GJModal: React.FC<GJModalProps> = ({ show, gjData, onClose }) => {
  const handleClose = () => {
    onClose();
  };

  const handleConfirm = () => {
    onClose();

    if (gjData && !gjData.isInsured) {
      Taro.navigateToMiniProgram({
        appId: 'wxbac45cc1588a5a75',
        path: 'pages/index/index',
        envVersion: 'trial',
      });
    }
  };

  const handleContentClick = (e: any) => {
    e.stopPropagation();
  };

  if (!show) {
    return null;
  }

  return (
    <View
      className="gj_modal show"
      onClick={handleClose}
    >
      <View
        className="gj_modal__content"
        onClick={handleContentClick}
      >
        <Image
          className="gj_modal__close"
          src={closeIcon}
          onClick={handleClose}
        />
        <View className="gj_modal__content_wrap">
          <View className="gj_modal__title">商城暂不能进入</View>

          {gjData.isInsured ? (
            <View className="gj_modal__desc">
              <View>您投保的{gjData.productName}还未过等待期</View>
              <View>{gjData.effectiveDate} 可进入商城购药</View>
            </View>
          ) : (
            <View className="gj_modal__desc">
              <View>现仅限有{gjData.productName}购药权益的用户进入</View>
            </View>
          )}
        </View>
        <View
          className="gj_modal__bottom"
          onClick={handleConfirm}
        >
          {gjData.isInsured ? '我知道了' : '获取购药权益'}
        </View>
      </View>
    </View>
  );
};

export default GJModal;
