import { View, Image, Input, Checkbox, CheckboxGroup, Label, Button, Text } from '@tarojs/components';
import Taro from '@tarojs/taro';
import React, { useState, useEffect } from 'react';

import { fetchJson } from '@/utils/fetch';
import validate from '@/utils/validate';
import storage from '@/utils/storage';
import { xflowPushEvent } from '@/utils/pageTrack';

import './index.scss';

interface UserInfo {
  name?: string;
  certiNo?: string;
  accountPhone?: string;
}

interface CarInsureModalProps {
  show: boolean;
  userInfo: UserInfo;
  onClose: () => void;
  onAuthSuccess: () => void;
}

const CarInsureModal: React.FC<CarInsureModalProps> = ({ show, userInfo, onClose, onAuthSuccess }) => {
  const [name, setName] = useState('');
  const [certiNo, setCertiNo] = useState('');
  const [checked, setChecked] = useState(false);

  useEffect(() => {
    if (userInfo) {
      setName(userInfo.name || '');
      setCertiNo(userInfo.certiNo || '');
    }
  }, [userInfo]);

  const handleNameInput = (e) => setName(e.detail.value);
  const handleCertiInput = (e) => setCertiNo(e.detail.value);
  const handleCheckboxChange = (e) => setChecked(e.detail.value.length > 0);

  const handleReadAgreement = async (type: 'memberAgreement' | 'participationAgreement') => {
    const agreements = {
      memberAgreement: {
        url: 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/pdf/memberServiceAgreement.doc',
      },
      participationAgreement: {
        url: 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/pdf/MemberParticipationAgreement.doc',
      },
    };
    const { url } = agreements[type];

    Taro.showLoading({ title: '加载中', mask: true });
    try {
      const downloadRes = await Taro.downloadFile({ url });
      if (downloadRes.statusCode === 200) {
        await Taro.openDocument({ filePath: downloadRes.tempFilePath, showMenu: true });
      } else {
        Taro.showToast({ title: '协议打开失败', icon: 'none' });
      }
    } catch (error) {
      Taro.showToast({ title: '协议打开失败', icon: 'none' });
    } finally {
      Taro.hideLoading();
    }
  };

  const sendChannelRealNameInfo = async (options: { name: string; certiNo: string }) => {
    const res = await fetchJson({
      url: '/zaApi/v1/patient/patient/sendChannelRealNameInfo',
      method: 'POST',
      needLoading: true,
      data: { patientName: options.name, patientCertNo: options.certiNo },
    });

    if (res.code === '0') {
      Taro.showToast({ title: '领取成功', icon: 'success', duration: 2000 });
      onClose();
      if (!userInfo.name) {
        onAuthSuccess();
      }
    }
  };

  const createPatientProfile = async () => {
    const patientNoRes = await fetchJson({
      method: "POST",
      url: '/zaApi/v1/patient/bizno/getByType',
      needLoading: true,
      data: { bizNoType: 'PATIENT_NO' }
    });

    if (!patientNoRes || patientNoRes.code !== '0') {
      Taro.showToast({ title: '实名异常，请重新进入', icon: 'none' });
      return;
    }

    const validationResult = validate.isIdCard(certiNo);
    const saveRes = await fetchJson({
      url: '/zaApi/v1/patient/patient/save',
      method: 'POST',
      needLoading: true,
      data: {
        patientRelation: 1,
        patientBirthday: validationResult.birthday,
        patientGender: validationResult.sex,
        patientName: name,
        patientCertNo: certiNo,
        patientCertType: 'I',
        patientNo: patientNoRes.result,
      },
    });

    if (saveRes.code === '0') {
      storage.set('realName', 'Y');
      await sendChannelRealNameInfo({ name, certiNo });
    }
  };

  const handleSubmit = async () => {
    xflowPushEvent({
      eventTag: 'ZAHLWYY_SY',
      text: '首页',
      attributes: '首页_车险弹窗_加入会员',
    });

    if (!checked) {
      Taro.showToast({ title: '请勾选协议', icon: 'none' });
      return;
    }

    const currentName = name || userInfo.name;
    const currentCertiNo = certiNo || userInfo.certiNo;

    if (validate.isEmpty(currentName)) {
      Taro.showToast({ title: '您还未输入您的姓名', icon: 'none' });
      return;
    }
    if (!validate.isUsername(currentName)) {
      Taro.showToast({ title: '您输入的姓名格式错误', icon: 'none' });
      return;
    }
    if (validate.isEmpty(currentCertiNo)) {
      Taro.showToast({ title: '您还未输入您的证件号码', icon: 'none' });
      return;
    }
    if (!validate.isIdCard(currentCertiNo)) {
      Taro.showToast({ title: '您输入的证件号码格式错误', icon: 'none' });
      return;
    }

    Taro.showLoading({ title: '处理中', mask: true });
    try {
      if (userInfo.name && userInfo.certiNo) {
        await sendChannelRealNameInfo({ name: userInfo.name, certiNo: userInfo.certiNo });
      } else {
        await createPatientProfile();
      }
    } catch (error) {
      console.error("Submit failed:", error);
    } finally {
      Taro.hideLoading();
    }
  };

  if (!show) return null;

  return (
    <View className="car_insure_modal flex-xy">
      <View className="car_insure_modal__content">
        <View className="header">
          <Image className="header_img" src="https://cdn-qcloud.zhongan.com/a00000/za-asclepius/images/carinsure_modal_title.png" />
        </View>
        <View className="form_wrap">
          <View className="input_wrap flex-y">
            <Label className="label">姓名</Label>
            <Input className="input" placeholder="请输入你的姓名" value={name} disabled={!!userInfo.name} onInput={handleNameInput} maxlength={15} placeholder-style="color:#CFCFCF;" />
          </View>
          <View className="input_wrap flex-y">
            <Label className="label">身份证号</Label>
            <Input className="input" placeholder="请输入你的身份证号码" value={certiNo} disabled={!!userInfo.certiNo} onInput={handleCertiInput} maxlength={18} placeholder-style="color:#CFCFCF;" />
          </View>
          <View className="input_wrap flex-y">
            <Label className="label">手机号码</Label>
            <Input className="input" placeholder="请输入你的手机号码" value={userInfo.accountPhone} disabled maxlength={11} placeholder-style="color:#CFCFCF;" />
          </View>

          <View className="checkbox_wrap">
            <CheckboxGroup onChange={handleCheckboxChange}>
              <Label>
                <Checkbox className="checkbox" value="notice" checked={checked} />本人已了解并确认
                <Text className='link_agreement' onClick={() => handleReadAgreement('memberAgreement')}>《众安医管家会员协议》</Text>、
                <Text className='link_agreement' onClick={() => handleReadAgreement('participationAgreement')}>《众安好司机用户参与协议》</Text>
              </Label>
            </CheckboxGroup>
          </View>

          <Button className="btn_join" hoverClass="join_hover_button" onClick={handleSubmit}>加入会员免费领取权益</Button>
        </View>
        <View className="rights_statement">
          <View>权益内容说明：</View>
          <View>1.本权益内容仅限众安好司机领取，凭卡密进行兑换，转让无效；</View>
          <View>2.医管家权益内容包括：用户可获得尊享私人医管家权益，享全年无限次“图文问诊+视频问诊+购药优惠+送药上门+体检报告解读+健康知识科普”服务，具体以医管家提供的服务内容为准。</View>
        </View>
      </View>
    </View>
  );
};

export default CarInsureModal;
