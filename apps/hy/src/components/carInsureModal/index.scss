.car_insure_modal {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 9999;
  display: none;
  justify-content: center;
  align-items: center;

  &.flex-xy {
    display: flex;
  }

  &__content {
    position: relative;
    width: 600rpx;
    background: #fff;
    border-radius: 16rpx;

    .header {
      height: 128rpx;
      font-size: 0;

      .header_img {
        width: 600rpx;
        height: 128rpx;
      }
    }

    .form_wrap {
      padding: 24rpx 30rpx 0;
    }

    .input_wrap {
      width: 540rpx;
      height: 80rpx;
      margin-bottom: 20rpx;
      background: rgba(0, 0, 0, 0.03);
      border-radius: 8rpx;
      font-size: 28rpx;
      color: #1e1e1e;
      display: flex;
      align-items: center;

      .label {
        width: 142rpx;
        margin-left: 20rpx;
        font-weight: 400;
        color: #333333;
      }
    }

    .checkbox_wrap {
      position: relative;
      margin-top: 24rpx;
      font-size: 24rpx;
      color: #666666;
      padding-left: 38rpx;
    }

    .checkbox {
      position: absolute;
      left: 0;
      top: 2rpx;
    }

    .link_agreement {
      color: #00a864;
    }

    .rights_statement {
      padding: 30rpx;
      font-size: 24rpx;
      color: #999999;
      line-height: 1.2;
    }

    .btn_join {
      margin-top: 30rpx;
      width: 540rpx;
      height: 88rpx;
      background: #ff7240;
      border-radius: 44rpx;
      font-size: 34rpx;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #ffffff;
    }

    .join_hover_button {
      opacity: 0.6;
    }
  }

  &__close {
    position: absolute;
    right: 18rpx;
    top: 18rpx;
    width: 26rpx;
    height: 26rpx;
  }
}
