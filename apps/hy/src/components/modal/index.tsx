import Taro from '@tarojs/taro';
import { View } from '@tarojs/components';
import React, { PropsWithChildren } from 'react';
import './index.scss';

interface ModalProps {
  show: boolean;
  maskClick?: boolean;
  onClose: () => void;
}

const Modal: React.FC<PropsWithChildren<ModalProps>> = (props) => {
  const { show, maskClick = true, onClose, children } = props;

  const handleMaskClick = () => {
    if (maskClick) {
      onClose();
    }
  };

  const handleContentClick = (e: any) => {
    e.stopPropagation();
  };

  // We add 'flex-xy' class manually to center the content like the original component
  // The original wepy component has a root class `flex-xy` which is not present in the new version.
  // To keep the styles consistent, we need to add a wrapper view with the `flex-xy` class
  const modalClasses = `comp_modal ${show ? 'show' : ''}`;

  if (!show) {
    return null;
  }

  return (
    <View className={modalClasses} onClick={handleMaskClick}>
      <View onClick={handleContentClick}>{children}</View>
    </View>
  );
};

export default Modal;
