<?xml version="1.0" encoding="UTF-8"?>
<svg width="66px" height="66px" viewBox="0 0 66 66" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon / 个人中心 权益 / 医管家</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="98.6451049%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" stop-opacity="0.61060424" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.45902535" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="14.5429363%" y1="0%" x2="85.4570637%" y2="100%" id="linearGradient-2">
            <stop stop-color="#F0C99A" offset="0%"></stop>
            <stop stop-color="#D3A26A" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="个人中心-未登录" transform="translate(-112.000000, -454.000000)">
            <g id="icon-/-个人中心-权益-/-医管家" transform="translate(112.000000, 454.000000)">
                <rect id="区域" fill="#F2F2F2" opacity="0" x="0" y="0" width="66" height="66"></rect>
                <rect id="区域" stroke-opacity="0.61098667" stroke="#E5CFB0" fill-opacity="0.6" fill="url(#linearGradient-1)" x="0.5" y="0.5" width="65" height="65" rx="32.5"></rect>
                <path d="M32.61875,14 C35.5818824,14 38.5475058,15.185655 41.5156202,17.5569651 C42.6644295,18.4747778 43.3333333,19.8653511 43.3333333,21.3357739 L43.333118,29.9589885 C43.3098901,33.4189269 41.4093516,36.4616647 38.5376631,38.2383636 C44.6451544,39.6702966 49,43.4046368 49,47.78575 C49,53.40475 17,53.40475 17,47.78575 C17,43.5187394 21.1309624,39.8652512 26.988565,38.3541487 C24.0821864,36.6396264 22.1216122,33.6465908 22.0054584,30.2175466 L22.001,29.959 L22,29.987055 L22,21.3118252 C22.0000024,19.8535218 22.6579759,18.4730637 23.7907797,17.5546934 C26.7139073,15.1848978 29.656564,14 32.61875,14 Z M32.9917341,19 C32.4394494,19 31.9917341,19.4477153 31.9917341,20 L31.9917341,20 L31.991,22.958 L29,22.9586707 C28.4477153,22.9586707 28,23.406386 28,23.9586707 C28,24.5109555 28.4477153,24.9586707 29,24.9586707 L29,24.9586707 L31.991,24.958 L31.9917341,28 C31.9917341,28.5522847 32.4394494,29 32.9917341,29 C33.5440189,29 33.9917341,28.5522847 33.9917341,28 L33.9917341,28 L33.991,24.958 L37,24.9586707 C37.5522847,24.9586707 38,24.5109555 38,23.9586707 C38,23.406386 37.5522847,22.9586707 37,22.9586707 L37,22.9586707 L33.991,22.958 L33.9917341,20 C33.9917341,19.4477153 33.5440189,19 32.9917341,19 Z" id="形状结合" fill="url(#linearGradient-2)"></path>
            </g>
        </g>
    </g>
</svg>