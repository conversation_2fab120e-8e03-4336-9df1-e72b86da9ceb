<?xml version="1.0" encoding="UTF-8"?>
<svg width="66px" height="66px" viewBox="0 0 66 66" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>0903 / 首页权益icon / 重疾绿通</title>
    <defs>
        <linearGradient x1="21.4158239%" y1="0%" x2="78.5841761%" y2="100%" id="linearGradient-1">
            <stop stop-color="#F0C99A" offset="0%"></stop>
            <stop stop-color="#D3A26A" offset="100%"></stop>
        </linearGradient>
        <path d="M12.375,17.53125 C13.0625,15.125 13.75,12.71875 14.4375,10.3125" id="path-2"></path>
        <filter x="-169.7%" y="-48.5%" width="439.4%" height="197.0%" filterUnits="objectBoundingBox" id="filter-3">
            <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="-2" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.0603693182 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="0904" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="0-首页icon" transform="translate(-362.000000, -164.000000)">
            <g id="重疾绿通" transform="translate(362.000000, 164.000000)">
                <rect id="区域" fill="#F2F2F2" opacity="0" x="0" y="0" width="66" height="66"></rect>
                <rect id="区域" stroke="#E5CFB0" fill-opacity="0.24" fill="#FFFFFF" x="0.5" y="0.5" width="65" height="65" rx="32.5"></rect>
                <g id="编组-7" transform="translate(17.531250, 13.406250)">
                    <path d="M11.1527516,31.5606265 L2.66624062,31.0408256 C1.56373725,30.973297 0.724724437,30.0247986 0.792253043,28.9222953 C0.803906449,28.7320364 0.84269903,28.5444204 0.907453549,28.365141 C4.32255291,18.910094 7.73765226,9.455047 11.1527516,0 L23.8199309,0 C24.9245004,-4.2495073e-16 25.8199309,0.8954305 25.8199309,2 C25.8199309,2.2034201 25.7888971,2.40564972 25.7279007,2.59970942 L21.330774,16.5891257 L21.330774,16.5891257 L27.9355931,16.5891257 C29.0401626,16.5891257 29.9355931,17.4845562 29.9355931,18.5891257 C29.9355931,19.0263748 29.7923014,19.4515764 29.5276533,19.7996398 L12.4338851,42.28125 L12.4338851,42.28125 L11.1527516,31.5606265 Z" id="路径" fill="url(#linearGradient-1)"></path>
                    <g id="路径" stroke-linecap="round" stroke-linejoin="round">
                        <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                        <use stroke="#FBE7C0" stroke-width="2" xlink:href="#path-2"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>