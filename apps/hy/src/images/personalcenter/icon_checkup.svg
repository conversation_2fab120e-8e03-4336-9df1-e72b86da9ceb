<?xml version="1.0" encoding="UTF-8"?>
<svg width="36px" height="36px" viewBox="0 0 36 36" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>个人中心 / 体检记录 icon</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="个人中心1" transform="translate(-60.000000, -725.000000)">
            <g id="个人中心-/-体检记录-icon" transform="translate(60.000000, 725.000000)">
                <rect id="矩形" stroke="#979797" fill="#D8D8D8" opacity="0" x="0.5" y="0.5" width="35" height="35"></rect>
                <g id="编组-9" transform="translate(4.000000, 2.000000)">
                    <path d="M5,4.79571984 L5,1.42301278 C5,0.637104525 5.8954305,0 7,0 L21,0 C22.1045695,0 23,0.637104525 23,1.42301278 L23,5" id="路径" stroke="#666666" stroke-width="2" transform="translate(14.000000, 2.500000) scale(1, -1) translate(-14.000000, -2.500000) "></path>
                    <path d="M9,9 L26,9 C27.1045695,9 28,9.8954305 28,11 L28,30 C28,31.1045695 27.1045695,32 26,32 L9,32 C7.8954305,32 7,31.1045695 7,30 L7,11 C7,9.8954305 7.8954305,9 9,9 Z" id="矩形" fill="#D9F7EB"></path>
                    <path d="M26,1 C26.2761424,1 26.5261424,1.11192881 26.7071068,1.29289322 C26.8880712,1.47385763 27,1.72385763 27,2 L27,2 L27,30 C27,30.2761424 26.8880712,30.5261424 26.7071068,30.7071068 C26.5261424,30.8880712 26.2761424,31 26,31 L26,31 L2,31 C1.72385763,31 1.47385763,30.8880712 1.29289322,30.7071068 C1.11192881,30.5261424 1,30.2761424 1,30 L1,30 L1,2 C1,1.72385763 1.11192881,1.47385763 1.29289322,1.29289322 C1.47385763,1.11192881 1.72385763,1 2,1 L2,1 Z" id="矩形" stroke="#666666" stroke-width="2"></path>
                    <polyline id="路径-2" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" points="6 22.1213203 9.98165445 16.6750421 15.6838038 22.1213203 22 13.1213203"></polyline>
                </g>
            </g>
        </g>
    </g>
</svg>