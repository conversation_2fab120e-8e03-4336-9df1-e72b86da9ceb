<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>个人中心 / 问诊记录 icon</title>
    <defs>
        <linearGradient x1="58.5714286%" y1="100%" x2="58.5714286%" y2="0%" id="linearGradient-1">
            <stop stop-color="#FFB900" offset="0%"></stop>
            <stop stop-color="#FFEFBA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#ABC8F4" offset="0%"></stop>
            <stop stop-color="#64A7F1" offset="100%"></stop>
        </linearGradient>
        <path d="M27.2916306,17.2487373 C28.3962001,17.2487373 29.2916306,18.1441678 29.2916306,19.2487373 L29.2916306,23.2487373 L33.2916306,23.2487373 C34.3962001,23.2487373 35.2916306,24.1441678 35.2916306,25.2487373 C35.2916306,26.3533068 34.3962001,27.2487373 33.2916306,27.2487373 L29.2916306,27.2487373 L29.2916306,31.2487373 C29.2916306,32.3533068 28.3962001,33.2487373 27.2916306,33.2487373 C26.1870611,33.2487373 25.2916306,32.3533068 25.2916306,31.2487373 L25.2916306,27.2487373 L21.2916306,27.2487373 C20.1870611,27.2487373 19.2916306,26.3533068 19.2916306,25.2487373 C19.2916306,24.1441678 20.1870611,23.2487373 21.2916306,23.2487373 L25.2916306,23.2487373 L25.2916306,19.2487373 C25.2916306,18.1441678 26.1870611,17.2487373 27.2916306,17.2487373 Z" id="path-3"></path>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="个人中心1" transform="translate(-121.000000, -376.000000)">
            <g id="个人中心-/-问诊记录-icon" transform="translate(113.000000, 371.000000)">
                <rect id="矩形" stroke="#979797" fill="#D8D8D8" opacity="0" x="8.5" y="5.5" width="39" height="39"></rect>
                <path d="M26.270184,6.61853352 C35.9351671,6.61853352 43.770184,14.4535504 43.770184,24.1185335 C43.770184,32.5350049 37.8286602,39.563759 29.9111636,41.2392451 L26.3225993,45.9218344 L22.7553132,41.2654448 C14.7748822,39.6382571 8.77018395,32.5796315 8.77018395,24.1185335 C8.77018395,14.4535504 16.6052008,6.61853352 26.270184,6.61853352 Z" id="形状结合" fill="url(#linearGradient-1)" transform="translate(26.270184, 26.270184) rotate(-315.000000) translate(-26.270184, -26.270184) "></path>
                <g id="形状结合">
                    <use fill="url(#linearGradient-2)" xlink:href="#path-3"></use>
                    <use fill="#FFFFFF" xlink:href="#path-3"></use>
                </g>
            </g>
        </g>
    </g>
</svg>