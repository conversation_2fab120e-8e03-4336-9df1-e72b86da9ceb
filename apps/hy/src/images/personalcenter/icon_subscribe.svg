<?xml version="1.0" encoding="UTF-8"?>
<svg width="36px" height="36px" viewBox="0 0 36 36" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>个人中心 / 消息订阅 icon</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="3_个人中心-增加消息订阅设置入口" transform="translate(-61.000000, -1308.000000)">
            <g id="编组-2" transform="translate(30.000000, 1276.000000)">
                <g id="个人中心-/-消息订阅-icon" transform="translate(31.000000, 32.000000)">
                    <rect id="矩形" stroke="#979797" fill="#D8D8D8" opacity="0" x="0.5" y="0.5" width="35" height="35"></rect>
                    <g id="编组-3" transform="translate(3.000000, 2.000000)">
                        <path d="M7,6 L30,6 C31.1045695,6 32,6.8954305 32,8 L32,30.9655868 C32,32.0701563 31.1045695,32.9655868 30,32.9655868 C29.728874,32.9655868 29.4605788,32.9104606 29.2114179,32.8035581 L18.83338,28.350858 C18.31281,28.1275073 17.7219438,28.1353825 17.2075113,28.3725278 L7.83728664,32.6920551 C6.83417103,33.1544758 5.64611916,32.7161559 5.18369852,31.7130403 C5.06267213,31.4505013 5,31.1648455 5,30.8757537 L5,8 C5,6.8954305 5.8954305,6 7,6 Z" id="矩形" fill="#D9F7EB"></path>
                        <path d="M28,1 C28.2761424,1 28.5261424,1.11192881 28.7071068,1.29289322 C28.8880712,1.47385763 29,1.72385763 29,2 L29,2 L29,29.0447899 C29,29.3209323 28.8880712,29.5709323 28.7071068,29.7518967 C28.5261424,29.9328611 28.2761424,30.0447899 28,30.0447899 L28,30.0447899 L16.1300246,25.3690367 C15.4148938,25.0826861 14.6170153,25.0823995 13.9016789,25.3682363 L13.9016789,25.3682363 L2.37105745,29.9756855 C2.1146289,30.0781502 1.8409444,30.0669763 1.60575087,29.9660792 C1.37055734,29.865182 1.17385477,29.6745616 1.07139009,29.418133 L1.07139009,29.418133 L1,2 C1,1.72385763 1.11192881,1.47385763 1.29289322,1.29289322 C1.47385763,1.11192881 1.72385763,1 2,1 L2,1 Z" id="矩形" stroke="#666666" stroke-width="2"></path>
                        <rect id="矩形" fill="#666666" x="9" y="13" width="12" height="2" rx="1"></rect>
                        <rect id="矩形" fill="#666666" transform="translate(15.000000, 14.000000) rotate(90.000000) translate(-15.000000, -14.000000) " x="9" y="13" width="12" height="2" rx="1"></rect>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>