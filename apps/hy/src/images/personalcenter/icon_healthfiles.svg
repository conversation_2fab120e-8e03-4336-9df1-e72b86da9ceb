<?xml version="1.0" encoding="UTF-8"?>
<svg width="28px" height="28px" viewBox="0 0 28 28" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组备份</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="个人中心1" transform="translate(-571.000000, -223.000000)">
            <g id="个人中心-/-健康档案-icon" transform="translate(554.000000, 212.000000)">
                <g id="编组备份" transform="translate(17.000000, 11.000000)">
                    <rect id="区域" stroke="#979797" fill="#D8D8D8" opacity="0" x="0.5" y="0.5" width="27" height="27"></rect>
                    <path d="M2.13492063,10.5661376 L5.15637568,3.93261084 C5.41510681,3.36457332 5.98177378,3 6.60595999,3 L21.1189077,3 C21.7430939,3 22.3097609,3.36457332 22.568492,3.93261084 L25.5899471,10.5661376 L25.5899471,10.5661376" id="路径" stroke="#00A864" stroke-width="2.26984127"></path>
                    <path d="M25.1319967,10.1878307 L25.5899471,21.8357004 L2.59287107,22.2936508 L2.13492063,10.6457811 L8.28557954,10.1878307 C10.1679612,12.7391757 12.0370921,13.8257481 14.200919,13.8257481 C15.3279606,13.8257481 16.3309302,13.5241505 17.2136297,12.9330808 C18.0475845,12.3746513 18.7794789,11.547316 19.3851816,10.4278704 L19.3851816,10.4278704 L25.1319967,10.1878307 Z" id="形状结合" stroke="#00A864" stroke-width="2.26984127"></path>
                </g>
            </g>
        </g>
    </g>
</svg>