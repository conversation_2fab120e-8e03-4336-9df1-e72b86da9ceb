<?xml version="1.0" encoding="UTF-8"?>
<svg width="66px" height="66px" viewBox="0 0 66 66" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>0903 / 首页权益icon / 术后家庭护理</title>
    <defs>
        <linearGradient x1="0%" y1="18.8365651%" x2="100%" y2="81.1634349%" id="linearGradient-1">
            <stop stop-color="#F0C99A" offset="0%"></stop>
            <stop stop-color="#D3A26A" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="50%" x2="100%" y2="50%" id="linearGradient-2">
            <stop stop-color="#EAC190" offset="0%"></stop>
            <stop stop-color="#E3B784" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="0904" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="0-首页icon" transform="translate(-150.000000, -258.000000)">
            <g id="编组" transform="translate(150.000000, 258.000000)">
                <g id="重离子">
                    <rect id="区域" fill="#F2F2F2" opacity="0" x="0" y="0" width="66" height="66"></rect>
                    <rect id="区域" stroke="#E5CFB0" fill-opacity="0.24" fill="#FFFFFF" x="0.5" y="0.5" width="65" height="65" rx="32.5"></rect>
                </g>
                <g id="编组-2" transform="translate(14.000000, 15.000000)">
                    <path d="M4,4.50852273 L34,4.50852273 C36.209139,4.50852273 38,6.29938373 38,8.50852273 L38,30.5085227 C38,32.7176617 36.209139,34.5085227 34,34.5085227 L4,34.5085227 C1.790861,34.5085227 2.705415e-16,32.7176617 0,30.5085227 L0,8.50852273 C-2.705415e-16,6.29938373 1.790861,4.50852273 4,4.50852273 Z" id="矩形" fill="url(#linearGradient-1)"></path>
                    <path d="M19,11 C19.5522847,11 20,11.4477153 20,12 L20,18 L26,18 C26.5522847,18 27,18.4477153 27,19 C27,19.5522847 26.5522847,20 26,20 L20,20 L20,26 C20,26.5522847 19.5522847,27 19,27 C18.4477153,27 18,26.5522847 18,26 L18,20 L12,20 C11.4477153,20 11,19.5522847 11,19 C11,18.4477153 11.4477153,18 12,18 L18,18 L18,12 C18,11.4477153 18.4477153,11 19,11 Z" id="形状结合" fill="#FDEDCD"></path>
                    <path d="M10.5400815,6.51988636 L10.5400815,2.89772727 C10.5400815,1.29735669 11.8430789,6.17284002e-14 13.4504076,6.17284002e-14 L23.6365489,6.17284002e-14 C25.2438776,6.17284002e-14 26.546875,1.29735669 26.546875,2.89772727 L26.546875,6.51988636" id="路径" stroke="url(#linearGradient-2)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                </g>
            </g>
        </g>
    </g>
</svg>