<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>通知</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#F3DCB8" offset="0%"></stop>
            <stop stop-color="#E2B682" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="控件" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="0903-/-首页健康档案提示-" transform="translate(-20.000000, -28.000000)">
            <g id="通知" transform="translate(20.000000, 28.000000)">
                <rect id="区域" stroke="#979797" fill="#D8D8D8" opacity="0" x="0.5" y="0.5" width="39" height="39"></rect>
                <g id="编组-16" transform="translate(6.000000, 4.000000)">
                    <g id="tongzhi-2" fill="url(#linearGradient-1)">
                        <path d="M1.45533882,20.0259016 L1.45533882,12.1268952 C1.45533882,5.44473016 6.51914639,-8.8817842e-15 12.8022834,-8.8817842e-15 C19.0855841,-8.8817842e-15 24.1492688,5.44473016 24.1492688,12.1268952 L24.1492688,19.9377079 C24.1492688,19.9973429 24.1684296,20.0553683 24.2039671,20.1031175 L25.6476168,22.0543968 C26.0493789,22.597673 26.1127159,23.3227841 25.8112612,23.9284603 C25.5099294,24.5341365 24.8953107,24.9166667 24.2231279,24.9166667 L1.77665025,24.9166667 C1.14205199,24.9163778 0.55572413,24.5752413 0.238261229,24.0213175 C-0.079037904,23.4673937 -0.0794882641,22.7849968 0.237319567,22.2307429 L1.41853212,20.1637016 C1.44252403,20.1218127 1.45533882,20.0742286 1.45533882,20.0259016 Z M8.1795038,29.9380907 C9.6625186,30.9142794 11.3002427,31.420774 12.9605072,31.4166667 C14.6284132,31.420774 16.2733093,30.9097003 17.7616177,29.9250234 C18.1376756,29.6936107 18.3877975,29.2128038 18.4143081,28.6702906 C18.4409895,28.1276099 18.2397479,27.6090532 17.8892187,27.3169952 C17.5385187,27.0248813 17.0942845,27.0052804 16.7295821,27.2659548 C15.5609379,28.0379263 14.2695594,28.4379844 12.9605072,28.4331819 C11.6569193,28.4384311 10.3707063,28.0425054 9.20573348,27.2770675 C8.64743106,26.9365379 7.98308619,27.23781 7.7055999,27.9576801 C7.42828438,28.677271 7.63806401,29.5550646 8.1795038,29.9398776 L8.1795038,29.9380907 Z" id="tongzhi"></path>
                    </g>
                    <path d="M14.0833333,5.41666667 C17.0827684,5.41666667 19.5,7.84864021 19.5,10.8333333" id="路径" stroke="#FFFFFF" stroke-width="2.70833333" stroke-linecap="round" stroke-linejoin="round"></path>
                </g>
            </g>
        </g>
    </g>
</svg>