import { createAction } from 'redux-actions';
import { fetchJson } from '../../utils/fetch';
import { TransformObject } from '../../utils/serialization';

import reduxStore  from '@/store'

export const resourcesEnumsList = [
  'GENDER', //性别
  'PATIENTRELATION', //患者关系
  'INQUIRYTYPE', // 问诊类型
  'INQUIRYSTATUS', // 问诊状态
  'SPECIALISMCLINICENUM',
  'PROFESSIONALTITLE', //医生职级
  'SYSTEMMODE',  //监管模式
  'MEDICINEFREQ',  //监管模式
  // "PRESCRIPTIONSTATUS",// 处方单状态
];

export const FETCH_RESOURCE_LIST = 'FETCH_RESOURCE_LIST';
export const FETCH_RESOURCE_LIST_REDUCER = 'FETCH_RESOURCE_LIST_REDUCER';
export const FETCH_DEPARTMENT_LIST = 'FETCH_DEPARTMENT_LIST';
// export const FETCH_RESOURCE_LIST_REDUCER = "FETCH_RESOURCE_LIST_REDUCER";

/****** 获取相应的枚举字段 ******/

export const fetchResourceList = createAction(FETCH_RESOURCE_LIST, (store, payload = {}) => {
  return new Promise((resolve) => {
    return fetchJson({
      url: '/zaApi/v1/patient/resource/list',
      method: 'POST',
      data: resourcesEnumsList,
      needLogin: false,
      needLoading: false,
    }).then((res) => {
      if (res.code === '0') {
        const { result = {} } = res;
        let data = resourcesEnumsList.reduce((prev, key) => {
          prev[`${key}_OBJ`] = TransformObject(result[key], 'resCode', 'resName');
          return prev;
        }, result);
        reduxStore.dispatch({
          type: FETCH_RESOURCE_LIST_REDUCER,
          data,
        });
      }
      resolve();
    });
  });
});

//获取科室枚举
export const fetchDepartmentList = createAction(FETCH_DEPARTMENT_LIST, (store, payload = {}) => {
  return new Promise((resolve) => {
    return fetchJson({
      url: '/zaApi/v1/patient/hostpital/departmentList',
      data: {},
      method: 'POST',
      needLogin: false,
      needLoading: false,
    }).then((res) => {
      if (res.code === '0') {
        const { result = [] } = res;
        if (result.length) {
          result.map((item) => {
            if (item.departmentCode === '03.07') {
              item.departmentName = '糖尿病专区';
            }
            return item;
          });
        }
        const DEPARTMENT_OBJ = TransformObject(result, 'departmentCode', 'departmentName');
        // yield put({ type: DepartmentActionTypes.FETCH_DEPARTMENT_LIST_SUCCESS, data: {list: result, obj: DEPARTMENT_OBJ} });
        reduxStore.dispatch({
          type: FETCH_RESOURCE_LIST_REDUCER,
          data: { DEPARTMENT_OBJ },
        });
      }
      resolve();
    });
  });
});
