
import { createAction } from 'redux-actions'

export const INCREMENT = 'INCREMENT'
export const IM_KICKOUT = 'IM_KICKOUT'
export const IM_UPDATEMYINFO = 'IM_UPDATEMYINFO'
export const IM_GETMESSAGELIST = 'IM_GETMESSAGELIST'
export const IM_GETGROUPLIST_REDUCER = 'IM_GETGROUPLIST_REDUCER'
export const IM_SENDGETMESSAGE = 'IM_SENDGETMESSAGE'
export const IM_RECEIVEMESSAGE = 'IM_RECEIVEMESSAGE';
export const IM_UPDATE_SENDGETMESSAGE_STATUS = 'IM_UPDATE_SENDGETMESSAGE_STATUS';

export const IM_UNSHIFTMESSAGELIST = 'IM_UNSHIFTMESSAGELIST'
export const IM_SET_CURRENTCONVERSATIONID = 'IM_SET_CURRENTCONVERSATIONID'
export const IM_GETGROUPLIST = 'IM_GETGROUPLIST';
export const IM_ISSDKREADY = 'IM_ISSDKREADY';


let getMessageLoading = false;

export const getMessageList = createAction(IM_GETMESSAGELIST, (store, payload = {}) => {
  /*获取消息列表 store*/
  console.log("1、开始 IM-获取消息列表");
  return new Promise(resolve => {
    const { nextReqMessageID, isCompleted = false, currentConversationID } = payload;

    // 判断是否拉完了，isCompleted 的话要报一下没有更多了

    console.log("2、开始 IM-获取 判断是否拉完了，isCompleted 的话要报一下没有更多了");
    if (!isCompleted) {
      // 如果请求还没回来，又拉，此时做一下防御
      if (!getMessageLoading) {
        getMessageLoading = true
        console.log("3、开始 IM-获取列表 正式进入请求了，～～");

        wx.$app.getMessageList({ conversationID: `GROUP${currentConversationID}`, nextReqMessageID: nextReqMessageID, count: 15 }).then(res => {
          let { nextReqMessageID = '', messageList = [], isCompleted: isCompleteds } = res.data;
          console.log("获取到了 IM-getMessageList", res, "======", res.data.nextReqMessageID);
          getMessageLoading = false
          store.dispatch({
            type: IM_UNSHIFTMESSAGELIST,
            payload: {
              messageList,
              isCompleted: isCompleteds, nextReqMessageID,
            }
          });
          resolve();
          console.log("4、IM-获取列表 请求了，成功了～～");

        }).catch(err => {
          console.log(err)
          getMessageLoading = false
          console.log("4、 IM-获取列表 出错了getMessageList   err", err);
          resolve();
        })
      } else {
        console.log("6、上一次的拉取列表，还在请求中，～～");
      }
    } else {
      console.log("7、获取列表没有更多了，～～");
      wx.showToast({
        title: '没有更多啦',
        icon: 'none',
        duration: 1500
      })
      resolve();
    }
  })
})

function matchingGroupList(store, videos, groupList = []) {
  let currentGroup = groupList.find(k => (k.groupID == videos.currentConversationID)) || {};
  console.log("~~~~~1、第0个群聊信息", groupList[0],);
  console.log("——————————————————=============",);
  console.log("~~~~~2、匹配到的群聊结果：", currentGroup, "=== 问诊id", videos.currentConversationID);
  if (currentGroup.groupID) {
    console.log("~~~~~3、匹配到了 群聊");
    store.dispatch({
      type: IM_GETGROUPLIST_REDUCER,
      payload: true
    });
    console.log("~~~~~4、并开始拉取历史群消息、群消息列表");
    store.dispatch(getMessageList(store, videos));
  }
}
export const imGetGroupList = createAction(IM_GETGROUPLIST, (store, payload = {}) => {
  /*获取消息列表 store*/
  console.log("1、store imGetGroupList 请求进入");
  return new Promise(resolve => {
    let { groupList = [], groupQueryNum = 0 } = payload;
    let { videos = {} } = store.getState();
    if (!videos.isSDKReady || (videos.isSDKReady && videos.isGroupReady)) {
      console.log(!videos.isSDKReady ? "2、store imGetGroupList 请求进入，但isSDKReady 还未初始化完成" : "2、isSDKReady 始化完成,并且群列表已获取到");
      return resolve();
    };
    if (videos.isSDKReady && groupList.length) {
      matchingGroupList(store, videos, groupList);
      console.log("3、store imGetGroupList 从全局 TIM.EVENT.GROUP_LIST_UPDATED 监听群列表进入 ");
      resolve();
    } else if (videos.isSDKReady && groupQueryNum >= 15) {
      console.log("**** 1、特别处理，已经达到最高阀值 ,尝试直接加入群聊", groupQueryNum);
      wx.$app.joinGroup({ groupID: videos.currentConversationID, applyMessage: '我想申请加入贵群，望批准！' })
        .then((res = {}) => {
          let { status = '' } = res.data || {}
          if (status == wx.TIM.TYPES.JOIN_STATUS_SUCCESS || status == wx.TIM.TYPES.JOIN_STATUS_ALREADY_IN_GROUP) {
            console.log("**** 2、特别处理，已经达到最高阀值 ,加入群聊成功、并开始拉取历史群消息、群消息列表", res);
            store.dispatch({
              type: IM_GETGROUPLIST_REDUCER,
              payload: true
            });
            store.dispatch(getMessageList(store, videos));
          } else {
            console.log("**** 3、特别处理，已经达到最高阀值 ,加入群聊没有成功，请查看状态", res.data);
          };
        })
        .catch((res) => {
          console.log("**** 4、特别处理，已经达到最高阀值 , 直接加入群聊出错了", res);
        });
    } else {
      let promise = wx.$app.getGroupList();
      promise.then((imResponse) => {
        let { data: { groupList = [] } = {} } = imResponse;
        console.log(groupList, "群组列表"); // 群组列表
        console.log("4、store imGetGroupList 从定时器 监听群列表进入 ");
        matchingGroupList(store, videos, groupList);
        resolve();
      }).catch((imError) => {
        console.warn('5、获取群列表出错 getGroupList error:', imError); // 获取群组列表失败的相关信息
        wx.showModal({
          title: '问诊异常',
          content: imError,
          confirmText: '好的',
          showCancel: false,
          success: (res) => {
            if (res.confirm) {
              const src = encodeURIComponent(
                `${wx.$webviewUrl}/hospital/myinquiry`
              );
              const param = JSON.stringify({ defaultType: 1 });
              wx.redirectTo({ url: `/pages/webview?src=${src}&param=${param}` });
            }
          },
        });
        resolve();
      });
    }
  })
})

export const imReceiveMessage = (payload = {}) => {
  /*IM 接收消息*/
  console.log("IM 接收消息", payload);
  let { name = '', data = [] } = payload;
  if (name !== 'onMessageReceived') {
    return {
      type: '',
    }
  };
  return {
    type: IM_RECEIVEMESSAGE,
    payload: data
  }
}
export const imSendMessage = (payload = {}) => {
  /*IM 发送消息*/
  console.log("IM 发送消息", payload);
  return {
    type: IM_SENDGETMESSAGE,
    payload: payload
  }
}

export const imUpdateSendMessageStatus = (payload = {}) => {
  /*IM 发送消息,成功后更新状态 ：用于图片、视频*/
  console.log("发送消息,成功后更新状态 ：用于图片、视频");
  let { status = '', index = '' } = payload;
  if (!status || !index) {
    return { type: '' };
  }
  return {
    type: IM_UPDATE_SENDGETMESSAGE_STATUS,
    payload
  }
}

export const imUpdateMyInfo = (payload = {}) => {
  /*更新用户信息*/
  console.log("更新用户信息");

  return {
    type: IM_UPDATEMYINFO,
    payload,
  }
}

export const imLogout = () => {
  /*退出登录，并重置 store*/
  console.log("退出登录，并重置");
  wx.$app && wx.$app.logout && wx.$app.logout();
  return {
    type: IM_KICKOUT,
  }
}

export const imSetCurrentConversationID = (payload = {}) => {

  /*设置当前聊天用户的 id store*/
  return {
    type: IM_SET_CURRENTCONVERSATIONID,
    payload,
  }
}

export const imIsSDKReady = (isSDKReady = false) => {
  /*设置sdk是否初始化完成 store*/
  return {
    type: IM_ISSDKREADY,
    isSDKReady,
  }
}


