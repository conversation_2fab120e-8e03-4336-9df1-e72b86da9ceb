
import { createAction } from 'redux-actions'
import { fetchJson } from '../../utils/fetch';

export const SELFVIDEO_GET_INQUIRY_DETAIL = 'SELFVIDEO_GET_INQUIRY_DETAIL';
export const SELFVIDEO_GET_INQUIRY_DETAIL_REDUCER = 'SELFVIDEO_GET_INQUIRY_DETAIL_REDUCER'
export const SELFVIDEO_UPDATE_INQUIRY_DETAIL = 'SELFVIDEO_UPDATE_INQUIRY_DETAIL'
export const SELFVIDEO_EDIT_CHAT_INFO = 'SELFVIDEO_EDIT_CHAT_INFO'
export const SELFVIDEO_RESET_CHAT_INFO = 'SELFVIDEO_RESET_CHAT_INFO'
export const SELFVIDEO_RESET_MSGLIST = 'SELFVIDEO_RESET_MSGLIST'
export const SELFVIDEO_ADD_CHAT_MSG = 'SELFVIDEO_ADD_CHAT_MSG'
export const SELFVIDEO_SHIFT_CHAT_MSG = 'SELFVIDEO_SHIFT_CHAT_MSG'
export const SELFVIDEO_ADD_RECEIVE_MSG = 'SELFVIDEO_ADD_RECEIVE_MSG'

export const selfVideoGetInquiryDetail = createAction(SELFVIDEO_EDIT_CHAT_INFO, (store, payload = {}) => {
  /*获取消息列表 store*/
  return new Promise(resolve => {
    let { inquiryId = '', inquiryNo = '', success } = payload;
    return fetchJson({
      url: `/zaApi/v1/patient/inquiry/detail`,
      method: 'POST',
      needLogin: true,
      needLoading: true,
      needToast: false,
      data: {
        inquiryNo,
        inquiryId,
        option: {
          needStaff: true,
          needRightsBaseInfo: true,
        }
      },
    }).then((res) => {
      if (res.code === '0') {
        store.dispatch({
          type: SELFVIDEO_GET_INQUIRY_DETAIL_REDUCER,
          data: res.result
        });
        success && success();
      };
      resolve();
    });
  })
})


export const selfVideoUpdateInquiryDetail = (data) => {
  return {
    type: SELFVIDEO_UPDATE_INQUIRY_DETAIL,
    data,
  }
}


export const selfVideoGetHistory = (data) => {
  return {
    type: SELFVIDEO_SHIFT_CHAT_MSG,
    data,
  }
}

export const selfVideoEditChatInfo = (data) => {
  return {
    type: SELFVIDEO_EDIT_CHAT_INFO,
    data
  }
}

export const selfVideoResetChatInfo = () => {
  return {
    type: SELFVIDEO_RESET_CHAT_INFO
  }
}

export const selfVideoResetMsgList = () => {
  return {
    type: SELFVIDEO_RESET_MSGLIST
  }
}

export const selfVideoAddReceiveMsg = (data) => {
  return {
    type: SELFVIDEO_ADD_RECEIVE_MSG,
    data
  }
}
export const selfVideoAddMessage = (data=[]) => {
  return {
    type: SELFVIDEO_ADD_CHAT_MSG,
    data
  }
}


