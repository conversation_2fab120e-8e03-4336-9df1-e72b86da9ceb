import { createAction } from 'redux-actions';
import { fetchJson } from '../../utils/fetch';
import format from '../../utils/format';

import reduxStore  from '@/store'

export const FETCH_FAMILY_DOCTOR_SIGNED = 'FETCH_FAMILY_DOCTOR_SIGNED';
export const UPDATE_FAMILY_DOCTOR_INFO = 'UPDATE_FAMILY_DOCTOR_INFO';
export const FETCH_FAMILY_DOCTOR_SERVICE = 'FETCH_FAMILY_DOCTOR_SERVICE';
export const UPDATE_BIND_DOCTOR_INFO = 'UPDATE_BIND_DOCTOR_INFO';
export const FETCH_FAMILY_DOCTOR_RIGHTS = 'FETCH_FAMILY_DOCTOR_RIGHTS';

export const fetchFamilyDoctorService = createAction(
  FETCH_FAMILY_DOCTOR_SERVICE,
  (store, payload = {}) => {
    return new Promise((resolve) => {
      return fetchJson({
        url: '/zaApi/v1/patient/user/servpack/listIndex',
        method: 'POST',
        data: {
          servpackBizTypeList: [2, 3], //"服务包业务类型,1-普通 2-家医体验版 3-家医正式版
        },
        needLoading: false,
      }).then((res) => {
        if (res && res.code === '0') {
          let { userServpackDomain = {}, favourCountdownTime = '' } = res.result || {};
         reduxStore.dispatch({
            type: UPDATE_FAMILY_DOCTOR_INFO,
            data: {
              favourCountdownTime,
              tryOutOrContinuedFeePoup: !!favourCountdownTime,
              isBuyedServpack: Object.keys(userServpackDomain).length ? 'Y' : 'N',
            },
          });
        }
        resolve(res);
      });
    });
  }
);

//查询绑定医生
export const fetchFamilyDoctorSigned = createAction(
  FETCH_FAMILY_DOCTOR_SIGNED,
  (store, payload = {}) => {
    return new Promise((resolve) => {
      return fetchJson({
        url: '/zaApi/v1/patient/medical/doctor/butler/getButlerDoctorByUser',
        method: 'POST',
        data: {
          servpackBizTypeList: [2, 3], //"服务包业务类型,1-普通 2-家医体验版 3-家医正式版
        },
        needLoading: false,
      }).then((res) => {
        if (res && res.code === '0') {
          let {
            medicalStaffView = {},
            expiryTime = '',
            bindTime = '',
            isDoctorNormalService='',
            ...other
          } = res.result || {};
         reduxStore.dispatch({
            type: UPDATE_FAMILY_DOCTOR_INFO,
            data: {
              isBindDoctor:  isDoctorNormalService === 'Y',
              isDoctorNormalService,
              bindDoctorInfo: {
                ...medicalStaffView,
                ...other,
                bindTime:
                  Math.floor(
                    (Date.now() - new Date(bindTime.replace(/-/g, '/')).getTime()) /
                      (1000 * 60 * 60 * 24)
                  ) + 1,
                expiryTime: format.date(expiryTime, 'yyyy.MM.dd'),
              },
            },
          });
        }
        resolve(res);
      });
    });
  }
);

export const updateFamilyDoctorInfo = (data) => ({ type: UPDATE_FAMILY_DOCTOR_INFO, data });
