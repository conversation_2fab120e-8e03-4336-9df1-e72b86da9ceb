import { handleActions } from 'redux-actions'
import { INCREMENT, IM_KICKOUT, IM_UPDATEMYINFO, IM_UNSHIFTMESSAGELIST, IM_RECEIVEMESSAGE, IM_SENDGETMESSAGE, IM_UPDATE_SENDGETMESSAGE_STATUS, IM_SET_CURRENTCONVERSATIONID, IM_ISSDKREADY, IM_GETGROUPLIST_REDUCER } from '../actions/video'
// import { decodeElement } from '../../utils/decodeElement';
/*const identityimageFilter = (item) => {
  let { elements = [] } = item;
  let { content: { imageFormat = '' } = {} } = elements[0] || {};
  console.log(elements, elements[0], "--identityimageFilter--", imageFormat);
  return imageFormat != "IDENTITYIMAGE";
}*/
const bedeckMessage = (item = {}, index = -1) => {
  // 小程序问题，在渲染的时候模板引擎不能处理函数，所以只能在渲染前处理好message的展示问题
  let { payload: { text = '' } = {}, time, type = '', ID } = item;
  let date = new Date(time * 1000);
  return { ...item, newtime: date, text, showTime: index % 10 === 0 ? true : false, uid: (type == 'TIMTextElem' || type == 'TIMImageElem') ? ID : '' }
}

const bedeckImageList = (list = []) => {
  // 处理图片
  let res = [];
  list.map((k) => {
    let { type, payload: { imageInfoArray = [] } = {} } = k;
    if (type === 'TIMImageElem') {
      res.push(imageInfoArray[1].url);
    }
    return k;
  })
  return res
}

export default handleActions({
  [INCREMENT](state) {
    return {
      ...state,
      num: state.num + 1
    }
  },
  [IM_KICKOUT](state) {
    return {
      ...state,
      users: {},
      isCompleted: false,
      nextReqMessageID: '',
      currentConversationID: '',
      messageList: [],
      isSDKReady: false,
      imagesList: [],
      isGroupReady: false
    }
  },
  [IM_UPDATEMYINFO](state, action) {
    return {
      ...state,
      users: action.payload
    }
  },
  [IM_GETGROUPLIST_REDUCER](state, action) {
    return {
      ...state,
      isGroupReady: action.payload
    }
  },

  [IM_UNSHIFTMESSAGELIST](state, action) { //更新列表
    let { messageList = [], isCompleted, nextReqMessageID } = action.payload;
    let curList = messageList;

    // 第一次列表，过滤重复的消息
    if (state.messageList.length<=15 && curList.length > 1) {
      let prveMessage = state.messageList[0] || {};
      let lastMessage = curList[curList.length - 1] || {};
      if (prveMessage.ID && prveMessage.ID == lastMessage.ID) {
        console.log("初始化获取列表，，消息重复了")
        curList = curList.slice(0, -1);
      };
    };

    return {
      ...state,
      messageList: [...(curList.map((k, index) => {
        return bedeckMessage(k, index);
      })), ...state.messageList],
      isCompleted, nextReqMessageID,
      imagesList: [...(bedeckImageList(curList)), ...state.imagesList]
    }
  },
  [IM_RECEIVEMESSAGE](state, action) { //接收消息
    let prveMessage = state.messageList[state.messageList.length - 1] || {};
    let curMessage = action.payload[action.payload.length - 1] || {};
    console.log("redurcer curMessage", curMessage.ID)
    console.log("redurcer prveMessage", prveMessage.ID)
    if (prveMessage.ID && prveMessage.ID == curMessage.ID) {
      console.log("redurcer 不好啦，消息重复了")
      return { ...state };
    }
    return {
      ...state,
      messageList: [...state.messageList, ...(action.payload.map((k, index) => {
        return bedeckMessage(k, index);
      }))],
      imagesList: [...state.imagesList, ...(bedeckImageList(action.payload)),]
    }
  },
  [IM_SENDGETMESSAGE](state, action) { //发送消息
    return {
      ...state,
      messageList: [...state.messageList, bedeckMessage(action.payload)],
      imagesList: [...state.imagesList, ...(bedeckImageList([action.payload])),]
    }
  },
  [IM_UPDATE_SENDGETMESSAGE_STATUS](state, action) { //更新消息状态
    let { status = '', index } = action.payload;
    state.messageList[index].status = status;
    return {
      ...state,
      messageList: [...state.messageList],
    }
  },
  [IM_SET_CURRENTCONVERSATIONID](state, action) { //当前im聊天用户id
    let { currentConversationID = '' } = action.payload;
    return {
      ...state,
      currentConversationID,
    }
  },
  [IM_ISSDKREADY](state, action) { //sdk是否初始化完成
    let { isSDKReady = false } = action;
    return {
      ...state,
      isSDKReady,
    }
  },


}, {
  num: 0,
  asyncNum: 0,
  users: {},
  isSDKReady: false,
  isCompleted: false,
  nextReqMessageID: '',
  currentConversationID: '',
  messageList: [],
  imagesList: [],
  isGroupReady: false
})