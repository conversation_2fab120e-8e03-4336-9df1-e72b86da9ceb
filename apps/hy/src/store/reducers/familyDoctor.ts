import { handleActions } from 'redux-actions';
import { UPDATE_FAMILY_DOCTOR_INFO, UPDATE_BIND_DOCTOR_INFO } from '../actions/familyDoctor';

export default handleActions(
  {
    [UPDATE_FAMILY_DOCTOR_INFO](state, action) {
      return {
        ...state,
        ...action.data,
      };
    },

  },
  {
    isBuyedServpack: '',
    servPack: {},
    isBindDoctor: false,
    isDoctorNormalService:'',
    tryOutOrContinuedFeePoup: true,
    favourCountdownTime: '',
    doctorList: [],
    bindDoctorInfo: {  },
  }
);
