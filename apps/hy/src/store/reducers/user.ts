// import { handleActions } from 'redux-actions';
// import {  } from '../actions/user';

// export default handleActions({
//   [INCREMENT](state) {
//     return {
//       ...state,
//       num: state.num + 1
//     }
//   },
//   [IM_KICKOUT](state) {
//     return {
//       ...state,
//       users: {}
//     }
//   },
//   [IM_UPDATEMYINFO](state, action) {
//     return {
//       ...state,
//       users: action.payload
//     }
//   },
//   [IM_UNSHIFTMESSAGELIST](state, action) { //更新列表
//     let { messageList = [], isCompleted, nextReqMessageID } = action.payload;
//     return {
//       ...state,
//       messageList: [...(messageList.map((k, index) => {
//         return bedeckMessage(k, index);
//       })), ...state.messageList],
//       isCompleted, nextReqMessageID,
//       imagesList: [...(bedeckImageList(messageList)), ...state.imagesList]
//     }
//   },
//   [IM_RECEIVEMESSAGE](state, action) { //接收消息
//     return {
//       ...state,
//       messageList: [...state.messageList, ...(action.payload.map((k, index) => {
//         return bedeckMessage(k, index);
//       }))],
//       imagesList: [...state.imagesList, ...(bedeckImageList(action.payload)),]
//     }
//   },
//   [IM_SENDGETMESSAGE](state, action) { //发送消息
//     return {
//       ...state,
//       messageList: [...state.messageList, bedeckMessage(action.payload)],
//       imagesList: [...state.imagesList, ...(bedeckImageList([action.payload])),]
//     }
//   },
//   [IM_UPDATE_SENDGETMESSAGE_STATUS](state, action) { //更新消息状态
//     let { status = '', index } = action.payload;
//     state.messageList[index].status = status;
//     return {
//       ...state,
//       messageList: [...state.messageList],
//     }
//   },
// }, {
//   num: 0,
//   asyncNum: 0,
//   users: {},
//   isCompleted: false,
//   nextReqMessageID: '',
//   messageList: [],
//   imagesList: []
// })
