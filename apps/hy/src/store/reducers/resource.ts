import { handleActions } from 'redux-actions'
import { FETCH_RESOURCE_LIST_REDUCER } from '../actions/resource'


export default handleActions({
  [FETCH_RESOURCE_LIST_REDUCER](state, action) {
    return {
      ...state,
      ...action.data
    }
  },

}, {
  "GENDER": [], //性别
  "GENDER_OBJ": [],
  "PATIENTRELATION": [],//患者关系
  "PATIENTRELATION_OBJ": [],
  "INQUIRYTYPE": [],// 问诊类型
  "INQUIRYTYPE_OBJ": [],
  "INQUIRYSTATUS": [],// 问诊状态
  "INQUIRYSTATUS_OBJ": [],
  "SYSTEMMODE":[], //监管模式
})