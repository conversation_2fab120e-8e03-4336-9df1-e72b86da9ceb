import { handleActions } from 'redux-actions'
import format from '@/utils/format';
import { SELFVIDEO_GET_INQUIRY_DETAIL_REDUCER, SELFVIDEO_UPDATE_INQUIRY_DETAIL, SELFVIDEO_EDIT_CHAT_INFO, SELFVIDEO_ADD_CHAT_MSG, SELFVIDEO_SHIFT_CHAT_MSG, SELFVIDEO_ADD_RECEIVE_MSG, SELFVIDEO_RESET_CHAT_INFO, SELFVIDEO_RESET_MSGLIST } from '../actions/selfVideo'


// const messageList = [
//   {
//     biz_no: "102107270008820068",
//     biz_type: "INQUIRY",
//     is_read: "Y",
//     msg_id: 19181,
//     msg_type: 3,
//     name: "蔡璐",
//     reply_content: { title: "蔡璐 | 女 | 28岁", content: "病情描述：<br/>烦烦烦难分难分男女<br/>其他信息：<br/>非孕期或哺乳期<br/>无肝肾功能异常<br/>无药物过敏史" },
//     reply_date: 1627370497405,
//     user_id: 3572004,
//     user_type: 1,
//   },
//   {
//     user_type: '3',
//     msg_type: '1',
//     reply_date: '1627370497405',
//     text: 'wejfergjrthtjyjhyujhh阿胶阿胶金融界yukjukiiulkiol;ui111111112343456565543443'
//   },
//   {
//     biz_no: "102106280008285041",
//     biz_type: "INQUIRY",
//     is_read: "Y",
//     msg_id: 15725,
//     msg_type: 3,
//     name: "蔡璐",
//     reply_content: { title: "我已确认复诊并对医生的问询如实回答" },
//     reply_date: 1624865614549,
//     user_id: 3572004,
//     user_type: 1,
//   },
//   {
//     biz_no: "102106280008285041",
//     biz_type: "INQUIRY",
//     headimage: "/v1/attchment/downloadFile/CF517E1C83F5422AB6D47F3825309CA2_20210129153149.jpg",
//     is_read: "Y",
//     msg_id: 15724,
//     msg_type: 3,
//     name: "江风",
//     reply_content: { title: "初次诊断结果：感冒病*#*治疗处理意见：啦啦啦啦啦啦啦啦啦啦", content: "查看处方单并购药" },
//     backUrl: "/prescription/view?inquiryId=8285042&patientId=4895007",
//     content: "查看处方单并购药",
//     frontUrl: "/hospital/myprescription",
//     title: "初次诊断结果：感冒病*#*治疗处理意见：啦啦啦啦啦啦啦啦啦啦",
//     reply_date: 1624865510862,
//     isPrescription: true,
//     user_id: 375002,
//     user_type: 2,
//   },
//   {
//     user_type: '2',
//     biz_no: "CSN04700009",
//     biz_type: "CONSULT",
//     is_read: "Y",
//     msg_id: 12092,
//     msg_type: 4,
//     reply_content: { link: "/v1/attchment/downloadFile/E10D2FB26FD046A0911106D16C148A6A_20210621171858.png" },
//     reply_date: 1624267138973,
//     room_id: 2455002,
//     tag: "CSN04700009",
//     user_id: 3572004,
//   },
//   {
//     user_type: '1',
//     biz_no: "CSN04700009",
//     biz_type: "CONSULT",
//     is_read: "Y",
//     msg_id: 12092,
//     msg_type: 4,
//     reply_content: { link: "/v1/attchment/downloadFile/E10D2FB26FD046A0911106D16C148A6A_20210621171858.png" },
//     reply_date: 1624267138973,
//     room_id: 2455002,
//     tag: "CSN04700009",
//     user_id: 3572004,
//   },
//   {
//     user_type: '1',
//     msg_type: 7,
//     reply_content: { link: '', title: '富婆通讯录.pdf' }
//   },
//   {
//     user_type: '2',
//     msg_type: 7,
//     reply_content: { link: '', title: '富婆通讯录.word' }
//   },
//   {
//     user_type: '1',
//     text: 'wejfergjrthtjyjhyujyukjukiiulkiol;ui111111112343456565543443'
//   },
//   {
//     user_type: '2',
//     text: 'wejfergjrthtjyjhyujhh阿胶阿胶金融界yukjukiiulkiol;ui111111112343456565543443'
//   }
// ];

const isToday = (date) => {
  return format.date(date, 'yyyy-MM-dd') === format.date(new Date(), 'yyyy-MM-dd');
}

const formatDate = (reply_date) => {
  return format.date(reply_date, isToday(reply_date) ? 'hh:mm' : 'MM月dd日 hh:mm')
}

export default handleActions({
  [SELFVIDEO_EDIT_CHAT_INFO](state, action) {
    return {
      ...state,
      ...action.data,
    }
  },
  [SELFVIDEO_SHIFT_CHAT_MSG](state, action) {
    const imgs = [];
    (action.data || []).map((item) => {
      if (item.msg_type == 1) {
        item.replyDate = formatDate(item.reply_date);
      } else if (item.msg_type == 4) {
        item.reply_content.link = format.imageUrl(item.reply_content.link);
        const url = item.reply_content.locallink || item.reply_content.link || '';
        imgs.push(url);
      } else if (item.msg_type == 3) {
        if ((item.reply_content.frontUrl || '').indexOf('prescription') > -1) {
          item.isPrescription = true;
        }
      }
    });
    return {
      ...state,
      msgList: [
        ...action.data,
        ...state.msgList,
      ],
      imgList: [
        ...imgs,
        ...state.imgList,
      ],
    }
  },
  [SELFVIDEO_ADD_CHAT_MSG](state, action) {
    const imgs = [];
    (action.data || []).map((item) => {
      if (item.msg_type == 1) {
        item.replyDate = formatDate(item.reply_date);
      } else if (item.msg_type == 4) {
        item.reply_content.link = format.imageUrl(item.reply_content.link);
        const url = item.reply_content.locallink || item.reply_content.link || '';
        imgs.push(url);
      } else if (item.msg_type == 3) {
        if ((item.reply_content.frontUrl || '').indexOf('prescription') > -1) {
          item.isPrescription = true;
        }
      }
    });
    return {
      ...state,
      msgList: [
        ...state.msgList,
        ...action.data
      ],
      imgList: [
        ...state.imgList,
        ...imgs,
      ],
    }
  },
  [SELFVIDEO_ADD_RECEIVE_MSG](state, action) {
    console.log(action);
    return {
      ...state,
      receiveMsgRes: {
        ...state.receiveMsgRes,
        ...action.data
      }
    }
  },
  [SELFVIDEO_GET_INQUIRY_DETAIL_REDUCER](state, action) {
    const inquiryDetail = action.data || {};
    inquiryDetail.staffAvatar = inquiryDetail.medicalStaff ? format.imageUrl(inquiryDetail.medicalStaff.headPortrait) : '';
    return {
      ...state,
      inquiryDetail,
    }
  },
  [SELFVIDEO_UPDATE_INQUIRY_DETAIL](state, action) {
    return {
      ...state,
      inquiryDetail: {
        ...state.inquiryDetail,
        ...action.data
      },
    }
  },
  [SELFVIDEO_RESET_MSGLIST](state, action) {
    return {
      ...state,
      msgList: [],
      imgList: [],
      receiveMsgRes: {},
      historyTime: 0,
    }
  },
  [SELFVIDEO_RESET_CHAT_INFO](state, action) {
    return {
      ...state,
      msgList: [],
      imgList: [],
      doctorInfo: {},
      historyTime: 0,
      receiveMsgRes: {},
      inquiryDetail: {},
      config: {},
      isPulling: false,
      noMoreMsg: false,
    }
  }
}, {
  msgList: [],
  imgList: [],
  doctorInfo: {},
  historyTime: 0,
  receiveMsgRes: {},
  inquiryDetail: {},
  config: {},
  isPulling: false,
  noMoreMsg: false,
  isInquiryEnd: false
})
