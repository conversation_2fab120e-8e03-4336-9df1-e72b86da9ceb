import '@/utils/polyfill';
import React, { useRef } from 'react';
import '@/utils/platform/patch';
import debug from '@/utils/debug';
import { navigateBack, useDidShow, useLaunch } from '@tarojs/taro';
import { navigateToLink } from '@/utils';
import { getStorageSync, removeStorageSync } from '@/utils/storage';
import '@/track/proxy';
// import TrackBind from '@/components/track-bind';
import { QueryClientProvider, QueryClient } from '@tanstack/react-query';
import GlobalContext from '@/context/global';
import { requestUpdate } from '@/utils/update';
import bindUserInfo from '@/utils/bindUserInfo';
import track from './track';

import 'uno.css';
import './app.scss';

const logger = debug('app');

const queryClient = new QueryClient();
function App(props) {
  const { children } = props;
  const launchOptionsRef = useRef<undefined | {[key: string]: any}>();
  useLaunch((options) => {
    const { query = {}, referrerInfo, scene } = options;

    const referrerId = referrerInfo?.appId;
    launchOptionsRef.current = {
      ...query,
      scene,
      referrerId,
    };
    if (process.env.TARO_ENV === 'weapp') {
      requestUpdate();
    }
    // if (channelOrigin) {
    //   // 全局 channelOrigin 设置
    //   session.set('channelOrigin', channelOrigin);
    // }
    // 绑定分享的信息
    if (process.env.TARO_ENV !== 'tt') {
      bindUserInfo(query);
    }
    track.click('小程序来源', { extra_infomation: scene, extra_infomation1: referrerId });
    // store.dispatch(appInit());
  });

  useDidShow((options) => {
    const { referrerInfo = {}, scene } = options || {};
    const { appId, extraData } = referrerInfo;
    logger.info('referrerInfo:', referrerInfo, extraData);

    if (appId === 'wx00bc516a4d1619ef' && extraData?.out_trade_no) {
      navigateToLink('https://im2.zhongan.io/zaim/?accessId=1623289758025&tenantID=1514946886000043&color=green&channelId=A1&subject=微信支付联系商家或投诉&biz=微信', { navigateType: 'redirectTo' });
      return;
    }

    if (scene === 1038) { // 场景值1038：从被打开的小程序返回
      const failCallbackUrl = getStorageSync('WX_SIGN_FAIL_URL');
      const successCallbackUrl = getStorageSync('WX_SIGN_SUCCESS_URL');
      const payMethod = getStorageSync('WX_PAY_METHOD');
      removeStorageSync('WX_SIGN_FAIL_URL');
      removeStorageSync('WX_SIGN_SUCCESS_URL');
      removeStorageSync('WX_PAY_METHOD');
      if (appId === 'wxbd687630cd02ce1d' && payMethod === 'sign') { // appId为wxbd687630cd02ce1d：从签约小程序跳转回来
        logger.info('referrerInfo', referrerInfo);
        logger.info('extraData', extraData);
        logger.info('successCallbackUrl', successCallbackUrl);
        logger.info('failCallbackUrl', failCallbackUrl);
        logger.info('type', typeof failCallbackUrl);
        // if (!successCallbackUrl && !failCallbackUrl) {
        //   logger.info('navigateBack');
        //   Taro.navigateBack();
        //   return;
        // }
        if ((!failCallbackUrl && !successCallbackUrl) || (failCallbackUrl === 'undefined' && successCallbackUrl === 'undefined')) {
          navigateBack();
          return false;
        }
        if (typeof extraData === 'undefined') {
          // TODO
          // 客户端小程序不确定签约结果，需要向商户侧后台请求确定签约结果
          logger.info('签约结果未知');
          navigateToLink(failCallbackUrl, { navigateType: 'redirectTo' });
          // removeStorageSync('WX_SIGN_FAIL_URL');
          // removeStorageSync('WX_SIGN_SUCCESS_URL');
          return;
        }
        if (extraData.return_code === 'SUCCESS') {
          // TODO
          // 客户端小程序签约成功，需要向商户侧后台请求确认签约结果
          logger.info('签约成功');
          navigateToLink(successCallbackUrl, { navigateType: 'redirectTo' });
          return;
        }
        // 签约失败
        logger.info('签约失败');
        navigateToLink(failCallbackUrl, { navigateType: 'redirectTo' });
      }
    }
  });

  return (
    <GlobalContext>
      <QueryClientProvider client={queryClient}>
        {/* <TrackBind query={launchOptionsRef.current} /> */}
        {children}
      </QueryClientProvider>
    </GlobalContext>
  );
}

export default App;
