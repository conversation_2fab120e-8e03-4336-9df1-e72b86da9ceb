/** 协议类型 */
export enum AgreementFileTypeEnum {
  PDF = 'PDF',
  WORD = 'WORD',
  HTML = 'HTML'
}
/** 协议枚举 */
export enum AgreementKeyEnum {
  // 个人信息保护政策
  PERSONAL_PROTECTION = 'PERSONAL_PROTECTION',
  // 儿童个人信息保护政策
  CHILDREN_INFO_PROTECTION = 'CHILDREN_INFO_PROTECTION',
  // 众安健康隐私保护指引
  HEALTH_INFO_PROTECTION = 'HEALTH_INFO_PROTECTION',
  // 会员服务协议
  SERVICE_AGREEMENT = 'SERVICE_AGREEMENT'
}

/** 协议ILog枚举 */
export enum AgreementLogEnum {
  // 个人中心
  ACCOUNT = '个人中心',
  // 登录
  LOGIN = '登录',
  // 协议更新弹层
  UPDATE = '协议更新弹层',
}
/** 协议项 */
export interface AgreementItem{
  text: string;
  url?: string;
  fileType?: AgreementFileTypeEnum;
  // // 文件类型
  fileCode?: AgreementKeyEnum;
  // // 使用主体
  // // 附件内容：文件类型html返回文本内容；pdf 和word 为 公网访问url地址
  version?: string;
}

/** 协议请求入参 */
export interface IUserAgreementListItem {
  fileCode?: AgreementKeyEnum;
  version?: string;
}
export interface IUpdateAgreementRequest {
  userAgreementList?: IUserAgreementListItem[];
}

/** 更新协议列表返回 */
export type IUpgradeAgreementResponseResult = {
  [K in AgreementKeyEnum]: string;
};

/** 协议配置(cms) */
export interface IAgreementConfigResult {
  open: boolean;
  baseInfo: string;
  remainInfo: string;
}
