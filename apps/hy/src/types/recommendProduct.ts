export interface recommendProduct {
  id: number;
  ruleId: number;
  planCode: string;
  planName: string;
  packageDefIds: number[];
  productLabel: string;
  productLabelName: string;
  productUrl: string;
  productPictureUrl: string;
  sort: number;
  title: string;
  subTitle: string;
  productDesc: string;
  buttonText: string;
  buttonTextColor: string;
  buttonPictureUrl: string;
  frontColor: string;
  isHot: string;
  extraInfo?: any;
  relationShip: number;
}
