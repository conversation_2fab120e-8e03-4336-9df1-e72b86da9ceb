const Tabs = {
  weapp: [
    {
      pagePath: 'pages/index/index',
      iconPath: './assets/tab-bar/home.png',
      selectedIconPath: './assets/tab-bar/home-active.png',
      text: '首页',
    },
    // {
    //   pagePath: 'pages/home/<USER>',
    //   iconPath: './assets/tab-bar/home.png',
    //   selectedIconPath: './assets/tab-bar/home-active.png',
    //   text: '我的',
    // },

    // {
    //   pagePath: 'pages/index',
    //   text: '首页',
    //   iconPath: './images/tab/tab-home.png',
    //   selectedIconPath: './images/tab/tab-home-active.png',
    // },
    {
      pagePath: 'pages/personalcenter/index',
      text: '个人中心',
      iconPath: './images/tab/tab-personal.png',
      selectedIconPath: './images/tab/tab-personal-active.png',
    },
  ],
};

const Pages = {
  weapp: [
    'pages/index/index',
    'pages/webview/index',
    'pages/bridgeWebview/index',
    'pages/home/<USER>',
    'pages/personalcenter/index',
    'pages/user/profile/index',
    'pages/debugger/index',
    'pages/login',
    'pages/subscribeMessage'
  ],
};

const Subpackages = {
  weapp: []
};

const preloadRule = {
  weapp: {
    'pages/index/index': {
      network: 'all',
      packages: ['pages/activity', 'pages/search'],
    },
  },
};
export default {
  pages: Pages[process.env.TARO_ENV],
  // subpackages: Subpackages[process.env.TARO_ENV] || [],
  tabBar: {
    // color: '#666',
    // selectedColor: '#00BC70',
    // backgroundColor: '#ffffff',
    // borderStyle: 'white',
    // position: 'bottom',
    color: '#999999',
    selectedColor: '#00BC70',
    backgroundColor: '#fff',
    borderStyle: 'white',
    list: Tabs[process.env.TARO_ENV],
  },
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#ffffff',
    navigationBarTitleText: '众安互联网医院',
    navigationBarTextStyle: 'black',
    // backgroundTextStyle: 'light',
    // navigationBarBackgroundColor: '#fff',
    // navigationBarTitleText: '众安健康',
    // navigationBarTextStyle: 'black',
    // backgroundColor: '#f8f9fa',
    // navigationBarForceEnable: true,
    // extraQueryParam: 'page=pages%2Findex%2Findex%3Ftarget%3Dhttps%253A%252F%252Fihealth.zhongan.com%252Fimc%252Fgt%253FpromotionId%253D2048085%2526goodsId%253D2042122%2526tenantFrom%253Dza-health%2526insureConfigId%253D2039635%2526healthChannelCode%253Dc20250410102815002%2526healthChannelSource%253DT202505270310985006%2526itemId%253D925276416010%26urlType%3DMiniProgramWebView',
  },
  permission: {
    "scope.bluetooth": {
      "desc": "蓝牙连接"
    }
  },
  requiredPrivateInfos: ['getLocation', 'chooseAddress'],
  lazyCodeLoading: 'requiredComponents',
  networkTimeout: {
    request: 100000,
    downloadFile: 100000,
  },
  [process.env.TARO_ENV === 'weapp' ? '__usePrivacyCheck__' : 'usePrivacyCheck']: true,
  preloadRule: preloadRule[process.env.TARO_ENV],
};
