export const DEFAULT_NICKNAME = '众安用户';
export const DEFAULT_AVATAR_URL = 'https://cdn-health.zhongan.com/magiccube/resource/rl/ENhnKUp71C.png';

export const PRIVACY_URL = `https://nstatic.zhongan.com/hfe-static/pdf-viewer/index.html?file=${encodeURIComponent(
  'https://nstatic.zhongan.com/hfe-static/document/个人信息保护政策.pdf',
)}`;
export const MEMBER_URL = `https://nstatic.zhongan.com/hfe-static/pdf-viewer/index.html?file=${encodeURIComponent(
  'https://nstatic.zhongan.com/hfe-static/document/众安保险会员服务协议.pdf',
)}`;

export const OPEN_TYPE = {
  getPhoneNumber: {
    tt: 'getPhoneNumber',
    weapp: 'getPhoneNumber', // agreePrivacyAuthorization
    alipay: 'getAuthorize',
  },
};

export enum REDIRECT_TYPE {
  NONE = 1,
  H5 = 2,
  H5_DYNAMIC = 3,
  MINIPROGRAM = 4,
  MINIPROGRAM_DOUYIN = 6,
}

export enum YES_OR_NO {
  YES = 'Y',
  NO = 'N',
}

export const enum FLOAT_BANNER_LOCATION {
  SUSPENDED_BALL_OF_COMMON_DISEASE = 'suspended_ball_of_common_disease',
  SUSPENDED_BALL_OF_IMAGE_TEXT = 'suspended_ball_of_image_text',
  SUSPENDED_BALL_OF_INSURANCE_SCHEME = 'suspended_ball_of_insurance_scheme',
  SUSPENDED_BALL_OF_PITFALL_WORD = 'suspended_ball_of_pitfall_word',
  SUSPENDED_BALL_OF_SELECTED_ONE = 'suspended_ball_of_selected_one',
  HOME_PAGE_WINDOW = 'home_page_window',
  MY_SUSPENDED_BALL = 'my_suspended_ball',
}

export const enum ArticleDataType {
  CONTENT = 1,
  LINK = 2,
  VIDEO = 3,
  AD = 4,
}
