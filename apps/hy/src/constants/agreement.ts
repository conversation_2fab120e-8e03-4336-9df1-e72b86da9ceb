import config from '@/config';
import { AgreementFileTypeEnum, AgreementItem, AgreementKeyEnum } from '@/types/agreement';

export const AGREEMENT_PREFIX_URL = `${config.API.lemon.host}/v1/agreement/fetch?fileType=${AgreementFileTypeEnum.HTML}&fileCode=`;

/**
 * 登录相关协议或条款，相关协议页面时根据情况进行过滤
*/
export const AGREEMENT_MAP: Record<AgreementKeyEnum, AgreementItem> = {
  // 个人信息保护政策
  [AgreementKeyEnum.PERSONAL_PROTECTION]: {
    text: '个人信息保护政策',
    url: `${AGREEMENT_PREFIX_URL}${AgreementKeyEnum.PERSONAL_PROTECTION}`,
  },
  // 儿童个人信息保护政策
  [AgreementKeyEnum.CHILDREN_INFO_PROTECTION]: {
    text: '儿童个人信息保护政策',
    url: `${AGREEMENT_PREFIX_URL}${AgreementKeyEnum.CHILDREN_INFO_PROTECTION}`,
  },
  // 众安健康隐私保护指引
  [AgreementKeyEnum.HEALTH_INFO_PROTECTION]: {
    text: '众安健康隐私保护指引',
  },
  // 会员服务协议
  [AgreementKeyEnum.SERVICE_AGREEMENT]: {
    text: '众安保险会员服务协议',
    url: `${AGREEMENT_PREFIX_URL}${AgreementKeyEnum.SERVICE_AGREEMENT}`,
  },
};
