export const isObject = (obj: any): obj is object => {
  return Object.prototype.toString.call(obj) === '[object Object]';
};

export const isString = (obj: any): obj is string => {
  return Object.prototype.toString.call(obj) === '[object String]';
};

export const isTrue = (value: any): boolean => value === true;
export const isAnyTrue = (value: any): boolean => !!~[1, '1', 'Y', 'true', true].indexOf(value);
export const isAnyFalse = (value: any): boolean => !!~[0, '0', 'N', 'false', false].indexOf(value);

export const isValidArray = (value: any): value is any[] => Array.isArray(value) && !!value.length;

export const routerParamsStringify = (params: Partial<Record<string, string | number | boolean>>): string => {
  return Object.entries(params)
    .map(([key, value]) => ((typeof value !== 'undefined' && value !== '') ? `${key}=${value}` : ''))
    .filter(Boolean)
    .join('&');
};

export const routerParamsStringParse = (paramstring: string): Partial<Record<string, string | number>> => {
  const result = {};
  if (!paramstring) {
    return result;
  }
  const pairs = paramstring.split('&');
  pairs.forEach((pair) => {
    const [key, value] = pair.split('=');
    if (value) {
      result[key] = value;
    }
  });
  return result;
};

export const queryStringToJSON = (paramstring = ''): object => {
  const result = {};
  if (!paramstring) {
    return result;
  }
  const pairs = paramstring.split('&');
  pairs.forEach((pair) => {
    const p = pair.split('=');
    result[p[0]] = decodeURIComponent(p[1] || '');
  });
  return result;
};

export const appendQuery = (url = '', params: Partial<Record<string, string | number | boolean>>) => {
  const query = routerParamsStringify(params);
  const flag = url.indexOf('?');
  return flag > -1 ? `${url}&${query}` : `${url}?${query}`;
};

export const removeQuery = (url = '', keys: Array<string> = []) => {
  let query = url;
  const index = url.indexOf('?');
  if (index > -1) {
    query = url.substring(index + 1);
  }
  const obj = queryStringToJSON(query);
  const newObj = {};
  Object.keys(obj)?.forEach((key) => {
    if (keys.indexOf(key) === -1) {
      newObj[key] = obj[key];
    }
  });
  return `${url.substring(0, index)}?${routerParamsStringify(newObj)}`;
};

export function replaceURLQuery(url: string, newQuery: Partial<Record<string, string>>) {
  let query = url;
  const index = url.indexOf('?');
  if (index > -1) {
    query = url.substring(index + 1);
  }
  const obj = queryStringToJSON(query);
  return `${url.substring(0, index)}?${routerParamsStringify({ ...obj, ...newQuery })}`;
}
