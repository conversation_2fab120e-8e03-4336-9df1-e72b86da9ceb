const MOBILE = /^1[3|4|5|6|8|7|9][0-9]\d{8}$/;
const EMAIL = /^([^@\s]+)@((?:[-a-z0-9]+\.)+[a-z]{2,})$/;
const MONEY = /^([1-9][\d]{0,7}|0)(\.[\d]{1,2})?$/;
const NAME = /^[\u4e00-\u9fa5\s·]+$/;
const PWD = /(\d(?!\d{5})|[A-Za-z](?![A-Za-z]{5})){6}/;
const IDCARDNO = /(^\d{15}$)|(^\d{17}([0-9]|X)$)/;

export const isRule = (re, value) => {
  if (!value || value.length === 0) {
    return false;
  }

  if (!(re instanceof RegExp)) {
    throw new Error('The rule shoud be RegExp');
  }

  if (!re.test(value)) {
    return false;
  }
  return true;
};

export const isUserName = (value) => {
  return isRule(NAME, value);
};

export const isMobile = (value) => {
  return isRule(MOBILE, value);
};

export const isIdCard = (value) => {
  return isRule(IDCARDNO, value);
};
