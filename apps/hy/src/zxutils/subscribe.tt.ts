import { updateSubscribe } from '@/api';

export default function subscribe(tmplIds: string[], options?: { onBeforeRequestSubscribeMessage?: Function; onCompleteRequestSubscribeMessage?: Function }) {
  console.info(' tmplIds:', tmplIds);
  if (tmplIds.length === 0) {
    return false;
  }

  // 调起客户端小程序订阅消息界面
  tt.requestSubscribeMessage({
    tmplIds,
    success(succResp) {
      // 订阅后相关操作
      // const acceptedtemplateIds = getAcceptedTemplateIds(succResp, tmplIds);
      // console.info('requestSubscribeMessage succResp:', succResp, ' acceptedtemplateIds:', acceptedtemplateIds);
      // if (!acceptedtemplateIds.length) {
      //   return;
      // }
      // 调阅后端接口,添加调阅
      if (tmplIds?.length > 0) {
        updateSubscribe({
          templateIds: tmplIds.join(','),
        });
      }
    },
    complete(completeResp) {
      options?.onCompleteRequestSubscribeMessage?.(completeResp);
    },
  });
}

export const subscribeWxMessageByTmpls = () => {

};
