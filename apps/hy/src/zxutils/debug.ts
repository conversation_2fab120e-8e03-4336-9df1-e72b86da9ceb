import Taro from '@tarojs/taro';

const levels = ['log', 'debug', 'info', 'warn', 'error'] as const;
const levels4prod = ['info', 'warn', 'error'];
const debugs = {};
const isOnline = process.env.DEPLOY_ENV === 'prd';
let enable = !isOnline;
let colourful = true;

type IDebugLevels = typeof levels[number];
type IDebugInstance = Pick<Console, IDebugLevels> & { [key: string]: any };
interface IDebugCreate {
  new (prefix: string): IDebugInstance;
  (prefix: string): IDebugInstance;
}

if (Taro.getStorageSync('debug') === false) {
  enable = false;
}
if (Taro.getStorageSync('debug_colourful') === false) {
  colourful = false;
}

const getRandomColor = function getRandomColor() {
  const letters = '0123456789ABCDEF';
  let color = '#';
  for (let i = 0; i < 6; i += 1) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
};

const Debug: IDebugCreate = function _(this: IDebugInstance | void, prefix: string) {
  if (!(this instanceof Debug)) {
    return new Debug(prefix);
  }

  this.prefix = prefix;
  const _debug = debugs[this.prefix];
  if (_debug) {
    return _debug;
  }
  if (colourful) {
    this.color = getRandomColor();
  }
  debugs[this.prefix] = this;

  levels.forEach(function set(this: IDebugInstance, level) {
    if (!enable) {
      this[level] = () => {};
      return;
    }
    if (isOnline && !levels4prod.includes(level)) {
      this[level] = () => {};
    } else if (colourful) {
      // eslint-disable-next-line
      this[level] = console[level].bind(console, `%c [${this.prefix}] ${level.toUpperCase()}`, `color: ${this.color}`);
    } else {
      // eslint-disable-next-line
      this[level] = console[level].bind(console, `[${this.prefix}] ${level.toUpperCase()}`);
    }
  }, this);
} as IDebugCreate;

export default Debug;
