import { fetchHospitalToken } from '@/api';
import Taro from '@tarojs/taro';
import { navigateToMiniProgram, navigateToLink } from '@/utils';

export const goHospital = async (sourceCode, path) => {
  const { code, result = {} } = await fetchHospitalToken({ channelResourceCode: sourceCode });

  if (Number(code)) {
    return;
  }

  const { token, hyChannelSource } = result;
  Taro.navigateToMiniProgram({
    appId: 'wx3c564538ea8e3905',
    path: `pages/autoLogin?token=${token}&channelResourceCode=${sourceCode}&path=${path}`,
  });
};

// const template = function (url, params) {
//   const names = Object.keys(params);
//   const vals = Object.values(params);
//   // eslint-disable-next-line
//   return new Function(...names, `return \`${url}\`;`)(...vals);
// };

export const goHospitalWeb = async (sourceCode, url) => {
  const { code, result = {} } = await fetchHospitalToken({ channelResourceCode: sourceCode });

  if (Number(code)) {
    return;
  }

  const { token, hyChannelSource } = result;

  navigateToLink(`${url}&token=${token}`, {
    webViewNeedLogin: '0',
    webViewShareLink: '0',
  });
};
