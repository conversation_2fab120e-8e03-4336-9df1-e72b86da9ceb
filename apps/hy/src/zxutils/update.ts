import { getUpdateManager, showModal } from '@tarojs/taro';

const updateManager = getUpdateManager();
/** 请求更新到最新版 */
export const requestUpdate = () => {
  return new Promise((resolve, reject) => {
    updateManager.onCheckForUpdate(() => {});
    updateManager.onUpdateReady(() => {
      showModal({
        title: '更新提示',
        content: '新版本已经准备好，是否重启应用？',
        showCancel: false,
        confirmText: '重启',
        success: ({ confirm }) => {
          if (confirm) {
            updateManager.applyUpdate();
            resolve(undefined);
          }
        },
      });
    });
    updateManager.onUpdateFailed((error) => reject(new Error(error.errMsg)));
  });
};

export default requestUpdate;
