import Taro from '@tarojs/taro';

export * from './navigate';
export * from './tools';
export * from './goHospital';

export const isOk = (msg: string) => !msg || !!~msg.indexOf('ok');
const { TARO_ENV } = process.env;
export const isWechat = TARO_ENV === 'weapp';
export const isByteDance = TARO_ENV === 'tt';
export const isAliPay = TARO_ENV === 'alipay';

const transform = (obj, predicate) => {
  return Object.keys(obj).reduce((memo, key) => {
    if (predicate(obj[key], key)) {
      memo[key] = obj[key];
    }
    return memo;
  }, {});
};

export const omit = (obj, items) => transform(obj, (value, key) => !items.includes(key));

export function hasSub(tpl: Array<string> = []) {
  return new Promise((resolve, reject) => {
    wx.getSetting({
      withSubscriptions: true,
      success: async (setting) => {
        // if ()
        const flag = tpl?.every((item) => {
          return setting?.subscriptionsSetting?.[item] === 'accept';
        });
        flag ? resolve(true) : reject();
      },
    });
  });
}

export function groupBy(list, name) {
  return list?.reduce((obj, item) => {
    if (!obj[item[name]]) {
      obj[item[name]] = [];
    }
    obj[item[name]].push(item);
    return obj;
  }, {});
}

export function debounce(fn, interval) {
  let timer;
  const gapTime = interval || 300;
  return (...args) => {
    clearTimeout(timer);
    timer = setTimeout(() => {
      return fn(...args);
    }, gapTime);
  };
}

export function throttle(fn, wait) {
  let inThrottle;
  let lastFn;
  let lastTime;
  return (...args) => {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const context = this;
    // eslint-disable-next-line
    if (!inThrottle) {
      fn.apply(context, args);
      lastTime = Date.now();
      inThrottle = true;
    } else {
      clearTimeout(lastFn);
      lastFn = setTimeout(() => {
        if (Date.now() - lastTime >= wait) {
          fn.apply(context, args);
          lastTime = Date.now();
        }
      }, Math.max(wait - (Date.now() - lastTime), 0));
    }
  };
}

type cancelFunction = () => void;

export function cancelable(fn, ctx?) {
  let cancelPrev: null | cancelFunction = null;

  return (...args) => {
    if (cancelPrev) {
      cancelPrev?.();
      cancelPrev = null;
    }
    return Promise.race([
      fn.apply(ctx, args),
      new Promise((_resolve, reject) => {
        cancelPrev = () => {
          // eslint-disable-next-line prefer-promise-reject-errors
          reject('cancel');
        };
      }),
    ]);
  };
}

export function sleep(time: number) {
  return new Promise((resolve) => {
    setTimeout(resolve, time);
  });
}

export function safeURL(url) {
  if (!url?.startsWith('https') && url?.startsWith('http')) {
    return encodeURI(url.replace('http', 'https'));
  }
  return encodeURI(url);
}

export function getYMD() {
  const date = new Date();
  const dayStr = `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;
  return dayStr;
}

export function getYMDTime(dateStr: string) {
  const date = new Date(dateStr?.replace(/-/g, '/'));

  const day = date.getDate();
  const month = date.getMonth() + 1;
  return {
    year: date.getFullYear(),
    month: month >= 10 ? month : `0${month}`,
    day: day >= 10 ? day : `0${day}`,
  };
}

export function getAccountInfoSync() {
  if (TARO_ENV === 'weapp') {
    return Taro.getAccountInfoSync().miniProgram;
  }

  if (TARO_ENV === 'tt') {
    const { microapp } = Taro.getEnvInfoSync();
    const { mpVersion: version, appId, envType: envVersion } = microapp;
    return {
      miniProgram: {
        version,
        appId,
        envVersion,
      },
    };
  }

  return {
    version: '',
    appId: '',
    envVersion: '',
  };
}

/**
 * 判断当前页面是否tab页
 * @param 当前页面路径
*/
export const isTabPage = (path) => {
  const { tabBar } = Taro.getApp().config;
  return tabBar && tabBar.list.some((item) => item.pagePath === path);
};
