import config from '@/config';

const { defaultChannelCode, defaultChannelSource } = config;

export const CLAIM_URL = `/policy/claimList?channelCode=${defaultChannelCode}`;
export const CLAIM_PROGRESS_URL = `/api/lemon/v1/claim/claimSchedule/${defaultChannelCode}`;
export const POLICY_URL = `/policy/list?channelCode=${defaultChannelCode}&channelSource=${defaultChannelSource}&backNeedRefresh=1`;
export const LOGIN_URL = `/policy/auth/login?channelCode=${defaultChannelCode}`;
export const AUTH_BIND_URL = `/policy/auth/bind?channelCode=${defaultChannelCode}`;
export const CLAIM_PROCESS_URL = `/claim/list?channelCode=${defaultChannelCode}`;
export const TEST_URL = '/policy/test';

export const VERIFY_URL = `/policy/auth/bind?channelCode=${defaultChannelCode}`;
export const VERIFY_STATUS_URL = `/policy/auth/artificialVerify/status?channelCode=${defaultChannelCode}`;

export default {
  CLAIM_URL,
  POLICY_URL,
  TEST_URL,
  LOGIN_URL,
  AUTH_BIND_URL,
  CLAIM_PROCESS_URL,
  VERIFY_URL,
  VERIFY_STATUS_URL,
};
