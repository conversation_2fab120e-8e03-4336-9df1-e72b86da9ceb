import { env } from '@/config';
import Storage from 'shared/storage';

const storage = new Storage({ env });

export const setStorageSync = (key: string, data: any, time?: number) => storage.setStorageSync(key, data, time);

export const setStorage = (key: string, data: any) => storage.setStorage(key, data);

export const getStorageSync = (key: string) => storage.getStorageSync(key);

export const removeStorageSync = (key: string) => storage.removeStorageSync(key);

// export const getStorage = (key: string) => storage.getStorage(key);

export const getStorage = (key: string) => storage.getStorage(key);
