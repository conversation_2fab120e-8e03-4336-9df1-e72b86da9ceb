import Taro from '@tarojs/taro';
import FetchAPI from 'shared/fetch';
import Storage from 'shared/storage';
import config from '@/config';
import Debug from './debug';
import Log from './log';

const { API, defaultChannelCode, env } = config;

const MPMAP = {
  tt: 'byteDanceApplet',
  weapp: 'wechatApplet',
  alipay: 'shareApplet',
};

export const MPPATH = MPMAP[process.env.TARO_ENV!];

const { host } = API?.lemon || {};

const storage = new Storage({ env });

export { type IResponse } from 'shared/fetch';

export const once = <T extends (...args: any[]) => Promise<any>>(promiseGenerator: T): T => {
  let promise: Promise<any> | undefined;

  return (async (...args: any[]) => {
    promise = promise || promiseGenerator(...args);
    let res;
    try {
      res = await promise;
    } catch (error) {
      promise = undefined;
      throw error;
    }
    promise = undefined;
    return res;
  }) as T;
};

const debug = Debug('fetch');

const middlewares: Array<(res: Taro.request.SuccessCallbackResult, requestParams) => any> = [
  // 提示错误信息
  (response, requestParams) => {
    const { data = {} } = response;
    const { code, message = '服务异常' } = data;
    const { showErrorMessage = true } = requestParams;
    if (code !== '0' && Number(code) >= 20000 && message !== null) {
      if (process.env.TARO_ENV === 'weapp' && config?.denv === 'prd') {
        // @ts-ignore:next-line
        Log.error('request_custom_error', {
          url: requestParams?.url,
          code,
          message,
          data: requestParams?.data,
          token: requestParams?.header?.['Access-Token'],
        });
      }
      if (showErrorMessage) {
        Taro.showToast({
          icon: 'none',
          title: message,
          duration: 5000,
        });
      }
    }

    return response;
  },
];

const responseMiddleInterceptor: Taro.interceptor = (chain) => {
  const { requestParams } = chain;
  const p = chain.proceed(requestParams);
  const res = p.then((source) => {
    return middlewares.reduce((response, middleware) => middleware(response, requestParams), source);
  });
  if (typeof p.abort === 'function') res.abort = p.abort;
  return res;
};
const appBaseInfo = Taro?.getAppBaseInfo?.();

const logGroupInterceptor: Taro.interceptor = (chain) => {
  const { requestParams } = chain;
  const { method, data, url } = requestParams;
  const p = chain.proceed(requestParams);
  const res = p.then((response) => {
    if ((appBaseInfo?.enableDebug || !appBaseInfo)) {
      // eslint-disable-next-line no-console
      console?.groupCollapsed?.(` fetch ${method || 'GET'} @ ${url}`);
      // eslint-disable-next-line no-console
      // console.group(` fetch ${method || 'GET'} @ ${url}`);
      debug.log('method:   ', method);
      debug.log('url:      ', url);
      debug.log('body:     ', data);
      debug.log('response: ', response);
      debug.log('result:   ', response.data);
      // eslint-disable-next-line no-console
      console?.groupEnd?.();
    }

    return response;
  });
  if (typeof p.abort === 'function') res.abort = p.abort;
  return res;
};

const statusInterceptor: Taro.interceptor = (chain) => {
  const { requestParams } = chain;
  const p = chain.proceed(requestParams);
  const res = p.then(
    (response) => {
      const { status, statusCode = status, statusText = '网络异常，请稍后重试', data } = response;
      if ((statusCode !== 200 && process.env.TARO_ENV === 'weapp' && config?.denv === 'prd') || data?.code === '40003') {
        // @ts-ignore:next-line
        Log.error('request_error', {
          requestParams: {
            url: requestParams?.url,
            data: requestParams?.data,
            token: requestParams?.token,
          },
          response: {
            data: response?.data,
            code: response?.code,
          },
          statusCode,
        });
      }
      if (statusCode >= 400 && statusCode < 600) {
        Taro.showToast({
          icon: 'none',
          title: statusText,
        });
        return Promise.reject(response);
      }

      return response;
    },
    // 支付宝网络请求状态异常会触发 onRejected，同时重置 promise 状态
    (response) => {
      const { status, statusCode = status, statusText = '网络异常，请稍后重试' } = response;

      if (statusCode >= 400 && statusCode < 600) {
        Taro.showToast({
          icon: 'none',
          title: statusText,
        });
      }

      return Promise.reject(response);
    },
  );
  if (typeof p.abort === 'function') res.abort = p.abort;
  return res;
};

const fetchAPI = new FetchAPI({
  apiConfig: API,
  storage,
  authOptions: {
    url: `${host}/v1/${MPPATH}/login/${defaultChannelCode}`,
    data: process.env.TARO_ENV === 'alipay' && config.alipayAppId ? { appId: config.alipayAppId } : {},
  },
});

FetchAPI.addInterceptor([responseMiddleInterceptor, logGroupInterceptor, statusInterceptor]);

export const fetchToken = (force = false) => {
  return fetchAPI.fetchToken(force);
};

export const getToken = () => {
  return fetchAPI.getToken();
};

export const removeToken = () => {
  return fetchAPI.removeToken();
};

export default fetchAPI;
