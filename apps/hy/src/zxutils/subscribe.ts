import Taro from '@tarojs/taro';
import { updateSubscribe } from '@/api';
import { getStorageSync, setStorageSync } from './storage';

const core = (options, cb: () => void) => {
  if (process.env.TARO_ENV !== 'weapp') {
    return cb();
  }

  const key = `subscribe_date_${options?.tmplIds?.code}`;
  const today = new Date(new Date().toLocaleDateString()).getTime();
  const date = (getStorageSync(key) as number) || 0;

  if (date > today) {
    return cb();
  }
  const { ids: tmplIds, longterm } = options?.tmplIds || {};

  if (!tmplIds || !tmplIds?.length) {
    return cb();
  }

  const request = () => {
    options?.onBeforeRequestSubscribeMessage?.();
    wx.requestSubscribeMessage({
      tmplIds,
      success(successRes) {
        const templateIds = tmplIds.filter((item) => successRes[item] === 'accept').join(',');
        if (templateIds) {
          updateSubscribe({ templateIds });
        }
      },
      complete() {
        options?.onCompleteRequestSubscribeMessage?.();
        setStorageSync(key, Date.now());
        cb();
      },
    });
  };

  if (!longterm) {
    return request();
  }

  wx.getSetting({
    withSubscriptions: true,
    success(res) {
      if (!res?.subscriptionsSetting?.mainSwitch) {
        Taro.showToast({
          title: '请在设置里面打开订接收阅通知',
          icon: 'none',
        });
      }
      const { itemSettings = {} } = res.subscriptionsSetting;
      let flag = false;
      tmplIds.forEach((id) => {
        // 存在未曾询问过用户的长期订阅模板时
        if (!itemSettings[id]) {
          flag = true;
        }
      });
      flag ? request() : cb();
    },
  });
};

export const subscribeWxMessageByTmpls = (options: { tmplIds: Array<string>; onBeforeRequestSubscribeMessage: Function; onCompleteRequestSubscribeMessage: Function }, cb) => {
  core(options, cb);
};

// export const getInsureSubscribe = (tmplIds, cb: () => void) => {
//   core('insure', cb);
// };

// export const getClaimSubscribe = (cb: () => void) => {
//   core('claim', cb);
// };

function getAcceptedTemplateIds(data = {}, tmplIds: string[] = []) {
  const respkeyArr = Object.keys(data);
  const acceptedtemplateIds = [] as string[];

  respkeyArr.forEach((respKey) => {
    const state = data[respKey];
    if (tmplIds.includes(respKey) && state === 'accept') {
      acceptedtemplateIds.push(respKey);
    }
  });
  return acceptedtemplateIds;
}

// function hasAcceptedAll(data = {}, tmplIds: string[] = []) {
//   console.log(data, '____________________________________________');
//   const respkeyArr = Object.keys(data);
//   const hasDone = respkeyArr.filter((respKey) => data[respKey] === 'accept');
//   console.log(hasDone, tmplIds, '----------------->');
//   return tmplIds.every((item) => {
//     return hasDone.indexOf(item) > -1;
//   });
// }

// 通过`key`获取模板id
export function getSigntmplIdsByKey(config = {}, key = '') {
  const { subscribe_tmplIds: signTmplIds = {} } = config as any;
  if (!key) {
    return [];
  }
  const tmpCfg = signTmplIds[key];
  const ids = tmpCfg?.ids;

  if (!ids || ids.length === 0) {
    return [];
  }

  return ids;
}

// withSubscriptions 只返回用户勾选过订阅面板中的“总是保持以上选择，不再询问”的订阅消息。
export function getSubscribedState() {
  return new Promise((resolve) => {
    wx.getSetting({
      withSubscriptions: true,
      success(res) {
        const sub = res.subscriptionsSetting || {};
        const { itemSettings = {} } = sub;
        const keysArr = Object.keys(itemSettings);
        const valsArr = Object.values(itemSettings);
        console.info(' res>>>>', res, ' keysArr:', keysArr, ' valsArr:', valsArr);
        resolve({ subscriptionIds: keysArr, subscriptionStatus: valsArr });
      },
    });
  });
}

export default function subscribe(tmplIds: string[], options?: { onBeforeRequestSubscribeMessage?: Function; onCompleteRequestSubscribeMessage?: Function }) {
  console.info(' tmplIds:', tmplIds);
  if (tmplIds?.length === 0) {
    return Promise.resolve(false);
  }
  // 获取用户对相关模板消息的订
  // wx.openSetting();
  wx.getSetting({
    withSubscriptions: true,
    success(res) {
      if (!res?.subscriptionsSetting?.mainSwitch) {
        Taro.showToast({
          title: '请在设置里面打开订接收阅通知',
          icon: 'none',
        });
        options?.onCompleteRequestSubscribeMessage?.();
        return false;
      }
      // if ()
      const allWays = tmplIds?.every((item) => {
        return res?.subscriptionsSetting?.itemSettings?.[item];
      });

      // const allReject = tmplIds?.every((item) => {
      //   return res?.subscriptionsSetting?.itemSettings?.[item] === 'reject';
      // });
      if (!allWays) {
        options?.onBeforeRequestSubscribeMessage?.();
      }
      // 调起客户端小程序订阅消息界面
      wx.requestSubscribeMessage({
        tmplIds,
        success(succResp) {
          // 订阅后相关操作
          const acceptedtemplateIds = getAcceptedTemplateIds(succResp, tmplIds);
          console.info('requestSubscribeMessage succResp:', succResp, ' acceptedtemplateIds:', acceptedtemplateIds);
          if (!acceptedtemplateIds?.length) {
            return;
          }
          // 调阅后端接口,添加调阅
          updateSubscribe({
            templateIds: acceptedtemplateIds.join(),
          });
        },
        complete(completeResp) {
          options?.onCompleteRequestSubscribeMessage?.(completeResp);
        },
      });
    },
  });
}

export function subscribeTpl(tmplIds: string[]) {
  console.info(' tmplIds:', tmplIds);

  return new Promise((resolve, reject) => {
    if (tmplIds?.length === 0) {
      return resolve(undefined);
    }
    // const sub = res.subs
    // 获取用户对相关模板消息的订
    wx.getSetting({
      withSubscriptions: true,
      success(res) {
        // if ()
        if (!res?.subscriptionsSetting?.mainSwitch) {
          Taro.showToast({
            title: '请在设置里面打开订接收阅通知',
            icon: 'none',
          });
          return resolve(undefined);
        }
        const flag = tmplIds?.every((item) => {
          return res?.subscriptionsSetting?.[item] === 'accept';
        });
        // if (!flag) {
        //   store.dispatch(updateSubscribleMask(true));
        // }
        // 调起客户端小程序订阅消息界面
        wx.requestSubscribeMessage({
          tmplIds,
          success(succResp) {
            // 订阅后相关操作
            const acceptedtemplateIds = getAcceptedTemplateIds(succResp, tmplIds);
            console.info('requestSubscribeMessage succResp:', succResp, ' acceptedtemplateIds:', acceptedtemplateIds);
            if (!acceptedtemplateIds?.length) {
              return resolve(succResp);
            }
            // 调阅后端接口,添加调阅
            updateSubscribe({
              templateIds: acceptedtemplateIds.join(),
            });
            resolve(succResp);
          },
          fail(failRes) {
            reject(failRes);
          },
          complete() {
            // store.dispatch(updateSubscribleMask(false));
          },
        });
        // }
      },
    });
  });
}

export function activitySubscribeTpl(tmplIds: string[], callback?: (args: { templateIds: string }) => Promise<any>) {
  console.info('activitySubscribeTpl:', tmplIds);

  return new Promise((resolve, reject) => {
    if (tmplIds?.length === 0) {
      return resolve(undefined);
    }
    // const sub = res.subs
    // 获取用户对相关模板消息的订
    wx.getSetting({
      withSubscriptions: true,
      success(res) {
        // if ()
        if (!res?.subscriptionsSetting?.mainSwitch) {
          return reject();
          // return false;
        }
        // 调起客户端小程序订阅消息界面
        wx.requestSubscribeMessage({
          tmplIds,
          success(succResp) {
            // 订阅后相关操作
            const acceptedtemplateIds = getAcceptedTemplateIds(succResp, tmplIds);
            if (!acceptedtemplateIds?.length) {
              return reject();
            }
            // 调阅后端接口,添加调阅
            if (callback) {
              callback({
                templateIds: acceptedtemplateIds.join(),
              })
                .then(() => resolve(succResp))
                .catch(() => {
                  reject();
                });
            } else {
              updateSubscribe({
                templateIds: acceptedtemplateIds.join(),
              })
                .then(() => resolve(succResp))
                .catch(() => {
                  reject();
                });
            }
          },
          fail(failRes) {
            reject(failRes);
          },
          complete() {},
        });
        // }
      },
    });
  });
}

export function hasSubscribeTpl(tmplIds: string[]): Promise<boolean> {
  console.info(' tmplIds:', tmplIds);

  return new Promise((resolve, reject) => {
    if (tmplIds?.length === 0) {
      return resolve(true);
    }
    // const sub = res.subs
    // 获取用户对相关模板消息的订
    wx.getSetting({
      withSubscriptions: true,
      success(res) {
        // if ()
        if (!res?.subscriptionsSetting?.mainSwitch) {
          // Taro.showToast({
          //   title: '请在设置里面打开订接收阅通知',
          //   icon: 'none',
          // });
          return resolve(false);
        }
        const flag = tmplIds?.every((item) => {
          return res?.subscriptionsSetting?.[item] === 'accept';
        });
        resolve(flag);
      },
    });
  });
}
