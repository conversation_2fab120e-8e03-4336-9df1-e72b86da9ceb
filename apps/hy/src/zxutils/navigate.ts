import Taro from '@tarojs/taro';
import { REDIRECT_TYPE } from '@/constants';
import config, { env, configs } from '@/config';
import { authorize } from '@/api';
import { isObject, isString, isTrue, isAnyTrue, routerParamsStringify, appendQuery } from './tools';
import { LOGIN_URL, AUTH_BIND_URL } from './sites';

// ！！！！！！！！！！
// 保持和环境中的 router params 一直
// 后续跳转参数 params 内的 url 请都做好 encodeURIComponent
// ！！！！！！！！！！

const defaultLinkConfig = {
  webViewNeedLogin: '1',
  webViewShareLink: '1',
  webViewInheritParams: '1',
};
const defaultNavigateToUrlType = 'MiniProgramWebView';
const defaultReturnToUrlType = 'MiniProgramPage';

export interface IWebViewParams {
  i?: string;
  iE?: string;
  url?: string;
  // force, 1 必须登录token, force 判断是否有手机号, atuo 如果存在token就带上
  needLogin?: 'force' | 'auto' | '1' | '0';
  shareLink?: string;
  authURL?: string;
  inheritParams?: string;
  hideHomeButton?: '1' | '0';
}

interface INavigateToLinkOptions {
  navigateType?: 'navigateTo' | 'redirectTo';
  webViewTitle?: string;
  webViewShareImageUrl?: string;
  webViewNeedLogin?: boolean | number | string;
  webViewShareLink?: boolean | number | string;
  webViewInheritParams?: boolean | number | string;
  webViewExtraParams?: Record<string, string | number | boolean>;
}

export const navigateToLink = (link: string, options: boolean | INavigateToLinkOptions = defaultLinkConfig) => {
  if (!isString(link)) {
    throw new Error('参数不对！');
  }

  if (isObject(options)) {
    options = {
      ...defaultLinkConfig,
      ...options,
    };
  }

  const needLogin: IWebViewParams['needLogin'] = isTrue(options) || isAnyTrue((options as INavigateToLinkOptions)?.webViewNeedLogin) ? '1' : '0';
  const shareLink: IWebViewParams['shareLink'] = isTrue(options) || isAnyTrue((options as INavigateToLinkOptions)?.webViewShareLink) ? '1' : '0';
  const inheritParams = isTrue(options) || isAnyTrue((options as INavigateToLinkOptions)?.webViewInheritParams) ? '1' : '0';

  let navigateType = 'navigateTo';
  if (isObject(options) && options.navigateType) {
    navigateType = options.navigateType;
  }

  // 支付宝 h5 页面的特殊跳转方式
  if (process.env.TARO_ENV === 'alipay') {
    if (link.match(/^(alipays:\/\/|https:\/\/render\.alipay\.com\/p)/)) {
      my.ap.navigateToAlipayPage({ path: link });
      return;
    }
  }

  const params = {
    ...(options as INavigateToLinkOptions)?.webViewExtraParams,
    url: encodeURIComponent(link),
    needLogin,
    shareLink,
    inheritParams,
    title: (options as INavigateToLinkOptions)?.webViewTitle,
    imageUrl: (options as INavigateToLinkOptions)?.webViewShareImageUrl,
  };

  Taro[navigateType]({
    url: `/pages/web/index?${routerParamsStringify(params)}`,
  });
};

interface INavigateToMiniProgram {
  appId: string;
  path?: string;
  success?: Function;
  extraData: Record<string, any>;
  isHalfScreen?: 'Y' | 'N';
}
interface AuthOptions {
  disableEnv?: boolean;
}

export const navigateToMiniProgram = (link: string | INavigateToMiniProgram, options?: AuthOptions) => {
  let version: any;
  if (process.env.TARO_ENV === 'weapp' && !options?.disableEnv) {
    version = {
      // eslint-disable-next-line no-undef
      envVersion: __wxConfig?.envVersion,
    };
  }

  if (typeof link === 'string') {
    Taro.navigateToMiniProgram({ appId: link, ...version });
    return;
  }

  if (!isObject(link) || !link.appId) {
    throw new Error('navigateToMiniProgram: 参数不对！');
  }

  if (link?.isHalfScreen === 'Y') {
    Taro.openEmbeddedMiniProgram({
      ...link,
      ...version,
    });
    return;
  }
  Taro.navigateToMiniProgram({ ...link, ...version });
};

type INavigateTo = {
  /**
   * 统一使用 url
   * @deprecated
   */
  href?: string;
  /**
   * 统一使用 webViewNeedLogin
   * @deprecated
   */
  needLogin?: INavigateToLinkOptions['webViewNeedLogin'];

  appId?: string;
  authId?: string;
  navigateType?: 'navigateBack' | 'navigateTo' | 'switchTab' | 'redirectTo';
  urlType?: 'MiniProgramPage' | 'MiniProgramWebView';
  url?: string;
  isHalfScreen?: 'Y' | 'N';
} & Partial<Pick<INavigateToLinkOptions, 'webViewTitle' | 'webViewShareImageUrl' | 'webViewNeedLogin' | 'webViewShareLink' | 'webViewInheritParams'>>;

export type IReturnProps = Partial<Pick<INavigateTo, 'urlType' | 'webViewNeedLogin' | 'webViewShareLink' | 'webViewInheritParams'>> & {
  redirectUrl?: INavigateTo['url'];
  redirectType?: INavigateTo['navigateType'];
  url?: INavigateTo['url'];
};

export type IReturnParams = IReturnProps;

/**
 * @deprecated
 */
export const getDefaultReturnParams = (params?: IReturnParams) => {
  return {
    redirectType: 'navigateBack',
    url: '',
    urlType: 'MiniProgramPage',
    webViewNeedLogin: '1',
    ...params,
  };
};

export const defaultReturnParams: IReturnParams = {
  redirectType: 'navigateBack',
  urlType: defaultReturnToUrlType,
  ...defaultLinkConfig,
};

export const appendDefaultReturnParams = (params?: IReturnParams): IReturnParams => {
  return {
    ...defaultReturnParams,
    ...params,
  };
};

type ICurrentRouterInfo = Pick<Taro.RouterInfo, 'path' | 'params'> & {
  url: string;
};

export const getCurrentRouterInfo = () => {
  let result: ICurrentRouterInfo = {
    path: '',
    params: {},
    url: '',
  };

  try {
    const instance = Taro.getCurrentInstance();
    const { router } = instance;
    if (router) {
      const { path, params } = router;

      result = {
        path,
        params,
        url: `${path}?${routerParamsStringify(params)}`,
      };
    }
  } catch (e) {
    // ignore
  }
  return result;
};

export const getCurrentPageUrl = (extraParams?: any) => {
  const { path, params } = getCurrentRouterInfo();
  const newParams = process.env.TARO_ENV === 'tt'
    ? Object.keys(params).reduce((acc, key) => {
      if (key === 'url' || key === 'redirectUrl') {
        acc[key] = encodeURIComponent(params[key]!);
      } else {
        acc[key] = params[key];
      }
      return acc;
    }, {})
    : params;
  const currentPageUrl = `${path}?${routerParamsStringify({
    ...newParams,
    ...extraParams,
  })}`;

  return currentPageUrl;
};

export const getReturnParamsFromRouter = (): IReturnParams => {
  const { params } = getCurrentRouterInfo();
  return appendDefaultReturnParams(params);
};

export const genReturnParams = (params: IReturnParams = {}) => {
  const currentPageUrl = getCurrentPageUrl();
  const { redirectUrl = encodeURIComponent(currentPageUrl), redirectType = redirectUrl ? 'redirectTo' : 'navigateBack', ...restParams } = params;

  console.log('redirectUrl:', redirectUrl);
  return appendDefaultReturnParams({
    redirectUrl,
    redirectType,
    ...restParams,
  });
};

export const genReturnSearch = (params: IReturnParams = {}) => {
  const returnParams = genReturnParams(params);
  const returnParamsString = routerParamsStringify(returnParams);

  return `?${returnParamsString}`;
};

export const getIUrlById = (i: string, iEnv: string) => {
  let host = config.iOrigin;
  if (iEnv && configs[iEnv]?.iOrigin) {
    host = configs[iEnv].iOrigin;
  }
  return i ? `${host}/${i}` : '';
};

export const navigateToLogin = (params?: IReturnParams, options?: INavigateToLinkOptions) => {
  const search = genReturnSearch(params);

  navigateTo({
    url: `/pages/login/index${search}`,
    urlType: 'MiniProgramPage',
    ...options,
  });
};

export const navigateToH5Login = (params?: IReturnParams, options?: INavigateToLinkOptions) => {
  const { redirectUrl, webViewNeedLogin, webViewShareLink, webViewInheritParams, ...returnParams } = genReturnParams(params);

  // TODO 兼容 policy 对接参数，policy auth 服务需要优化
  if (!redirectUrl) {
    returnParams.urlType = 'MiniProgramPage';
  }

  const mpLoginUrl = getCurrentPageUrl({ from: 'zunxiang_mp' });
  const returnParamsString = routerParamsStringify({
    redirectUrl,
    ...returnParams,
    mpLoginUrl: encodeURIComponent(mpLoginUrl),
  });

  const loginUrl = `${LOGIN_URL}&${returnParamsString}`;

  navigateToLink(loginUrl, {
    webViewShareLink: '0',
    ...options,
  });
};

export const navigateToH5BindVerify = (params?: IReturnParams, options?: INavigateToLinkOptions) => {
  const { redirectUrl, webViewNeedLogin, webViewShareLink, url, ...returnParams } = genReturnParams(params);

  // TODO 兼容 policy 对接参数，policy auth 服务需要优化
  if (!redirectUrl) {
    returnParams.urlType = 'MiniProgramPage';
  }

  const returnParamsString = routerParamsStringify({
    redirectUrl,
    ...returnParams,
  });

  const authUrl = `${url}&${returnParamsString}`;

  navigateToLink(authUrl, {
    webViewShareLink: '0',
    ...options,
  });
};

export const navigateToH5Bind = (params?: IReturnParams, options?: INavigateToLinkOptions) => {
  // const { redirectUrl, webViewNeedLogin, webViewShareLink, ...returnParams } = genReturnParams(params);

  // // TODO 兼容 policy 对接参数，policy auth 服务需要优化
  // if (!redirectUrl) {
  //   returnParams.urlType = 'MiniProgramPage';
  // }

  // const returnParamsString = routerParamsStringify({
  //   redirectUrl,
  //   ...returnParams,
  // });

  // const authUrl = `${AUTH_BIND_URL}&${returnParamsString}`;

  // navigateToLink(authUrl, {
  //   webViewShareLink: '0',
  //   ...options,
  // });
  navigateToH5BindVerify({
    url: AUTH_BIND_URL,
    ...params,
    ...options,
  });
};

/**
 * 仅兼容方案，主要为配置的链接服务
 * 建议使用目的更明确的 navigateToLink、navigateToMiniProgram 代替
 */

export const navigateTo = async ({
  href,
  needLogin = '1',
  appId,
  authId,
  navigateType = 'navigateTo',
  urlType = defaultNavigateToUrlType,
  url = href,
  webViewTitle,
  webViewShareImageUrl,
  webViewNeedLogin = needLogin,
  webViewShareLink = '1',
  isHalfScreen = 'N',
}: INavigateTo) => {
  if (authId) {
    const { result, code = '', message } = await authorize(authId);
    if (parseInt(code, 10) === 0) {
      if (parseInt(result.authStatus, 10) === 1) {
        return navigateToLogin();
      }
      url = appendQuery(url, { healthAuthCode: result?.authCode });
    } else {
      Taro.showToast({
        title: message || '授权失败',
        icon: 'none',
      });
    }
  }

  if (appId && appId !== config.appId) {
    console.log(url);
    navigateToMiniProgram({
      appId,
      path: url,
      extraData: {},
      isHalfScreen,
    });
    return;
  }

  if (navigateType === 'navigateBack') {
    Taro[navigateType]();
    return;
  }

  if (!url) {
    console.warn('参数不对！！！');
    return;
  }

  // tab
  if (['/pages/index/index', '/pages/home/<USER>', '/pages/productList/index', '/pages/post/index'].indexOf(url) > -1) {
    Taro.switchTab({ url });
    return false;
  }

  if (urlType === 'MiniProgramPage') {
    Taro[navigateType]({ url });
  }

  if (urlType === 'MiniProgramWebView') {
    navigateToLink(url, {
      // @ts-ignore
      navigateType,
      webViewTitle,
      webViewShareImageUrl,
      webViewNeedLogin: authId ? 0 : webViewNeedLogin, // 这种授权的不要重定向
      webViewShareLink,
    });
  }
};

export const navigateToReturn = (params?: IReturnParams) => {
  const { redirectUrl: _url, redirectType, url, ...rest } = params || getReturnParamsFromRouter();
  const finalUrl = _url ? decodeURIComponent(_url) : decodeURIComponent(url!);

  const options = {
    url: finalUrl,
    navigateType: redirectType,
    ...rest,
  };

  console.log(options);
  navigateTo(options);
};

export const bannerLinkTo = (item) => {
  const redirectType = item.pictureRedirectType || item.redirectType;
  const url = item.pictureRedirectUrl || item.redirectUrl;
  const appId = item.redirectAppId || item.redirectAppID;

  switch (redirectType) {
    case REDIRECT_TYPE.H5:
    case REDIRECT_TYPE.H5_DYNAMIC:
      if (!item.pictureRedirectUrl) {
        throw new Error(`跳转添加为空${JSON.stringify(item)}`);
      }
      navigateToLink(item.pictureRedirectUrl);
      break;
    case REDIRECT_TYPE.MINIPROGRAM:
    case REDIRECT_TYPE.MINIPROGRAM_DOUYIN:
      navigateTo({
        appId,
        url,
        urlType: 'MiniProgramPage',
        isHalfScreen: item.isHalfScreen,
      });
      break;
    default:
    // todo
  }
};

export const navigateBack = () => {
  const pages = Taro.getCurrentPages();
  if (pages.length > 1) {
    Taro.navigateBack();
  } else {
    // TODO use Taro.switchTab
    Taro.switchTab({
      url: '/pages/index/index',
    });
  }
};
