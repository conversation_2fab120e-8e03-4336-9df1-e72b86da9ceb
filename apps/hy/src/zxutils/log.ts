const WX = process.env.TARO_ENV === 'weapp' ? wx : {};
const logManager = WX?.getRealtimeLogManager ? WX?.getRealtimeLogManager() : null;

const Log = {
  debug() {
    // eslint-disable-next-line
    logManager?.debug?.apply(logManager, arguments);
  },
  info() {
    // eslint-disable-next-line
    logManager?.info?.apply(logManager, arguments);
  },
  warn() {
    // eslint-disable-next-line
    logManager?.warn?.apply(logManager, arguments);
  },
  error(...agrs) {
    // eslint-disable-next-line
    logManager?.error?.apply(logManager, agrs);
  },
  setFilterMsg(msg) { // 从基础库2.7.3开始支持
    if (typeof msg !== 'string') return;
    logManager?.setFilterMsg(msg);
  },
  addFilterMsg(msg) { // 从基础库2.7.3开始支持
    if (typeof msg !== 'string') return;
    logManager?.addFilterMsg(msg);
  },
};

export default Log;
