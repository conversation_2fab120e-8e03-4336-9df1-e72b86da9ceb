const processApi = (api) => (options = {}) => {
  return new Promise((resolve, reject) => {
    const obj = { ...options };

    ['fail', 'success', 'complete'].forEach((k) => {
      obj[k] = (res) => {
        options[k] && options[k](res);
        if (k === 'success') {
          resolve(res);
        } else if (k === 'fail') {
          reject(res);
        }
      };
    });
    api(obj);
  });
};

const _ = {
  getSetting: processApi(my.getSetting),
  getPhoneNumber: processApi(my.getPhoneNumber),
  getOpenUserInfo: processApi(my.getOpenUserInfo),
  getAuthCode: processApi(my.getAuthCode),
  getUserInfo: async (...args: any) => {
    const result: any = await _.getOpenUserInfo(...args);
    const { code, msg, ...userInfo } = JSON.parse(result.response).response;
    return { userInfo, encryptedData: JSON.stringify(userInfo) };
  },
  login: async (options: any) => {
    const result: any = await _.getAuthCode({
      ...options,
      scopes: 'auth_base', // 主动授权（弹框）：auth_user，静默授权（不弹框）：auth_base
    });
    const { errorMsg, authCode } = result;

    return {
      errMsg: errorMsg || 'login:ok',
      code: authCode,
    };
  },
  // openDocument: processApi(my.openDocument),
};

export default _;
