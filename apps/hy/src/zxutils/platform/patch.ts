import Taro from '@tarojs/taro';
import { getStorageSync } from '@/utils/storage';
import { routerParamsStringify, routerParamsStringParse } from '@/utils';

import TaroPlatform from '.';
import Debug from '../debug';

const debug = Debug('patch');

Taro.login = TaroPlatform.login;
Taro.getSetting = TaroPlatform.getSetting;
Taro.getUserInfo = TaroPlatform.getUserInfo;

const key = 'userInfo';
const useOriginShareAppMessage = Taro.useShareAppMessage;

Taro.useShareAppMessage = (fn) => {
  return useOriginShareAppMessage((...args) => {
    const result = fn(...args);
    const { path: rawPath, ...rest } = result;

    if (rawPath) {
      //  append shareFromUserId (from localstore)
      const [path, paramstring] = rawPath.split('?');
      const params = routerParamsStringParse(paramstring);
      const userInfo = getStorageSync(key) || {};
      debug.info('share userInfo', userInfo);
      const shareUserInfo = userInfo.openId ? { shareFromUserId: userInfo.openId } : {};
      const search = `?${routerParamsStringify({
        ...params,
        ...shareUserInfo,
      })}`;
      const nextResult = { ...rest, path: `${path}${search}` };
      debug.info('share params', params);
      debug.info('share result', nextResult);
      return nextResult;
    }
    debug.info('share result', result);
    return result;
  });
};
