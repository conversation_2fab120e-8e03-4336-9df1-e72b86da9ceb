import { useCallback, useEffect, useReducer, useRef } from 'react';
import Taro, { usePageScroll, useDidShow } from '@tarojs/taro';

export const useScrollTo = (dep) => {
  const offsetTop = useRef(0);
  const pageScrollTop = useRef(0);
  usePageScroll((res) => {
    pageScrollTop.current = res.scrollTop;
  });
  return useCallback((id, offset = 0) => {
    if (!offsetTop?.current) {
      const query = Taro.createSelectorQuery();
      query.select(`#${id}`).boundingClientRect();
      // query.selectViewport().scrollOffset();
      query.exec((res) => {
        const top = res[0]?.top;
        offsetTop.current = top;
        Taro.pageScrollTo({
          scrollTop: pageScrollTop.current + top - offset,
        });
      });
    } else {
      Taro.pageScrollTo({
        scrollTop: pageScrollTop.current + offsetTop.current - offset,
      });
    }
  }, dep);
};

// export const pageScrollTo = (id, offset = 0) => {
//   const query = Taro.createSelectorQuery();
//   query.select(`#${id}`).boundingClientRect();
//   // query.selectViewport().scrollOffset();
//   query.exec((res) => {
//     const top = res[0]?.top;

//     Taro.pageScrollTo({
//       scrollTop: top - offset,
//     });
//   });
// };

const fillZero = (value) => {
  return value < 10 ? `0${value}` : value;
};

export const useTimer = (time: number) => {
  const lefttime = (time - Date.now()) / 1000;
  const day = lefttime / (24 * 60 * 60);
  const hours = (lefttime / (60 * 60)) % 24;
  const mintues = (lefttime / 60) % 60;
  const seconds = lefttime % 60;
  const [, forceUpdate] = useReducer((count) => count + 1, 0);

  useEffect(() => {
    const timer = setInterval(() => {
      if ((time - Date.now()) / 1000 > 0) {
        forceUpdate();
      }
    }, 1000);
    return () => {
      clearInterval(timer);
    };
  }, [time]);

  if (lefttime < 0) {
    return {
      day: 0,
      hours: 0,
      mintues: 0,
      seconds: 0,
    };
  }

  return {
    day: fillZero(Math.floor(day)),
    hours: fillZero(Math.floor(hours)),
    mintues: fillZero(Math.floor(mintues)),
    seconds: fillZero(Math.floor(seconds)),
  };
};

export const useViewed = (fn: (...args: any[]) => any) => {
  const count = useRef(0);
  useDidShow(() => {
    count.current += 1;
    if (count.current > 1) {
      fn && fn();
    }
  });
};
