import {
  bindUserInfo as fetchBindUserInfo,
} from '@/api';

const bindUserInfo = async (query: Record<string, string>) => {
  const kMap: string[][] = [
    ['inferFromUserId'],
    ['inferFromUserPhone'],
    ['inferFromUserCertId'],
    ['inferFromUserInfo', 'encUserInfo'],
    // ['shareFromUserId'],
    // ['shareFromUserPhone'],
    // ['shareFromUserCertId'],
  ];
  const data = kMap.reduce((next, array) => {
    const [key, alias] = array;
    if (query[key]) {
      next[alias || key] = query[key];
    }
    return next;
  }, {});

  if (!Object.keys(data).length) {
    return;
  }

  await fetchBindUserInfo(data);
};

export default bindUserInfo;
