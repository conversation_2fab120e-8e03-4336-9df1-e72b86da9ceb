import { updateAgreement } from '@/api/agreement';
import { AGREEMENT_MAP } from '@/constants/agreement';
import { AgreementKeyEnum, IUserAgreementListItem } from '@/types/agreement';
import { isSurrportPrivacyAuthorization } from '@/utils/privacy-authorization';

/**
 * 获取协议
 * @param isMember 是否显示会员服务协议;
 */
export const getAgreementList = (isMember = true) => {
  const isSupport = isSurrportPrivacyAuthorization();
  return Object.entries(AGREEMENT_MAP).filter(([key]) => {
    return ((key !== AgreementKeyEnum.SERVICE_AGREEMENT && (key !== AgreementKeyEnum.HEALTH_INFO_PROTECTION || isSupport)) || (key === AgreementKeyEnum.SERVICE_AGREEMENT && isMember));
  });
};

/**
 * 登录时更新条款
 * @param isMember 是否显示会员服务协议;
*/
export const updatedTerms = (isMember = true) => {
  const isSupport = isSurrportPrivacyAuthorization();
  const list = getAgreementList(isMember).filter(([key]) => (key !== AgreementKeyEnum.HEALTH_INFO_PROTECTION || isSupport)).map(([key, item]) => {
    return {
      version: item.version || '',
      fileCode: key,
    } as IUserAgreementListItem;
  });
  updateAgreement({ userAgreementList: list });
};
