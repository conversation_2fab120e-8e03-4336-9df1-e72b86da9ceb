.subscribe-message-container {
  min-height: 100vh;
  padding-top: 214rpx;
  background: url("https://cdn-qcloud.zhongan.com/a00000/za-asclepius/applets/subscribe_message_no_title.jpg") top center no-repeat;
  background-size: cover;

  .subscribe-message {
    margin: 0 30rpx 0;
    padding: 0 30rpx;
    background-color: white;
    box-shadow: 0 18rpx 32rpx 0 rgba(0, 0, 0, 0.03);
    border-radius: 16rpx;

    .message-item {
      display: flex;
      align-items: center;
      padding: 50rpx 0;

      .text {
        flex: 1 1 auto;

        .title {
          display: flex;
          align-items: center;
          font-size: 32rpx;
          font-weight: 600;
          color: #1E1E1E;
          line-height: 45rpx;
        }

        .tip {
          margin-top: 10rpx;
          font-size: 26rpx;
          font-weight: 400;
          color: #999;
          line-height: 37rpx;
        }

        .subscribed-checked {
          display: inline-block;
          height: 36rpx;
          line-height: 36rpx;
          margin-left: 10rpx;
          padding: 0 10rpx;
          font-size: 22rpx;
          font-weight: 400;
          color: #00A864;
          background: #E5F6EF;
          border-radius: 4rpx;

          .subscribed-icon {
            width: 20rpx;
            height: 16rpx;
            margin-right: 6rpx;
          }
        }
      }

      .btn {
        width: 156rpx;
        height: 56rpx;
        line-height: 56rpx;
        font-size: 26rpx;
        font-weight: 600;
        text-align: center;
        border-radius: 28rpx;
      }

      .unsubscribed {
        color: white;
        background-color: #00BC70;
      }

      .subscribed {
        color: #999;
        font-weight: 400;
        border: 1rpx solid #ABABAB;
      }

      &:not(:last-of-type) {
        border-bottom: 1rpx solid #E6E6E6;
      }
    }
  }
}
