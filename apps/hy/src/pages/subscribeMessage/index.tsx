import React, { useState, useEffect } from 'react';
import { View, Text, Image } from '@tarojs/components';
import Taro from '@tarojs/taro';
import './index.scss';

// 导入消息模板配置
import { templateIdTips, templateIds } from '@/utils/messageTemplate';
import subscribeActiveIcon from '@/images/personalcenter/subscribe-active.svg';

interface Template {
  id: string;
  title: string;
  tip: string;
  subscribed?: 'accept' | 'reject' | 'ban';
}

interface TemplateSubName {
  accept: string;
  reject: string;
  ban: string;
}

const SubscribeMessagePage: React.FC = () => {
  const [templates, setTemplates] = useState<Template[]>([]);

  // 订阅状态对应的按钮文本
  const templateSubName: TemplateSubName = {
    accept: '已订阅',
    reject: '订阅消息',
    ban: '已禁用',
  };

  // 页面显示时获取订阅状态
  useEffect(() => {
    getSubscribe();
  }, []);

  // 获取订阅状态
  const getSubscribe = () => {
    Taro.getSetting({
      withSubscriptions: true,
      success: (res) => {
        console.log('订阅设置:', res.subscriptionsSetting);
        const { subscriptionsSetting } = res;
        const { mainSwitch, itemSettings } = subscriptionsSetting || {};

        const templateList: Template[] = [];

        // 处理已有订阅设置的模板
        if (subscriptionsSetting && mainSwitch && itemSettings) {
          for (let id in itemSettings) {
            if (Object.prototype.hasOwnProperty.call(templateIdTips, id)) {
              templateList.push({
                ...templateIdTips[id],
                id,
                subscribed: itemSettings[id]
              });
            }
          }
        }

        // 处理未设置的模板
        const existingSettings = itemSettings || {};
        templateIds.forEach((template) => {
          const { id } = template;
          if (!Object.prototype.hasOwnProperty.call(existingSettings, id)) {
            templateList.push(template);
          }
        });

        // 排序：已订阅的排在后面，未订阅的排在前面
        const sortedTemplates = templateList.reduce<Template[]>((previousValue, currentValue) => {
          if (currentValue.subscribed === 'accept') {
            previousValue.push(currentValue);
          } else {
            previousValue.unshift(currentValue);
          }
          return previousValue;
        }, []);

        setTemplates(sortedTemplates);
      },
      fail: (error) => {
        console.error('获取订阅设置失败:', error);
      }
    });
  };

  // 订阅消息
  const handleSubscribe = (template: Template) => {
    const { id, subscribed } = template;
    const disabledSubscribed = ['accept', 'ban']; // 已订阅和已禁用状态不允许再次订阅

    if (!disabledSubscribed.includes(subscribed || '')) {
      Taro.requestSubscribeMessage({
        tmplIds: [id],
        success: (res: any) => {
          console.log('订阅结果:', res);
          // 重新获取订阅状态
          getSubscribe();
        },
        fail: (error: any) => {
          console.error('订阅失败:', error);
        }
      } as any);
    }
  };

  return (
    <View className="subscribe-message-container">
      <View className="subscribe-message">
        {templates.map((template) => (
          <View key={template.id} className="message-item">
            <View className="text">
              <View className="title">
                <Text>{template.title}</Text>
                {template.subscribed === 'accept' && (
                  <View className="subscribed-checked">
                    <Image
                      className="subscribed-icon"
                      src={subscribeActiveIcon}
                    />
                    <Text>订阅中</Text>
                  </View>
                )}
              </View>
              <View className="tip">{template.tip}</View>
            </View>
            <View
              className={`btn ${template.subscribed === 'accept' ? 'subscribed' : 'unsubscribed'}`}
              onClick={() => handleSubscribe(template)}
            >
              {templateSubName[template.subscribed || 'reject'] || '订阅消息'}
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

export default SubscribeMessagePage;
