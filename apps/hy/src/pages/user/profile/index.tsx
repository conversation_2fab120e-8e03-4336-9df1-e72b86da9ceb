import { View, Image, Button, Input } from '@tarojs/components';
import Taro, { useLoad } from '@tarojs/taro';
import React, { useState } from 'react';
import storage from '@/utils/storage';
import { uploadImage } from '@/utils/tools';
import format from '@/utils/format';
import { fetchJson, getAccountInfo } from '@/utils/fetch';
import { useThrottleFn } from 'ahooks';

import defaultAvatar from '@/images/icon_default_avatar.png';
import './index.scss';

const NICKNAME_MIN_SIZE = 2;
const NICKNAME_MAX_SIZE = 20;

const Profile = () => {
  const [avatar, setAvatar] = useState(defaultAvatar);
  const [nickName, setNickName] = useState('');
  const [attachmentList, setAttachmentList] = useState<any[]>([]);

  useLoad(() => {
    const { userHeadPortrait = '', userNickname = '' } = storage.get('userInfo') || {};
    setAvatar(userHeadPortrait || defaultAvatar);
    setNickName(userNickname);
  });

  const onChooseAvatar = (e: any) => {
    const { avatarUrl } = e.detail;
    if (avatarUrl) {
      // @ts-ignore
      uploadImage({
        filePath: avatarUrl,
      })
        .then((res: any) => {
          if (res) {
            setAttachmentList([res]);
            setAvatar(format.imageUrl(res.attachmentDownloadUrl));
          }
        })
        .catch(err => {
          Taro.showToast({ title: '头像上传失败', icon: 'none' });
          console.error(err);
        });
    }
  };

  const handleNameInput = (e: any) => {
    setNickName(e.detail.value);
  };

  const { run: saveName } = useThrottleFn(() => {
    // nickName中汉字占用2个字符
    const nicknameLength = nickName.replace(/[^\x00-\xff]/gi, '**').length;
    if (!nickName) {
      Taro.showToast({
        title: '请输入昵称',
        icon: 'none',
      });
      return;
    };
    if (nicknameLength < NICKNAME_MIN_SIZE) {
      Taro.showToast({
        title: `昵称不能小于 ${NICKNAME_MIN_SIZE} 个字符`,
        icon: 'none',
      });
      return;
    }
    if (nicknameLength > NICKNAME_MAX_SIZE) {
      Taro.showToast({
        title: `昵称不能超过 ${NICKNAME_MAX_SIZE} 个字符，当前 ${nicknameLength}`,
        icon: 'none',
      });
      return;
    }

    fetchJson({
      url: '/zaApi/v1/patient/user/modifyNickNameAndHeadPortrait',
      method: 'POST',
      data: {
        userNickname: nickName,
        attachmentList: attachmentList,
        userHeadPortrait: avatar,
      },
      needLoading: false,
    }).then(() => {
      getAccountInfo().then(() => {
        Taro.showToast({
          title: '修改成功',
          icon: 'none',
        });
        setTimeout(() => {
          Taro.navigateBack({ delta: 1 });
        }, 1000)
      })
    }).catch(err => {
      console.error(err);
      Taro.showToast({ title: '保存失败', icon: 'none' });
    })
  }, { wait: 600, leading: true, trailing: false });

  return (
    <View className="profile">
      <View className="profile-list">
        <View className="profile-item">
          <View>头像</View>

          <View className="profile-avatar">
            <Button className="profile-avatar__choose" openType="chooseAvatar" onChooseAvatar={onChooseAvatar}></Button>
            <Image className="profile-avatar__image" src={avatar} onError={() => setAvatar(defaultAvatar)}></Image>
          </View>
        </View>
        <View className="profile-item">
          <View>昵称</View>
          <View className="profile-nickname">
            <Input type="nickname" maxlength={NICKNAME_MAX_SIZE} className="profile-nickname__input" value={nickName} placeholder="请输入昵称" onInput={handleNameInput}/>
          </View>
        </View>
        <View className="profile-item profile-addition__tip">
          昵称仅限{NICKNAME_MIN_SIZE}-{NICKNAME_MAX_SIZE}个字符，一个汉字为2个字符
        </View>
      </View>
      <Button className="profile-btn" type="primary" onClick={saveName}>确认</Button>
    </View>
  );
};

export default Profile;
