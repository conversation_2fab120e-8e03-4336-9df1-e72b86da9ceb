.profile {
  min-height: 100vh;
  background: #f5f5f5;

  &-list {
    background: #fff;
  }

  &-item {
    margin: 0 20PX;
    padding: 12PX 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1PX solid #f5f5f5;
  }

  &-avatar {
    position: relative;
    width: 50PX;
    height: 50PX;
    border-radius: 50%;
    overflow: hidden;

    &__image {
      width: 100%;
      height: 100%;
    }

    &__choose {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
      z-index: 1;
    }
  }

  &-nickname {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 15PX;
    flex: 1;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      right: 0;
      margin-left: 10PX;
      width: 6PX;
      height: 6PX;
      border-left: 1PX solid #aaaaaa;
      border-top: 1PX solid #aaaaaa;
      transform: rotate(135deg) translate(0, 3PX);
      // transform-origin: 0 50%;
    }

    &__input {
      text-align: right;
      flex: 1;
    }
  }

  &-input {
    width: 100%;
    padding: 20PX;
    min-height: 80PX;
    background-color: #fff;
    box-sizing: border-box;
  }

  &-btn {
    display: block;
    margin: 30PX 20PX;
    border-radius: 50PX;
    background-color: #00bc70!important;
  }

  &-addition__tip {
    padding-top: 8PX;
    padding-bottom: 8PX;
    font-size: 12PX;
    color: #999;
  }
}
