.auth_modal {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 9999;
  transform: translateY(100%);

  &.show {
    transform: translateY(0);
  }

  &__content {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 750rpx;
    height: 840rpx;
    padding-top: 66rpx;
    box-sizing: border-box;
    border-radius: 35rpx 35rpx 0 0;
    background: #fff;
    text-align: center;
    color: #333;
    font-size: 32rpx;
    transform: translateY(100%);
    transition: all 0.3s;

    &.show {
      transform: translateY(0);
    }
  }

  &__img {
    width: 724rpx;
    height: 436rpx;
    margin: 0 0 30rpx 24rpx;
  }

  &__close {
    position: absolute;
    right: 30rpx;
    top: 30rpx;
    width: 40rpx;
    height: 40rpx;
    padding: 20rpx;
  }

  &__btn {
    width: 600rpx;
    height: 88rpx;
    line-height: 88rpx;
    margin: 20rpx auto 0;
    border-radius: 8rpx;
    background: #00cb93;
    color: #fff;
    font-size: 32rpx;

    &::after {
      display: none;
    }
  }

  .active_auth_button {
    background: #00aa66;
    color: #d4d4d4;
  }

  .checkbox_wrap {
    margin-top: 10rpx;
    padding: 10rpx 40rpx 0;
    font-size: 28rpx;
    text-align: left;

    .checkbox {
      transform: scale(0.9) translateY(-5rpx);
    }
  }

  .flex {
    display: flex;
    align-self: center;
  }

  .check-box {
    width: 32rpx;
    margin-right: 20rpx;
  }

  .auth_modal__agreement {
    // padding-top: 20rpx;
    font-size: 26rpx;

    .text {
      display: inline;
      color: rgba(51, 51, 51, 0.6);
    }

    .link {
      display: inline;
      color: #00bc7d;
    }
  }

  .hide {
    display: none;
  }

  .showBtn {
    display: block;
  }

  checkbox .wx-checkbox-input {
    border-radius: 50%;
  }

  checkbox .wx-checkbox-input.wx-checkbox-input-checked {
    border: none;
    background: url('https://cdn-qcloud.zhongan.com/a00000/za-asclepius/applets/guide/tick.png') no-repeat center center #00bc70;
    background-size: 100% 100%;
  }

  checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
    width: 40rpx;
    height: 40rpx;
    line-height: 30rpx;
    text-align: center;
    font-size: 0;
    color: #00bc70;
    background: transparent;
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
  }
}
