import React, { useState, useEffect, useRef, useCallback } from 'react';
import { WebView } from '@tarojs/components';
import Taro, {
  getCurrentInstance,
  setNavigationBarTitle,
  useShareAppMessage,
  getSystemInfoSync,
  hideHomeButton,
} from '@tarojs/taro';

// 引入所有必需的工具函数、组件和常量
import { Serialize, Deserialize, appendUrl } from '@/utils/serialization';
import { login, fetchJson } from '@/utils/fetch';
import getSearch from '@/utils/getSearch';
import storage from '@/utils/storage';
import { JSONPARSE, getWebviewUrl, filterURLParams } from '@/utils/tools';
import { getShareInfo } from '@/utils/shareApi';
import getChannelInfo from '@/utils/getChannelInfo';
import AuthPage from '@/components/authPage';
import { ZA_MALL_H5_URL } from '@/utils/staticData';
import { sendCustomEvent } from '@/utils/xflowTrack';

import './index.scss';

// H5 域名分享白名单
const shareAuthList: readonly string[] = ['asleephealth.com'];

const WebviewPage = () => {
  const [webviewSrc, setWebviewSrc] = useState('');
  const [shouldAuth, setShouldAuth] = useState(false);
  const [shareInfo, setShareInfo] = useState<any>(null);

  const cacheSrc = useRef('');
  const originUrl = useRef('');
  const instance = getCurrentInstance();

  /**
   * 为缓存的 cacheSrc 拼接授权 token 并设置给 webview
   */
  const generateAuthUrl = useCallback((token: string) => {
    if (!cacheSrc.current) return;
    const joiner = cacheSrc.current.includes('?') ? '&' : '?';
    // H5 Mall 页面需要 openId 和 appCode
    const finalSrc = `${cacheSrc.current}${joiner}${token.split('?')[1]}&appCode=hy&openId=${storage.get('openId')}`;
    console.log('generateAuthUrl-finalSrc', finalSrc);
    setWebviewSrc(finalSrc);
  }, []);

  /**
   * AuthPage 授权成功后的回调
   */
  const handleAuthSuccess = useCallback((token: string) => {
    if (token) {
      setShouldAuth(false);
      generateAuthUrl(token);
    }
  }, [generateAuthUrl]);

  /**
   * 判断链接是否属于免授权白名单 (noLoginInHy=Y)
   */
  const isNoNeedAuth = (url: string) => {
    const originPartnerUrl = decodeURIComponent(url);
    const params: { noLoginInHy?: string } = Deserialize(originPartnerUrl) || {};
    const hasNoLoginParams = params.noLoginInHy === 'Y';
    return originPartnerUrl.startsWith(ZA_MALL_H5_URL) && hasNoLoginParams;
  };

  /**
   * 检查用户是否需要对当前 H5 页面进行授权
   */
  const checkIsAuth = useCallback(async (url: string) => {
    const doAuthCheck = async () => {
      try {
        const res = await fetchJson({
          url: '/zaApi/v1/patient/partner/user/auth/isUserAuth',
          data: { thirdPlatformCode: 'HY_MALL', businessType: 'index', target: '' },
          method: 'POST',
          needLogin: !isNoNeedAuth(url),
          needToast: !isNoNeedAuth(url),
        });

        if (res.code === '0') {
          const { isUserAuth, token: newToken } = res.result;
          cacheSrc.current = decodeURIComponent(url);
          if (isUserAuth === 'N') {
            setShouldAuth(true);
            return;
          }
          generateAuthUrl(newToken);
        }
      } catch (err) {
        cacheSrc.current = decodeURIComponent(url);
        generateAuthUrl('');
      }
    };

    const token = storage.get('Token');
    if (!token && !isNoNeedAuth(url)) {
      try {
        const accountPhone = await login();
        if (!accountPhone) {
          Taro.redirectTo({
            url: `/pages/login?returnUrl=${encodeURIComponent(`/pages/webview/index?target=${encodeURIComponent(url)}`)}`,
          });
          return;
        }
        await doAuthCheck();
      } catch (error) {
        console.error('Silent login failed', error);
        Taro.redirectTo({
          url: `/pages/login?returnUrl=${encodeURIComponent(`/pages/webview/index?target=${encodeURIComponent(url)}`)}`,
        });
      }
      return;
    }

    await doAuthCheck();
  }, [generateAuthUrl]);

  useEffect(() => {
    // FinClip 环境兼容
    if ((getSystemInfoSync() as any).inFinChat) {
      hideHomeButton();
    }
    const { router } = instance;
    if (!router || !router.params) {
      console.error('无法获取页面参数');
      return;
    }

    const { src = '', target = '', param = '', title = '' } = router.params;
    const decodedLink = decodeURIComponent(src || target);
    originUrl.current = src || target; // 缓存最原始的链接，用于特殊分享

    if (title) {
      setNavigationBarTitle({ title: decodeURIComponent(title) });
    }

    // 初始化分享信息
    if (decodedLink) {
      // @ts-ignore
      getShareInfo(decodedLink, {}).then(res => setShareInfo(res));
    }

    // 如果是商城链接，走授权流程
    if (decodedLink.startsWith(ZA_MALL_H5_URL)) {
      checkIsAuth(src || target);
      return;
    }

    // --- 非商城链接，走原有的拼接逻辑 ---
    getSearch().then(res => {
      // 解析嵌套的 target 参数
      const getDeliveryTarget = (p = '') => {
        if (p.includes('target')) {
          const { target: nestedTarget }: { target?: string } = JSONPARSE(p.replace(/\(/g, "{").replace(/\)/g, "}").replace(/\'/g, '"'), 'webview');
          return { target: nestedTarget };
        }
        return {};
      };
      const processParam = decodeURIComponent(param).replace(/\(/g, "{").replace(/\)/g, "}").replace(/\'/g, '"');
      const tryData = JSONPARSE(processParam, 'webview');
      const nextSearch = Serialize({ ...tryData, ...res, ...getDeliveryTarget(param) });

      let transformParam: any = {};
      if (decodedLink.startsWith(ZA_MALL_H5_URL)) {
        transformParam = { openId: res.openId, appCode: 'hy' };
      }
      const linkWithExtraParams = appendUrl(decodedLink, Serialize(transformParam));
      const finalSrc = getWebviewUrl(encodeURIComponent(linkWithExtraParams), nextSearch);

      console.log('最终加载的 WebView SRC:', finalSrc);
      setWebviewSrc(finalSrc);
    });

  }, [instance.router?.params, checkIsAuth]);

  /**
   * 处理来自 H5 的 postMessage
   */
  const handleMessage = useCallback((e) => {
    try {
      const { data = [] } = e.detail;
      const [item = '{}'] = data;
      const msg = JSON.parse(item);
      if (msg.data && msg.data.deliveryAddressId) {
        storage.set('deliveryAddressId', msg.data.deliveryAddressId);
      }
      //这里针对公众号授权成功回调做个缓存
      if (data && data.length && data[0]['bindPublic']) {
        storage.set('bindPublic', true);
      }
    } catch (error) {
      sendCustomEvent('webviewHandleMessageError', { ...e.detail }, error);
      console.error('handleMessage error', error);
    }
  }, []);

  /**
   * 处理 H5 加载失败
   */
  const handleError = useCallback((e) => {
    sendCustomEvent('webviewBindError', e.detail, {});
    console.error('webview handleError', e.detail);
  }, []);

  /**
   * 自定义分享
   */
  useShareAppMessage(res => {
    // TODO: 页面分享埋点逻辑待实现
    // 原 WePY 代码中, 此处会设置全局分享埋点:
    // app.za.para.autoTrack.pageShare = {
    //   shareTitle: "webview页",
    //   shareDesc: "webview分享",
    // };

    const { webViewUrl = '' } = res;
    // 定义需要从URL中过滤掉的参数列表
    const needFilterParams: string[] = ['token', 'isReal', 'maskActId', 'maskusId', 'userId', 'openId', 'userBlackFlag'];
    // 过滤URL中的敏感参数
    const url = filterURLParams(webViewUrl, needFilterParams);

    let finalPath = `/pages/index?channelResourceCode=${getChannelInfo.channelResourceCode()}&webviewUrl=${encodeURIComponent(url)}`;

    // 如果 webViewUrl 包含在 shareAuthList 中的任何项，使用原始 URL 作为目标
    if(shareAuthList.some(item => webViewUrl.includes(item))) {
      finalPath = `/pages/webview/index?channelResourceCode=${getChannelInfo.channelResourceCode()}&target=${originUrl.current}`;
    }

    return {
      title: shareInfo?.title || '众安健康',
      path: finalPath,
      imageUrl: shareInfo?.imageUrl,
    };
  });

  // 根据是否需要授权，条件渲染 AuthPage 或 WebView
  if (shouldAuth) {
    // @ts-ignore
    return <AuthPage thirdPlatformCode="HY_MALL" onSuccess={handleAuthSuccess} />;
  }

  return (
    webviewSrc ? <WebView src={webviewSrc} onMessage={handleMessage} onError={handleError} /> : null
  );
};

export default WebviewPage;
