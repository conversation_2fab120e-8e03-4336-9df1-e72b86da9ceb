import React, { useState, useEffect, useRef } from 'react';
import { View, Swiper, SwiperItem, Image } from '@tarojs/components';
import Taro, { useDidShow, getCurrentInstance, navigateTo, showToast, showLoading, hideLoading, requestSubscribeMessage, useShareAppMessage, getSetting } from '@tarojs/taro';
import { useSelector, useDispatch } from 'react-redux';
import { CDN_PREFIX, REAL_STATUS } from '@/utils/staticData';
import storage, { everyDayOnceExpiredStorage } from '@/utils/storage';
import { globalData } from '@/utils/global';

// 引入 Actions
import { fetchResourceList, fetchDepartmentList } from '@/store/actions/resource';

// 引入工具函数
import { fetchJson, login } from '@/utils/fetch';
import { xflowPushEvent } from '@/utils/pageTrack';
import getChannelInfo from '@/utils/getChannelInfo';
import { getShareInfo } from '@/utils/shareApi';
import { groupBy } from '@/utils/groupBy';
import format from '@/utils/format';
import { onAdClick } from '@/utils/ad';
import { JSONPARSE } from '@/utils/tools';
import { DOCTOR_MSG_TEMPLATE_ID } from '@/utils/messageTemplate';


// Import child components as per TODO.md
import GoldSteel from '@/components/goldSteel';
import CheckInquiry from '@/components/checkInquiry';
import GoldCore from '@/components/goldCore';
import AuthModal from '@/components/authModal';
import GJModal from '@/components/gjModal';
import CarInsureModal from '@/components/carInsureModal';
import FamilyContinuedFee from '@/components/continuedFee';
import Modal from '@/components/modal';

import './index.scss';

// 弹窗相关的常量
const EVERY_DAY_GUIDANCE_ONCE = 'everyDayGuidanceOnce';
const EVERY_DAY_ONCE_POPUP = 'everyDayOncePopup';
const EVERY_DAY_ONCE_POPUP_WITHLOGIN = 'everyDayOncePopupWithLogin';

const Index = () => {
  const dispatch = useDispatch();
  const instance = getCurrentInstance();

  // Timers for popup logic
  const popupShowTimer = useRef<NodeJS.Timeout | null>(null);
  const popupHideTimer = useRef<NodeJS.Timeout | null>(null);


  // --- State Management ---
  const [bannerList, setBannerList] = useState<any[]>([]);
  const [footerBannerList, setFooterBannerList] = useState<any[]>([]);
  const [popups, setPopups] = useState<any[]>([]);
  const [popupQueue, setPopupQueue] = useState<any[]>([]); // New state for the queue
  const [bannerCurrent, setBannerCurrent] = useState(0);
  const [articleTab, setArticleTab] = useState(0);
  const [weekArticleList, setWeekArticleList] = useState<any[]>([]);
  const [officialArticleList, setOfficialArticleList] = useState<any[]>([]);
  const [weekArticleButtonShow, setWeekArticleButtonShow] = useState(true);
  const [officialArticleButtonShow, setOfficialArticleButtonShow] = useState(true);
  const [weekBatchNo, setWeekBatchNo] = useState(1);
  const [officalBatchNo, setOfficalBatchNo] = useState(1);
  const [homePopup, setHomePopup] = useState<{ visible: boolean; url?: string, [key: string]: any }>({ visible: false });
  const [officialAccountShow, setOfficialAccountShow] = useState(false);
  const [otherModalCanShow, setOtherModalCanShow] = useState(false);
  const [thirdChannelBizOpt, setThirdChannelBizOpt] = useState<any>({});
  const [hasJumped, setHasJumped] = useState(false);
  const [isHomePopupsWithLogin, setIsHomePopupsWithLogin] = useState(false);
  const [shareInfo, setShareInfo] = useState<any>({});
  const [hasInquiry, setHasInquiry] = useState(false);
  const [needSubscribed, setNeedSubscribed] = useState(false);


  // Modal visibility states
  const [isShowAuthModal, setIsShowAuthModal] = useState(false);
  const [isShowGJModal, setIsShowGJModal] = useState(false);
  const [gjData, setGjData] = useState({ isInsured: false });
  const [carInsureModalShow, setCarInsureModalShow] = useState(false);
  const [userRealAuthInfo, setUserRealAuthInfo] = useState({});
  const [showNoDoctor, setShowNoDoctor] = useState(false);

  // Redux State
  const { tryOutOrContinuedFeePoup, ZA_TOKEN, SYSTEMMODE } = useSelector((state: any) => ({
    tryOutOrContinuedFeePoup: state.familyDoctors?.tryOutOrContinuedFeePoup || false,
    ZA_TOKEN: state.user?.token,
    SYSTEMMODE: state.resource?.SYSTEMMODE,
  }));

  // --- Popup Logic Functions ---

  const alertPushEvent = (popupData, eventTag: 'open' | 'button' | 'close') => {
    try {
      const xflow = popupData?.adRule?.xflow;
      if (xflow && xflow[eventTag]) {
        xflowPushEvent({
          eventTag: { open: 'ZAHLWYY_SYTCCX', button: 'ZAHLWYY_SYTCDJ', close: 'ZAHLWYY_SYTCGB' }[eventTag],
          attributes: xflow[eventTag],
        });
      }
    } catch (e) {
      console.error('alertPushEvent failed', e);
    }
  };

  const clearPopup = () => {
    if (homePopup.visible) {
      alertPushEvent(homePopup, 'close');
    }
    clearTimeout(popupShowTimer.current!);
    clearTimeout(popupHideTimer.current!);
    setHomePopup({ visible: false });
  };

  const hidePopup = () => {
    alertPushEvent(homePopup, 'close');
    clearTimeout(popupHideTimer.current!);
    setHomePopup({ visible: false });
    // This state change will trigger the useEffect to process the next popup
  };

  const showPopup = (item: any) => {
    alertPushEvent(item, 'open');
    setHomePopup({ visible: true, ...item });

    const disappearType = item.adRule?.disappear_arg?.type;
    const disappearValue = item.adRule?.disappear_arg?.value;

    if (disappearType === 'delayAuto' && disappearValue > 0) {
      popupHideTimer.current = setTimeout(hidePopup, disappearValue * 1000);
    }
  };

  const processNextPopup = (queue: any[], isFirstPopup: boolean) => {
    if (queue.length === 0) return;

    const [itemToShow, ...remainingQueue] = queue;
    setPopupQueue(remainingQueue);

    const appearValue = itemToShow.adRule?.appear_arg?.value || 0;
    if (isFirstPopup && appearValue > 0) {
      popupShowTimer.current = setTimeout(() => showPopup(itemToShow), appearValue * 1000);
    } else {
      showPopup(itemToShow);
    }
  };

  const handleHomePops = (allPopups: any[], loggedIn: boolean) => {
    const everyDayOnce = storage.get(EVERY_DAY_ONCE_POPUP);
    const everyDayOnceWithLogin = storage.get(EVERY_DAY_ONCE_POPUP_WITHLOGIN);

    if ((everyDayOnce && everyDayOnceWithLogin) || !allPopups || allPopups.length === 0) {
      return;
    }
    everyDayOnceExpiredStorage(EVERY_DAY_ONCE_POPUP);

    if (!everyDayOnce && loggedIn) {
      everyDayOnceExpiredStorage(EVERY_DAY_ONCE_POPUP_WITHLOGIN);
      setPopupQueue(allPopups);
    } else {
      const willPops = allPopups.filter(item => {
        const appearType = item.adRule?.appear_arg?.type || '';
        return loggedIn ? appearType === 'isLogin' : appearType !== 'isLogin';
      });

      if (everyDayOnce && !loggedIn) {
        return;
      }
      if (willPops && willPops.length) {
        setPopupQueue(willPops);
        if (loggedIn) {
          everyDayOnceExpiredStorage(EVERY_DAY_ONCE_POPUP_WITHLOGIN);
        }
      }
    }
  };


  // --- Helper Functions ---

  const getRealAuth = async () => {
    try {
      const res = await fetchJson({
        url: '/zaApi/v1/patient/patient/list',
        method: 'POST',
        data: { patientRelation: 1 },
        needLoading: false,
      });
      if (res.code === '0' && res.result?.length > 0) {
        storage.set('realName', REAL_STATUS.auth);
      }
    } catch (error) {
      console.error('[getRealAuth] Failed:', error);
    }
  };

  const saveChannelBizPushData = async (opts: any) => {
    if (!opts.thirdUserId) {
      return;
    }
    try {
      await fetchJson({
        url: '/zaApi/v1/patient/user/saveChannelBizPushData',
        method: 'POST',
        data: {
          ...opts,
          channelOpenId: storage.get('openId'),
          channelSource: getChannelInfo.channelSource(),
          channelResourceCode: getChannelInfo.channelResourceCode(),
        },
        needLoading: false,
        needLogin: false,
      });
    } catch (error) {
      console.error('[saveChannelBizPushData] Failed:', error);
    }
  };

  const checkSubscribeMsgSetting = async () => {
    if (Taro.getSetting) {
        try {
            const res = await Taro.getSetting({ withSubscriptions: true });
            const { subscriptionsSetting } = res;
            const itemSettings = subscriptionsSetting?.itemSettings;
            let needed = true;

            if (subscriptionsSetting?.mainSwitch && itemSettings) {
                if (itemSettings[DOCTOR_MSG_TEMPLATE_ID] === 'accept') {
                    needed = false;
                }
            }
            setNeedSubscribed(needed);
        } catch (error) {
            console.error('checkSubscribeMsgSetting failed:', error);
            setNeedSubscribed(true); // Fallback to needing subscription
        }
    }
  };

  const checkCarInsureModalStatus = async (opts: any = {}, mobile = '', _dummy?: any) => {
    const channelResourceCode = getChannelInfo.channelResourceCode();
    if (!opts.thirdUserId || channelResourceCode !== 'CARINSURE') {
      setOtherModalCanShow(true);
      return;
    }
    try {
      const carInsureInfo = await fetchJson({
        url: '/zaApi/v1/patient/user/getUserJumpUrl',
        method: 'POST',
        data: {
          ...opts,
          isRealName: 'Y',
          channelResourceCode,
        },
        needLoading: false,
        needLogin: false,
      });

      if (!carInsureInfo || carInsureInfo.result) {
        setOtherModalCanShow(true);
      } else {
        const userInfo = storage.get('userRealInfo') || {};
        const { accountPhone = '' } = storage.get('userInfo') || {};
        setUserRealAuthInfo({
          name: userInfo.patientName,
          certiNo: userInfo.patientCertNo,
          accountPhone: mobile || accountPhone,
        });
        setCarInsureModalShow(true);
      }
    } catch (error) {
      console.error('checkCarInsureModalStatus failed', error);
      setOtherModalCanShow(true); // Fallback to allow other modals to show
    }
  };

  const sendSubscribeMessage = async (id: string) => {
    if (requestSubscribeMessage && id) {
      try {
        // @ts-ignore
        await requestSubscribeMessage({ tmplIds: [id] });
      } catch (err) {
        console.error('订阅消息失败', err);
      }
    }
  };

  const checkAuth = async (authType: 'phone' | 'real', nextPath: string, messageId = '') => {
    const accountPhone = storage.get('userInfo')?.accountPhone || '';
    const realNameStatus = storage.get('realName') || REAL_STATUS.noAuth;

    if (!accountPhone && (authType === 'phone' || authType === 'real')) {
      setIsShowAuthModal(true);
      return;
    }

    if (authType === 'real' && realNameStatus === REAL_STATUS.noAuth) {
      await sendSubscribeMessage(messageId);
      navigateTo({
        url: `/pages/authnew/index?nextPath=${encodeURIComponent(nextPath)}`
      });
      return;
    }

    await sendSubscribeMessage(messageId);
    navigateTo({ url: nextPath });
  };


  // --- Data Fetching and Business Logic ---

  const getArticle = async (tab = articleTab) => {
    const batchNo = tab === 0 ? weekBatchNo : officalBatchNo;
    try {
      const res = await fetchJson({
        url: '/zaApi/v1/patient/article/list/batch',
        method: 'POST',
        needLogin: false,
        needLoading: false,
        data: {
          articleType: tab === 0 ? 'week' : 'official',
          batchNoList: [batchNo],
          status: 1,
          needAttachment: true,
        },
      });

      const { code = '0', result = [] } = res;
      if (code === '0') {
        const hasContain = result[0] || [];
        if (!hasContain.length) {
          if (batchNo !== 1) {
            showToast({ icon: 'none', title: '没有更多内容' });
          }
          if (tab === 0) setWeekArticleButtonShow(false);
          else setOfficialArticleButtonShow(false);
          return;
        }

        const formattedArticles = result.map(batch =>
          batch.map(article => ({
            ...article,
            attachmentDownloadUrl: format.imageUrl(article.attachmentList?.[0]?.attachmentDownloadUrl, true),
          }))
        );

        if (tab === 0) {
          setWeekArticleList(prev => batchNo === 1 ? formattedArticles : [...prev, ...formattedArticles]);
          setWeekBatchNo(prev => prev + 1);
        } else {
          setOfficialArticleList(prev => batchNo === 1 ? formattedArticles : [...prev, ...formattedArticles]);
          setOfficalBatchNo(prev => prev + 1);
        }
      }
    } catch (e) {
      console.error("getArticle failed", e);
    }
  };

  const getBannerList = async () => {
    if (isHomePopupsWithLogin) return;
    const token = storage.get('Token') || '';
    try {
      const res = await fetchJson({
        url: '/zaApi/v1/patient/ad/list',
        method: 'POST',
        needLogin: false,
        needLoading: false,
        data: {
          option: { needAdPicture: true, needFilterExcludeResource: true },
          channelSource: getChannelInfo.channelSource(),
          channelResourceCode: getChannelInfo.channelResourceCode(),
        },
      });

      if (res.code === '0') {
        const { result = [] } = res;
        if (result.length === 0) return;

        const ad = groupBy(result.map((k) => ({
          ...k,
          url: format.imageUrl(k.attachmentDomain?.attachmentDownloadUrl, true),
          type: k.adType,
          link: k.adRequestUrl,
          appId: k.targetAppId,
          extraData: k.extraData,
          adName: k.adName || '',
          adRule: JSONPARSE(k.adRule, 'index_banner'), // 使用正确的 JSONPARSE
        })), ({ adPosition }) => adPosition);

        setBannerList(ad['banner']?.slice(0, 5) || []);
        const homePopups = ad['home_popup'] || [];
        setPopups(homePopups); // keep original popups for reference
        setFooterBannerList(ad['bottom_suspension'] || []);

        const loggedIn = !!token;
        setIsHomePopupsWithLogin(loggedIn);
        // Trigger the popup logic instead of just setting state
        handleHomePops(homePopups, loggedIn);
      }
    } catch (e) {
      console.error("getBannerList failed", e);
    }
  };

  const alertGuidenceEveryDay = () => {
    const everyDayOnce = storage.get(EVERY_DAY_GUIDANCE_ONCE);
    if (!everyDayOnce) {
      setOfficialAccountShow(true);
    }
  };

  const initShare = async () => {
    try {
      // @ts-ignore
      const res = await getShareInfo('/pages/index');
      setShareInfo(res);
    } catch (error) {
      console.log('init share error', error);
    }
  };

  // --- Lifecycle Hooks ---

  useEffect(() => {
    showLoading({ title: '加载中', mask: true });
    const params = instance.router?.params || {};

    dispatch(fetchResourceList());
    dispatch(fetchDepartmentList());

    getArticle(0);
    alertGuidenceEveryDay();
    initShare();
    setThirdChannelBizOpt({ thirdUserId: params.userId, sign: params.sign });

    if (params.webviewUrl && !hasJumped) {
      const webviewUrl = decodeURIComponent(params.webviewUrl);
      setHasJumped(true);
      navigateTo({ url: `/pages/webview?target=${encodeURIComponent(webviewUrl)}` });
      return;
    }

    hideLoading();
  }, []); // 依赖数组为空，确保只在首次渲染时执行

  // Effect to drive the popup queue
  useEffect(() => {
    if (popupQueue.length > 0 && !homePopup.visible) {
      // isFirst - only the very first popup in a session gets the entry delay
      const isFirstPopup = popups.length === popupQueue.length;
      processNextPopup(popupQueue, isFirstPopup);
    }
  }, [popupQueue, homePopup.visible]);

  // Effect for cleanup on unmount (like onHide)
  useEffect(() => {
    return () => {
      clearPopup();
    };
  }, []);


  useDidShow(async () => {
    xflowPushEvent({
      eventTag: 'ZAHLWYY_SY',
      attributes: '首页_首页',
    });

    if (getChannelInfo.channelResourceCode() !== 'CARINSURE') {
      setOtherModalCanShow(true);
    }

    const loadDataAfterLogin = () => {
        getBannerList();
        getRealAuth();
        checkCarInsureModalStatus(thirdChannelBizOpt);
    }

    const token = storage.get('Token') || '';
    if (token) {
        loadDataAfterLogin();
    } else {
        try {
            const accountPhone = await login();
            if (!accountPhone) {
              storage.set('realName', REAL_STATUS.noAuth);
            } else {
              loadDataAfterLogin();
            }

        } catch(e) {
            // loadDataAfterLogin();
        }
    }
    saveChannelBizPushData(thirdChannelBizOpt);
    checkSubscribeMsgSetting();
  });

  useShareAppMessage(() => {
    return shareInfo;
  });


  // --- Event Handlers ---
  const bannerChange = (e) => setBannerCurrent(e.detail.current);

  const handleTopBannerClick = (item) => {
    const { link = '' } = item;
    if (!link) return;
    const src = encodeURIComponent(link);
    navigateTo({ url: `/pages/webview/index?src=${src}&title=众安互联网医院` });
  };

  const handleBannerClick = (item) => {
    const { link, type, appId = '', adName = '', extraData = {}, adPosition, adNo } = item;
    onAdClick({
      adRequestUrl: link,
      adType: type,
      appId,
      adName,
      extraData,
      adPosition,
      adNo
    });
  };

  const tapArticleTab = (tabIndex) => {
    setArticleTab(tabIndex);
    if (tabIndex === 1 && officialArticleList.length === 0) {
      getArticle(1);
    }
  };

  const toReadArticle = (article) => {
    const { urlLink = '' } = article;
    const src = encodeURIComponent(urlLink);
    navigateTo({ url: `/pages/webview/index?src=${src}&title=众安互联网医院` });
  };

  const fetchMoreArticle = () => getArticle();

  const closeGuidanceModal = () => {
    everyDayOnceExpiredStorage(EVERY_DAY_GUIDANCE_ONCE);
    setOfficialAccountShow(false);
  };

  const toOfficalAccount = () => {
      xflowPushEvent({
        eventTag: 'ZAHLWYY_SY',
        attributes: '去引导添加公众号文章',
      });
      const src = encodeURIComponent('https://mp.weixin.qq.com/s/rx7G2GZq7__udi4ZyjSOZw');
      navigateTo({ url: `/pages/webview/index?src=${src}&title=众安互联网医院` });
  }

  const footerImageBannerClick = () => {
    if (footerBannerList.length > 0) {
        handleBannerClick(footerBannerList[0]);
    }
  }

  const closePopup = () => {
    setHomePopup({ visible: false });
  };

  const popupBtnTap = () => {
    const { link, type, appId = '', extraData = {},adName, adPosition, adNo } = homePopup;
    alertPushEvent(homePopup, 'button');
    onAdClick({
      adRequestUrl: link,
      adType: type,
      appId,
      extraData,
      adName,
      adPosition,
      adNo,
    });
    // The original logic for type 1 ads (display only) was to close the popup on click.
    if (type == 1) {
      hidePopup();
    }
  };

  const handleAuthSuccess = (accountPhone?: string) => {
      // TODO: 授权弹窗的 onAuthSuccess 回调机制已改变, 不再直接传递 accountPhone.
      // 后续需要改造 AuthModal, 使其在成功后通过其他方式 (如 event bus 或 redux) 通知父组件.
      // 暂时先无参数调用, 保证流程继续.
      getBannerList();
      checkCarInsureModalStatus(thirdChannelBizOpt, accountPhone, {});
  }

  const handleToTextInquiry = () => {
    xflowPushEvent({
      eventTag: 'ZAHLWYY_SY',
      attributes: '首页_在线咨询_互联网门诊',
    });

    const inquiryPath = SYSTEMMODE?.[0]?.resCode === 'supervision'
      ? '/hospital/choosedepartment'
      : '/hospital/inquiryform';

    let param = '';
    if (needSubscribed) {
        param = `&param=${encodeURIComponent(JSON.stringify({ needSubscribeMsg: '1' }))}`;
    }

    const src = encodeURIComponent(`${globalData.webviewUrl}${inquiryPath}`);
    const nextPath = `/pages/webview/index?src=${src}${param}`;

    checkAuth('phone', nextPath, 'MKqlB4hLfWhm03A1j2FKUW5yxCz_8qFxYgbFGqtsi8U');
  };

  const handleToButler = () => {
    xflowPushEvent({
      eventTag: 'ZAHLWYY_SY',
      attributes: '首页_问一下',
    });

    let param = '';
    if (needSubscribed) {
        param = `&param=${encodeURIComponent(JSON.stringify({ needSubscribeMsg: '1' }))}`;
    }

    const src = encodeURIComponent(`${globalData.webviewUrl}/hospital/chatmedicalmanage`);
    const nextPath = `/pages/webview/index?src=${src}${param || ''}`;

    checkAuth('phone', nextPath, '');
  };

  const handleShowNoDoctorModal = () => {
    setShowNoDoctor(true);
  };

  const handleHasInquiryChange = (status: boolean) => {
    setHasInquiry(status);
  };


  // --- Render Method ---
  return (
    <View className="index_page">
      <Swiper className="banner_cover" autoplay circular onChange={bannerChange}>
        {bannerList.map((item, index) => (
          <SwiperItem key={index} className="slide_item" onClick={() => handleTopBannerClick(item)}>
            <Image src={item.url} className="slide_image" />
          </SwiperItem>
        ))}
      </Swiper>

      <View className="banner_slide flex-y">
        {bannerList.map((_, index) => (
          <View key={index} className={bannerCurrent === index ? 'active' : ''}></View>
        ))}
      </View>

      <View className="mainbox">
        <GoldSteel />
        <CheckInquiry/>
        <GoldCore/>
      </View>

      <View className="news">
        <View className="news_tab">
          <View className={`news_title ${articleTab === 0 ? 'active' : ''}`} onClick={() => tapArticleTab(0)}>
            壹周医选
          </View>
          <View className={`news_title ${articleTab === 1 ? 'active' : ''}`} onClick={() => tapArticleTab(1)}>
            院务公开
          </View>
          <View className="slide" style={{ left: `${articleTab * 50}%` }}></View>
        </View>
        <View>
          {articleTab === 0 ? (
            <View className="new_subtitle">每周分享热门健康资讯</View>
          ) : (
            <View className="new_subtitle">众安互联网医院院务信息</View>
          )}
        </View>

        {(articleTab === 0 && weekArticleList.length > 0) || (articleTab === 1 && officialArticleList.length > 0) ? (
          (articleTab === 0 ? weekArticleList : officialArticleList).map((batch, batchIndex) => (
            <View className="news_batch" key={batchIndex}>
              {batch.map((article, articleIndex) => (
                <View className="news_item flex flex-justify" key={articleIndex} onClick={() => toReadArticle(article)}>
                  <View className="text flex flex-justify">
                    <View className="title">{article.articleTitle}</View>
                    <View className="desc">@{article.author}</View>
                  </View>
                  <Image src={article.attachmentDownloadUrl} />
                </View>
              ))}
            </View>
          ))
        ) : (
          <View className="news_batch_cover">暂无信息</View>
        )}
        {((articleTab === 0 && weekArticleButtonShow) || (articleTab === 1 && officialArticleButtonShow)) && (
          <View className="news_button" onClick={fetchMoreArticle}>查看更多</View>
        )}
      </View>

      <View className="slogan">
        <Image src={`${CDN_PREFIX}common/footer_slogan.png`} />
      </View>

      {otherModalCanShow && (
        <>
          {footerBannerList.length > 1 ? (
            <View className="footer-swiper">
              <Swiper className="swiper" autoplay interval={3000} circular indicatorDots indicatorColor="rgba(0, 0, 0, .2)" indicatorActiveColor="rgba(0, 188, 112, .6)">
                {footerBannerList.map((item, index) => (
                  <SwiperItem key={index} className="slide_item" onClick={() => handleTopBannerClick(item)}>
                    <Image src={item.url} className="swiper_image" />
                  </SwiperItem>
                ))}
              </Swiper>
            </View>
          ) : footerBannerList.length === 1 ? (
            <View className="footer-swiper">
              <Image src={footerBannerList[0].url} className="swiper_image" onClick={footerImageBannerClick} />
            </View>
          ) : (
            officialAccountShow && (
              <View className="gudiance_modal" style={{ backgroundImage: `url(${CDN_PREFIX}/guidance_bar.png)` }}>
                <View className="close" onClick={closeGuidanceModal}></View>
                <View className="subscribe" onClick={toOfficalAccount}></View>
              </View>
            )
          )}
          <View className="gudiance_modal_cover"></View>
          {(!storage.get('Token') || !tryOutOrContinuedFeePoup) && homePopup.visible && (
            <View className="home-popup-container flex-xy">
              <View className="content">
                <View className="close" onClick={closePopup}>
                  <Image src={require('@/images/index/home-alert-close.png')} />
                </View>
                <View className="bottom-btn" onClick={popupBtnTap}></View>
                <View className="popup-bg">
                  <Image className="bg" src={homePopup.url || ''} />
                </View>
              </View>
            </View>
          )}
        </>
      )}

      <View onClick={(e) => e.stopPropagation()}>
        <AuthModal show={isShowAuthModal} onClose={() => setIsShowAuthModal(false)} onAuthSuccess={handleAuthSuccess} />
        <GJModal show={isShowGJModal} onClose={() => setIsShowGJModal(false)} gjData={gjData} />
        <CarInsureModal show={carInsureModalShow} onClose={() => setCarInsureModalShow(false)} onAuthSuccess={handleAuthSuccess} userInfo={userRealAuthInfo} />
        <FamilyContinuedFee/>
        <Modal show={showNoDoctor} onClose={() => setShowNoDoctor(false)}>
          <View className="comp_modal__content">
            <Image className="comp_modal__img" src={require('@/images/icon_pay_fail.svg')} />
            <View className="comp_modal__text">
              <View className="comp_modal__text_strong">接通失败</View>
              <View>当前无空闲医生，请稍后再试</View>
            </View>
            <View className="comp_modal__footer" onClick={() => setShowNoDoctor(false)}>我知道了</View>
          </View>
        </Modal>
      </View>
    </View>
  );
};

export default Index;
