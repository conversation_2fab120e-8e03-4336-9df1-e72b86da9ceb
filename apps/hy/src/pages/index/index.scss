.index_page {
  position: relative;
  min-height: 100vh;
  background: #f5f5f5;
  overflow: hidden;
  font-family: PingFangSC-Regular, PingFang SC;

  .word-spot {
    position: absolute;
    top: 24rpx;
    right: 106rpx;
    width: 56rpx;
    height: 30rpx;
    line-height: 30rpx;
    background: #ff5050;
    border-radius: 15rpx 15rpx 15rpx 0;
    border: 1px solid #fafafa;
    font-size: 18rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #fff;
  }

  .banner_cover {
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    width: 100%;
    height: 600rpx;
    overflow: hidden;
    background-color: inherit;
    border-top-left-radius: 100% 0%;
    border-top-right-radius: 100% 0%;
    border-bottom-left-radius: 100% 50px;
    border-bottom-right-radius: 100% 50px;

    // &::after {
    //   position: absolute;
    //   left: 0;
    //   right: 0;
    //   top: 0;
    //   content: '';
    //   height: 105%;
    //   width: 100%;

    //   z-index: -1;
    // }
    .slide_image {
      width: 100%;
      height: 600rpx;
    }
  }

  .banner_slide {
    position: absolute;
    z-index: 2;
    height: 30rpx;
    left: 48rpx;
    top: 360rpx;

    view {
      margin-left: 6rpx;
      width: 10rpx;
      height: 6rpx;
      border-radius: 30%;
      background: rgba(255, 255, 255, 0.4);
      transition: width, background-color 0.3s ease;

      &.active {
        width: 20rpx;
        background: #fff;
        border-radius: 30%;
      }
    }
  }

  .mainbox {
    position: relative;
    z-index: 21;
    margin-top: 400rpx;
    padding: 0 30rpx 30rpx;

    // background: linear-gradient(180deg, #fff 0%, #f7f7f7 256rpx, #fff 257rpx, #fff 100%) no-repeat;
    // box-shadow: 0 18rpx 32rpx 0 rgba(0, 0, 0, 0.03);
    overflow: hidden;
    &::after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: 210rpx;
      // background-color: #ccc;
      bottom: 0;
      background: linear-gradient(180deg, #f5f5f5 0, #fff 100%) no-repeat;
      z-index: -1;
    }
  }

  .news {
    overflow: hidden;
    background: #fff;

    .news_tab {
      position: relative;
      display: inline-block;
      .news_title {
        display: inline-block;
        padding: 0 30rpx 15rpx;
        font-size: 34rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 48rpx;

        &.active {
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #333333;
        }
      }

      .slide {
        position: absolute;
        bottom: 2rpx;
        width: 50%;
        transition: left 0.3s linear;

        &::after {
          content: '';
          display: block;
          width: 60rpx;
          margin: 0 auto;
          height: 5rpx;
          background: #00a864;
          border-radius: 2rpx;
        }
      }
    }

    .new_subtitle {
      margin: 20rpx 24rpx 24rpx;
      display: inline-block;
      background: #e5f6ef;
      border-radius: 0rpx 12rpx 12rpx 12rpx;
      height: 56rpx;
      padding: 0 24rpx;
      line-height: 56rpx;
      font-size: 26rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #00a864;
    }

    .news_batch {
      width: 93.6%;
      margin: 0 auto;
    }

    .news_batch + .news_batch {
      padding-top: 30rpx;
      border-top: 1px solid #e6e6e6;
      margin: 30rpx auto 20rpx;
      width: 93.6%;
    }

    .news_batch_cover {
      margin: 0 auto 20rpx;
      width: 93.6%;
      height: 208rpx;
      background: #fafafa;
      line-height: 208rpx;
      text-align: center;
      font-weight: 400;
      color: #666666;
      font-size: 30rpx;
    }

    .news_item {
      position: relative;
      margin: 0 auto 20rpx;
      width: 100%;
      min-height: 208rpx;
      background: #fafafa;
      border-radius: 16rpx;
      padding: 24rpx;
      overflow: hidden;

      .text {
        flex-direction: column;
        padding: 6rpx 0;
        max-width: 400rpx;
      }

      .title {
        font-size: 28rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333;
        line-height: 40rpx;
      }

      .desc {
        font-size: 24rpx;
        color: #999;
        line-height: 34rpx;
      }

      image {
        display: block;
        width: 200rpx;
        height: 160rpx;
        border-radius: 16rpx;
      }
    }

    .news_button {
      display: block;
      width: 152rpx;
      margin: 24rpx auto;
      color: #999;
      font-size: 24rpx;
      height: 48rpx;
      line-height: 48rpx;
      text-align: center;
      border-radius: 24rpx;
      border: 1px solid #d9d9d9;
    }
  }

  .slogan {
    padding: 60rpx 0 50rpx;

    image {
      display: block;
      width: 434rpx;
      height: 72rpx;
      margin: 0 auto;
    }
  }

  .ygj_guide_modal,
  .new_user_DRAW_MODAL {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;

    .ygj_guide_modal_content {
      position: relative;
      width: 650rpx;
      height: 850rpx;
      background: url('https://cdn-qcloud.zhongan.com/a00000/za-asclepius/applets/ygj_guide_modal_new_1.png');
      background-size: 100% 100%;

      .ygj_guide_modal_close {
        position: absolute;
        width: 80rpx;
        height: 80rpx;
        right: 20rpx;
        top: 30rpx;
      }

      .ygj_guide_modal_btn {
        position: absolute;
        width: 60%;
        height: 120rpx;
        left: 20%;
        bottom: 110rpx;
      }
    }

    .new_user_DRAW_MODAL_content {
      position: relative;
      width: 650rpx;
      height: 840rpx;
      background: url('https://cdn-qcloud.zhongan.com/a00000/za-asclepius/images/modal_draw_new.png');
      background-size: 100% 100%;

      .new_user_DRAW_MODAL_close {
        position: absolute;
        width: 60rpx;
        height: 60rpx;
        right: 20rpx;
        top: 40rpx;
      }

      .new_user_DRAW_MODAL_btn {
        position: absolute;
        width: 60%;
        height: 120rpx;
        left: 20%;
        bottom: 110rpx;
      }
    }
  }

  .gudiance_modal {
    position: fixed;
    bottom: 21rpx;
    left: 21rpx;
    width: 712rpx;
    height: 100rpx;
    background-size: 100%;
    background-repeat: no-repeat;
    z-index: 22;

    .close {
      position: absolute;
      width: 40rpx;
      height: 40rpx;
      left: 25rpx;
      top: 50%;
      transform: translateY(-50%);
    }

    .subscribe {
      position: absolute;
      width: 140rpx;
      height: 50rpx;
      right: 25rpx;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .gudiance_modal_cover {
    width: 712rpx;
    height: 100rpx;
    margin: 21rpx auto;
  }

  .active-opacity {
    opacity: 0.5;
  }

  .channel-free-tag-chat {
    position: absolute;
    top: -19rpx;
    right: 0rpx;
    height: 38rpx;
    line-height: 38rpx;
    padding: 0 14rpx;
    background: #ff5050;
    border-radius: 19rpx 19rpx 19rpx 0rpx;
    border: 1px solid #f6e9dc;
    font-size: 20rpx;
    font-weight: 400;

    .del {
      text-decoration: line-through;
      white-space: nowrap;
    }

    .up0 {
      font-size: 22rpx;
    }

    .up1 {
      display: flex;
      align-items: center;
      > image {
        display: inline-block;
        width: 79rpx;
        height: 21rpx;
        margin-left: 8rpx;
        vertical-align: middle;
      }
    }
  }

  @keyframes LOADING {
    0% {
      transform: scale(0.5);
      background: #fff;
    }

    50% {
      transform: scale(1);
      background: #ffefd1;
    }

    100% {
      transform: scale(0.5);
      background: #fff;
    }
  }

  // @keyframes AVATAR {
  //   0% {
  //     opacity: 0.4;
  //     transform: translateX(0);
  //   }

  //   20% {
  //     transform: translateX(0);
  //     opacity: 1;
  //     z-index: 1;
  //   }

  //   40% {
  //     transform: translateX(-45rpx);
  //     opacity: 1;
  //     z-index: 2;
  //   }

  //   60% {
  //     transform: translateX(-94rpx) scale(1.1);
  //     opacity: 1;
  //     z-index: 3;
  //   }

  //   80% {
  //     transform: scale(1);
  //     transform: translateX(-116rpx);
  //     opacity: 0;
  //   }

  //   99% {
  //     transform: translateX(116rpx);
  //     opacity: 0;
  //   }

  //   100% {
  //     opacity: 0;
  //     transform: translateX(0);
  //   }
  // }

  @keyframes ICONSHAKE {
    0% {
      transform: rotate(0deg);
    }

    5% {
      transform: rotate(40deg);
    }

    10% {
      transform: rotate(-30deg);
    }

    15% {
      transform: rotate(25deg);
    }

    20% {
      transform: rotate(-16deg);
    }

    25% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(0deg);
    }
  }

  @keyframes DISAPPEAR {
    0% {
      opacity: 1;
    }

    5% {
      opacity: 0;
    }

    100% {
      opacity: 0;
    }
  }

  @keyframes APPEAR_ONE {
    0% {
      opacity: 0;
      transform: rotate(-110deg);
      transform-origin: 50%;
    }

    20% {
      opacity: 1;
    }

    100% {
      opacity: 1;
      transform: rotate(0deg);
      transform-origin: 50%;
    }
  }

  @keyframes APPEAR_TWO {
    0% {
      opacity: 0;
      transform: rotate(110deg);
      transform-origin: 50%;
    }

    20% {
      opacity: 1;
    }

    100% {
      opacity: 1;
      transform: rotate(0deg);
      transform-origin: 50%;
    }
  }
}

.home-popup-container {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;

  .content {
    position: relative;
    width: 600rpx;
    height: 760rpx;
    max-width: 95%;

    .popup-bg {
      position: absolute;
      left: 0;
      top: 0;
      z-index: 1000;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;

      .bg {
        width: 100%;
        height: 100%;
      }
    }

    .close {
      position: absolute;
      width: 60rpx;
      height: 60rpx;
      right: 56rpx;
      top: 76rpx;
      z-index: 1001;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .bottom-btn {
      position: absolute;
      left: 50%;
      bottom: 0;
      z-index: 1002;
      width: 95%;
      height: 70%;
      transform: translateX(-50%);
    }
  }
}

// 页脚轮播
.footer-swiper {
  height: 150rpx;
  position: fixed;
  bottom: 21rpx;
  left: 21rpx;
  right: 21rpx;
  z-index: 23;
  .swiper,
  .swiper_image {
    width: 100%;
    height: 150rpx;
    border-radius: 20rpx;
  }
}
