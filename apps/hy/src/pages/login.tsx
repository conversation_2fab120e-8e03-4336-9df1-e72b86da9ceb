export * from './login/index';
export { default } from './login/index';


// import React, { useEffect } from 'react';
// import Taro, { getCurrentInstance } from '@tarojs/taro';
// import { View } from '@tarojs/components';
// import LoadingComponent from '@/components/Loading';

// /**
//  * 将对象转换为 URL 查询字符串
//  * @param obj - 需要转换的参数对象
//  * @returns URL 查询字符串 (e.g., "?key1=value1&key2=value2")
//  */
// const objToUrl = (obj: Record<string, any>): string => {
//   const queryString = Object.keys(obj)
//     .filter(key => obj[key] !== undefined && obj[key] !== null)
//     .map(key => `${key}=${encodeURIComponent(obj[key])}`)
//     .join('&');
//   return queryString ? `?${queryString}` : '';
// };

// /**
//  * H5 跳转登录页的路由占位页
//  * 该页面对用户不可见，加载后会立刻重定向到真实的登录页
//  */
// const LoginRedirectPage = () => {
//   useEffect(() => {
//     const router = getCurrentInstance().router;
//     const params = router?.params || {};

//     // 定义真实的登录页路径
//     const targetPath = '/pages/login/index';

//     // 拼接最终的 URL，并附带上 H5 传来的所有原始参数
//     const finalUrl = `${targetPath}${objToUrl(params)}`;

//     // 使用 redirectTo 进行重定向。
//     // 这会替换掉导航历史中的当前页面，确保用户返回时不会回到这个空白的占位页。
//     setTimeout(() => {
//       Taro.redirectTo({
//         url: finalUrl,
//         fail: (err) => {
//           console.error('重定向到新登录页失败:', err);
//           // 如果重定向失败，可以提供一个降级方案，例如返回上一页
//           Taro.navigateBack();
//         }
//       });
//     }, 300);
//   }, []); // 空依赖数组确保该 effect 仅在组件挂载时执行一次

//   // 此页面无需渲染任何实际内容
//   return <LoadingComponent />;
// };

// export default LoginRedirectPage;
