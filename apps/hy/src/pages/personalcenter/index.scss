@keyframes DOT {
  0% {
    opacity: 100%;
  }

  50% {
    opacity: 60%;
  }

  100% {
    opacity: 100%;
  }
}
.personalcenter-page {
  min-height: 100vh;
  padding-top: 183rpx;
  padding-bottom: 20rpx;
  // background: linear-gradient(180deg, #ffffff 0%, #ffffff 15%, #f5f5f5 35%, #f5f5f5 100%);
  font-family: PingFangSC-Regular, PingFang SC;
  // background: #f5f5f5 url(./images/icon/header-bg.png) no-repeat;
  background-repeat: no-repeat;
  background-color: #f5f5f5;
  background-size: 100% auto;
  background-position: 0 -4rpx;

  &__hd {
    padding: 0 30rpx;

    .user-info {
      padding: 0 10rpx;
      font-size: 24rpx;
      color: #fff;
      line-height: 40rpx;

      .user-icon {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        border: 1px solid rgba(255, 255, 255, 0.6);
        overflow: hidden;
      }

      .user-core {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-left: 20rpx;

        .user-name {
          font-size: 36rpx;
          font-weight: 600;
          margin-bottom: 6rpx;
        }

        .vip-mark {
          display: inline-block;
          border-radius: 2px;
          padding: 0 4rpx;
          color: #5f3312;
          position: relative;
          height: 32rpx;
          line-height: 32rpx;

          &::after {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            background: linear-gradient(90deg, #faeccf 0%, #f7d59a 100%);
            transform: skew(-0.03turn);
            z-index: 0;
            border-radius: 2rpx;
          }

          .text {
            position: relative;
            z-index: 2;
            transform: scale(0.9);
            font-size: 12PX;
            display: block;
          }
        }
      }
    }

    .core-column {
      background: linear-gradient(360deg, #f8efdd 0%, #f9d0ab 100%);
      border-radius: 27rpx 27rpx 0 0;
      margin-top: 33rpx;

      .column-item {
        // @include display-flex;
        flex: 1;

        font-size: 24rpx;
        padding: 28rpx 0 28rpx 56rpx;
        color: #5f3312;

        .icon {
          width: 78rpx;
          height: 78rpx;
          margin-right: 13rpx;
        }

        .strong {
          font-weight: bold;
          color: #5f3312;
          font-size: 44rpx;
          margin-right: 10rpx;
          display: inline-block;
          line-height: 40rpx;
        }
        .not-rights {
          display: inline-block;
          position: relative;
          color: #5f3312;
          &:after {
            content: '';
            position: absolute;
            border-width: 1rpx 1rpx 0 0;
            border-style: solid;
            top: 50%;
            transform: translate(0, -50%) rotate(45deg);
            border-color: #caa98b;
            width: 14rpx;
            height: 14rpx;
            right: -20rpx;
          }
        }

        .belonging {
          font-weight: 600;
          color: #5f3312;
          font-size: 26rpx;
          margin-top: 4rpx;
        }

        &:not(:first-child) {
          position: relative;

          &::after {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            width: 2rpx;
            height: 70rpx;
            background: rgba(95, 51, 18, 0.1);
            transform: translate(0, -50%);
          }
        }
      }
    }
  }

  &__main {
    background: linear-gradient(180deg, #ffffff 90%, #f5f5f5 104%);
    border-radius: 16px 16px 0 0;
    padding: 27rpx 30rpx 30rpx;
    &-carousel {
      background-color: #ebfaf4;
      // background-image: url(./images/icon/icon-heart.png);
      background-repeat: no-repeat;
      background-position: -2% 102%;
      background-size: 194rpx auto;
      border-radius: 16rpx;
      border: 1px solid rgba(0, 168, 100, 0.15);
      overflow: hidden;
      height: 170rpx;
      margin-bottom: 20rpx;

      .single-col {
        padding: 50rpx 28rpx 50rpx 40rpx;
      }
      .look-btn {
        width: 180rpx;
        line-height: 70rpx;
        background: linear-gradient(270deg, #87deb4 0%, #07c465 100%);
        border-radius: 35rpx;
        text-align: center;
        font-weight: 600;
        color: #fff;
        font-size: 26rpx;

        .plus {
          width: 30rpx;
          height: 26rpx;
          margin: 0 3rpx 0 6rpx;
          transform: translate(0px, 2px);
        }
      }
      .not-patient {
        .plus {
          width: 22rpx;
          height: 20rpx;
          transform: translate(0px, 0px);
        }
        .patient-info {
          font-size: 28rpx;
          font-weight: 600;
          color: #999;
          line-height: 44rpx;
        }
        .protrude {
          color: #00a864;
        }
        .look-btn {
          position: relative;
          &::after {
            content: '';
            left: 93%;
            top: 3px;
            position: absolute;
            width: 13rpx;
            height: 13rpx;
            background: #ff5050;
            border: 1rpx solid #fffced;
            border-radius: 50%;
          }
        }
      }
      .carousel__item {
        .patient-info {
          color: #9b9b9b;
          font-weight: 500;
          font-size: 32rpx;
          .strong {
            font-size: 36rpx;
            padding-right: 30rpx;
            color: #333;
          }
        }
      }
    }
    .carousel-pagination {
      // padding: 20rpx 0 30rpx;
      text-align: center;
      font-size: 0;
      margin-bottom: 20rpx;
      .dot {
        display: inline-block;
        width: 10rpx;
        height: 6rpx;
        background: #f4f4f4;
        border-radius: 8rpx;
        margin-left: 6rpx;
        transition: all 0.3s;
        &.active {
          width: 20rpx;
          background: #e5e5e5;
        }
      }
    }
    &-title {
      position: relative;
      margin-bottom: 20rpx;
      .icon {
        width: 125rpx;
        height: 30rpx;
      }
    }
    .link-family-info{
      position: absolute;
      top:0;
      right: 8rpx;
      font-size: 13PX;
      color: #00A864;
      font-weight: bold;

    }
    &-minor {
      // @include display-flex;
      // @include justify-content(space-between);
      // @include align-items(center);
      .minor-part {
        flex: 1;
        height: 140rpx;
        line-height: 140rpx;
        background: rgba(0, 0, 0, 0.03);
        border-radius: 16rpx;
        background-repeat: no-repeat;
        background-position: 100% 100%;
        background-size: 168rpx auto;
        color: #333333;
        font-weight: 600;
        font-size: 30rpx;
        padding: 0 20rpx;
        position: relative;
        & + .minor-part {
          margin-left: 20rpx;
        }
        &.red::after {
          content: '';
          position: absolute;
          left: 142rpx;
          top: 48rpx;
          width: 20rpx;
          height: 20rpx;
          background: rgba(255, 35, 35, 0.9);
          border-radius: 50%;
          animation-name: DOT;
          animation-iteration-count: infinite;
          animation-duration: 2s;
        }
      }
    }
  }

  &__piece {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin: 20rpx 30rpx 0;
    &-title {
      line-height: 46rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: #1e1e1e;
      margin-bottom: 37rpx;
      position: relative;
      .look-all {
        position: absolute;
        right: 3PX;
        top: 0;
        font-size: 26rpx;
        font-weight: normal;
        color: #aaaaaa;
        &::after {
          content: '';
          position: relative;
          display: inline-block;
          width: 6PX;
          height: 6PX;
          border-left: 1px solid #aaaaaa;
          border-top: 1px solid #aaaaaa;
          transform: rotate(135deg) translate(0, 3PX);
        }
      }
    }
    &-orders {
      .order-category {
        flex: 1;
        text-align: center;
        font-size: 26rpx;
        color: #232323;
        &:not(:first-child) {
          margin-left: 10rpx;
        }
        .category-icon {
          width: 55rpx;
          height: 55rpx;
          margin: 0 auto 10rpx;
          position: relative;
          .img {
            width: 55rpx;
            height: 55rpx;
          }
        }
        .order-num {
          position: absolute;
          right: -10rpx;
          top: -12rpx;
          width: 30rpx;
          height: 30rpx;
          line-height: 30rpx;
          text-align: center;
          background-color: #fff;
          border: 1px solid #00a864;
          border-radius: 50%;
          color: #00a864;
          font-size: 24rpx;
          text {
            display: block;
            transform: scale(0.7) translate(0, -2px);
          }
        }
      }
    }
    &-tool {
      // @include display-flex;
      flex-flow: row wrap;
      // @include align-content(start);
      background: #fff;
      border-radius: 16rpx;
      .tool-item {
        flex: 0 0 25%;
        text-align: center;
        padding: 0 0 24rpx;
        line-height: 40rpx;
        font-size: 28rpx;
        color: #333;
        overflow: hidden;
        .img {
          width: 40rpx;
          height: 40rpx;
          margin-bottom: 20rpx;
        }
      }
    }
  }
}

// 页脚轮播
.footer-swiper {
  height: 150rpx;
  position: fixed;
  bottom: 21rpx;
  left: 21rpx;
  right: 21rpx;
  z-index: 23;

  &__placeholder {
    height: 150rpx;
  }

  .swiper,
  .swiper_image {
    width: 100%;
    height: 150rpx;
    border-radius: 20rpx;
  }

  .carousel-pagination {
    text-align: center;
    font-size: 0;
    margin-top: -15rpx;
    .dot {
      display: inline-block;
      width: 10rpx;
      height: 6rpx;
      background: #f4f4f4;
      border-radius: 8rpx;
      margin-left: 6rpx;
      transition: all 0.3s;
      &.active {
        width: 20rpx;
        background: #e5e5e5;
      }
    }
  }
}
