import { <PERSON>, Image, Button, Swiper, SwiperItem, Text, Block, Navigator } from '@tarojs/components';
import { useState, useEffect, useCallback, useRef } from 'react';
import Taro, { useDidShow, useShareAppMessage } from '@tarojs/taro';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import AuthModal from '@/components/authModal';
import { CDN_PREFIX, REAL_STATUS } from '@/utils/staticData';
import defaultAvatar from '@/images/icon_default_avatar.png';
import { fetchResourceList } from '@/store/actions/resource';
import storage from '@/utils/storage';
import { login, fetchJson, bindUserPhone } from '@/utils/fetch';
import { xflowPushEvent } from '@/utils/pageTrack';
import format from '@/utils/format';
import getChannelInfo from '@/utils/getChannelInfo';
import config, { webviewUrl, __DEBUG__ } from '@/utils/config';
import { getShareInfo } from '@/utils/shareApi';
import { onAdClick } from '@/utils/ad';



import './index.scss';

// --- Type Definitions ---
interface Patient {
  id: number;
  patientName: string;
  patientGender: string;
  age: number;
  patientRelation?: number;
  [key: string]: any; // Allow other properties
}

interface Banner {
  url: string;
  link: string;
  type: string;
  appId?: string;
  adName?: string;
  extraData?: any;
  adPosition?: string;
  adNo?: string;
  adRequestUrl?: string;
  [key: string]: any; // Allow other properties
}

type ShareInfo = Taro.ShareAppMessageReturn | null;


const PersonalCenter = () => {
  const ENV = config.ENV;
  const dispatch = useDispatch();
  const GENDER_OBJ = useSelector((state: any) => state.resource.GENDER_OBJ) || {};

  // --- State ---
  const [phone, setPhone] = useState('');
  const [loading, setLoading] = useState(true);
  const [avatar, setAvatar] = useState('');
  const [nickname, setNickname] = useState('');
  const [channelRight, setChannelRight] = useState<number | undefined>(undefined);
  const [rightsSum, setRightsSum] = useState(0);
  const [couponSum, setCouponSum] = useState(0);
  const [patientList, setPatientList] = useState<Patient[]>([]);
  const [swiperCurrent, setSwiperCurrent] = useState(0);
  const [unfinishedInquiry, setUnfinishedInquiry] = useState(false);
  const [unusedPrescription, setUnusedPrescription] = useState(false);
  const [orderStatusList, setOrderStatusList] = useState([ // This is static, so it can remain
    { title: '待付款', status: 1, icon: CDN_PREFIX + 'common/orderStatus-01.png', unPaidSum: 1 },
    { title: '待发货', status: 5, icon: CDN_PREFIX + 'common/orderStatus-02.png' },
    { title: '已发货', status: 6, icon: CDN_PREFIX + 'common/orderStatus-03.png' },
    { title: '已完成', status: 7, icon: CDN_PREFIX + 'common/orderStatus-04.png' },
    { title: '已取消', status: 99, icon: CDN_PREFIX + 'common/orderStatus-09.png' },
  ]);
  const [unPaidSum, setUnPaidSum] = useState(0);
  const [serviceToolsList, setServiceToolsList] = useState([ // This is also static
    { title: '挂号记录', icon: CDN_PREFIX + 'common/icon-tool01.png', type: 'thirdPartner', config: { partnerCode: 'WEIYI', businessType: 'list' } },
    { title: '体检记录', target: 'mycheckup', icon: CDN_PREFIX + 'common/icon-tool02.png' },
    { title: '咨询记录', target: 'mymentalConsult', icon: CDN_PREFIX + 'common/icon-tool06.png' },
    { title: '收货地址', target: 'myaddress', icon: CDN_PREFIX + 'common/icon-tool03.png' },
    { title: '意见反馈', target: 'feedback', icon: CDN_PREFIX + 'common/icon-tool04.png' },
    { title: '在线客服', icon: CDN_PREFIX + 'common/icon-tool07.png', target: 'service', url: 'https://webchat-sh.clink.cn/chat.html?accessId=1936f9be-d24f-46cf-b0d1-09d34b8bef9d&language=zh_CN' },
    { title: '设置', icon: CDN_PREFIX + 'common/icon-tool-setting.webp', target: 'setting' },
  ]);
  const [footerBannerList, setFooterBannerList] = useState<Banner[]>([]);
  const [footerSwiperActiveIndex, setFooterSwiperActiveIndex] = useState(0);

  const [isShowAuthModal, setIsShowAuthModal] = useState(false);
  const [shareInfo, setShareInfo] = useState<ShareInfo>(null);

  const channelRightTextNames = {
    0: '限时免费',
    1: 'VIP免费',
  };
  // --- End State ---

  // --- Ilog and Navigation Handlers ---
  const sendIlog = (value: string) => {
    xflowPushEvent({
      event: 'click',
      eventTag: 'ZAHLWYY_GRZXY',
      text: '个人中心页',
      attributes: JSON.stringify({ ZAHLWYY_CLICK_CONTENT: value }),
    });
  }

  const toWebview = (pathname = '', params = '', ilogValue = '') => {
    if (ilogValue) {
      sendIlog(ilogValue);
    }
    if (!pathname) return;

    const src = encodeURIComponent(`${webviewUrl}${pathname}`);
    const url = params
      ? `/pages/webview/index?src=${src}&param=${params}`
      : `/pages/webview/index?src=${src}`;
    Taro.navigateTo({ url });
  }

  const handleNavigate = (authCallback: () => void) => {
    if (!phone) {
      setIsShowAuthModal(true);
    } else {
      authCallback();
    }
  }

  const handleServiceToolClick = (item: any) => {
    handleNavigate(() => {
      const { title = '', target = '', type = '', config = {}, url } = item;
      sendIlog('个人中心_' + title); // Send ilog for service tools
      if (target) {
        if (target === 'service') {
          Taro.navigateTo({ url: `/pages/webview/index?src=${encodeURIComponent(url)}` });
          return;
        }
        toWebview(`/hospital/${target}`, '', title);
      } else if (type === 'thirdPartner') {
        const params = JSON.stringify(config);
        toWebview(`/hospital/preauth`, params, title);
      } else if (type === 'applet') {
        url && Taro.navigateTo({ url });
      }
    });
  };

  const toProfile = () => {
    handleNavigate(() => {
      Taro.navigateTo({ url: '/pages/user/profile/index' });
    });
  }

  const handleAuthSuccess = (accountPhone: string) => {
    if (accountPhone) {
      setPhone(format.noPassByMobile(accountPhone));
      getData(); // Re-fetch data after login
    }
    setIsShowAuthModal(false);
  }

  const handleGetUserPhone = async (e: any) => {
    if (e.detail.encryptedData && e.detail.iv) {
      try {
        await bindUserPhone({
          encryptedData: e.detail.encryptedData,
          iv: e.detail.iv,
          eventType: 1,
        });
        // After binding, user info in storage is updated, so just re-fetch everything.
        getData();
      } catch (error) {
        Taro.showToast({
          icon: 'none',
          title: '获取手机号码出错',
        });
      }
    }
  };

  // --- Data Fetching Functions ---
  const getRedPot = () => {
    fetchJson({
      url: "/zaApi/v1/patient/user/userStatistics",
      method: "GET",
      data: {}, // In WePY this was not passed, but it's good practice.
      needLoading: false,
    }).then(res => {
      if (res.code === '0' && res.result) {
        const { unfinishedInquiry = false, unusedPrescription = false, unPaidSum = 0} = res.result;
        setUnfinishedInquiry(unfinishedInquiry);
        setUnusedPrescription(unusedPrescription);
        setUnPaidSum(unPaidSum);
      }
    });

    fetchJson({
      url: "/zaApi/v1/patient/user/userRightsCount",
      method: "GET",
      data: {},
      needLoading: false,
    }).then(res => {
      if (res.code === '0' && res.result) {
        const { rightsSum = 0, couponSum = 0 } = res.result;
        setRightsSum(rightsSum);
        setCouponSum(couponSum);
      }
    });
  }

  const getUserChannel = () => {
    fetchJson({
      url: '/zaApi/v1/patient/channel/getUserChannel',
      method: 'GET',
      data: {
        channelSource: getChannelInfo.channelSource(),
        channelResourceCode: getChannelInfo.channelResourceCode(),
      },
      needLoading: false,
      needLogin: false,
    }).then(res => {
      if (res.code === '0' && res.result) {
        const { channelRight } = res.result || {};
        setChannelRight(channelRight);
      }
    });
  };

  const getBannerList = () => {
    fetchJson({
      url: '/zaApi/v1/patient/ad/list',
      method: 'POST',
      needLogin: false,
      needLoading: false,
      data: {
        option: {
          needAdPicture: true,
          needFilterExcludeResource: true,
        },
        channelSource: getChannelInfo.channelSource(),
        channelResourceCode: getChannelInfo.channelResourceCode(),
        adPositionList: ['personal_center'],
      },
    }).then(res => {
      if (res.code === '0' && res.result && res.result.length > 0) {
        const bannerListProcessed = res.result.map(k => {
          const {
            attachmentDomain: { attachmentDownloadUrl = '' } = {},
          } = k;
          return {
            ...k,
            url: format.imageUrl(attachmentDownloadUrl),
            type: k.adType,
            link: k.adRequestUrl,
            appId: k.targetAppId,
            extraData: k.extraData,
            adName: k.adName || '',
          };
        });
        setFooterBannerList(bannerListProcessed);
      }
    });
  };

  const getPatientList = () => {
    fetchJson({
      url: '/zaApi/v1/patient/patient/list',
      method: 'POST',
      data: {},
      needLoading: false,
    }).then((res) => {
      const { code = '', result = [] } = res;
      if (code === '0' && Array.isArray(result) && result.length) {
        const patientListProcessed: Patient[] = result.map((k) => {
          return {
            ...k,
            age: k.patientBirthday ? format.GetAgeByBirthday(k.patientBirthday) : 0,
            patientGender: GENDER_OBJ[k.patientGender] || '1',
          };
        });

        setPatientList(patientListProcessed);

        const self = patientListProcessed.find((i) => i.patientRelation === 1);
        if (self) {
          storage.set('realName', REAL_STATUS.auth);
        }
      } else {
        setPatientList([]);
      }
    });
  };

  const initShare = async () => {
    try {
      const res = await getShareInfo('/pages/personalcenter');
      setShareInfo(res);
    } catch (error) {
      console.error("Failed to get share info", error);
      // Set a default or empty share object to prevent crash
      setShareInfo({
        title: '众安健康',
        path: '/pages/index/index',
      });
    }
  };

  const getData = () => {
    const { userHeadPortrait = '', userNickname = '', accountPhone = '' } = storage.get('userInfo') || {};
    setNickname(userNickname);
    setAvatar(userHeadPortrait);
    setPhone(format.noPassByMobile(accountPhone));
    setLoading(false);

    getRedPot();
    getPatientList();
  };

  // --- Lifecycle Hooks ---
  useEffect(() => {
    getUserChannel();
    getBannerList();
    dispatch(fetchResourceList());
    initShare();
  }, []);

  useDidShow(() => {
    xflowPushEvent({
      event: 'click',
      eventTag: 'ZAHLWYY_SY',
      text: '首页',
      attributes: JSON.stringify({
        ZAHLWYY_CLICK_CONTENT: '首页_个人中心',
      })
    });

    const token = storage.get('Token') || '';
    if (token) {
      getData();
    } else {
      login().then((accountPhone) => {
        if (accountPhone) {
          getData();
        } else {
          setLoading(false);
        }
      });
    }
  });

  useShareAppMessage(() => {
    // TODO: Legacy share tracking logic from wepy needs to be reimplemented.
    // Original code:
    // app.za.para.autoTrack.pageShare = {
    //   shareTitle: '健康管理',
    //   shareDesc: '健康管理分享',
    // };
    return shareInfo || {
      title: '众安健康',
      path: '/pages/index/index',
    };
  });


  return (
    <View
      className="personalcenter-page"
      style={{ backgroundImage: `url(${CDN_PREFIX}common/header-bg.png)` }}
    >
      <View className="personalcenter-page__hd">
        <View className="user-info flex">
          {!phone && !loading ? (
            <Button
              className="head custome_button flex-y"
              openType="getPhoneNumber"
              onGetPhoneNumber={handleGetUserPhone}
            >
              <Image className="user-icon" src={defaultAvatar} />
              <View className="user-core">
                <View className="user-name">登录</View>
                <View className="rights-mark">获取医疗权益大礼包</View>
              </View>
            </Button>
          ) : null}
          {phone && !loading ? (
            <Block>
              <Image className="user-icon" src={avatar || defaultAvatar} onError={() => setAvatar(defaultAvatar)} onClick={toProfile} />
              <View className="user-core">
                <View onClick={toProfile}>
                  <View className="user-name">{nickname}</View>
                  <View>{phone}</View>
                  <View className="rights-mark">众安医疗权益用户</View>
                </View>
                {channelRight != null && (
                  <Text className="vip-mark">
                    <Text className="text">{channelRightTextNames[channelRight]}健康咨询</Text>
                  </Text>
                )}
              </View>
            </Block>
          ) : null}
        </View>
        <View className="core-column flex-y">
          <View className="column-item flex" onClick={() => handleNavigate(() => toWebview('/hospital/myrights', '', '个人中心_权益中心'))}>
            <Image className="icon" src={`${CDN_PREFIX}common/icon-rights.png`} />
            <View className="det">
              <View>
                <Text className="strong">{rightsSum}</Text>项
              </View>
              <View className="belonging">我的权益</View>
            </View>
          </View>
          <View className="column-item flex" onClick={() => handleNavigate(() => toWebview('/hospital/mycoupon', '', '个人中心_优惠券'))}>
            <Image className="icon" src={`${CDN_PREFIX}common/icon-coupon.png`} />
            <View className="det">
              <View>
                <Text className="strong">{couponSum}</Text>张
              </View>
              <View className="belonging">我的优惠劵</View>
            </View>
          </View>
        </View>
      </View>

      <View className="personalcenter-page__main">
        <View className="personalcenter-page__main-title">
          <Image className="icon" src={`${CDN_PREFIX}common/icon-archives.png`} />
          <View className='link-family-info' onClick={() => handleNavigate(() => toWebview('/hospital/family-info', '', '成员列表'))}>家庭成员</View>
        </View>
        {patientList.length > 0 && (
          <Swiper
            className="personalcenter-page__main-carousel"
            autoplay={false}
            circular={true}
            onChange={(e) => setSwiperCurrent(e.detail.current)}
            style={{ backgroundImage: `url(${CDN_PREFIX}common/icon-heart.png)` }}
          >
            {patientList.map((item) => (
              <SwiperItem key={item.id} className="carousel__item">
                <View className="single-col flex-y flex-justify" onClick={() => handleNavigate(() => {
                  const params = JSON.stringify({ patientId: item.id });
                  toWebview('/hospital/healthArchive/home', params, '个人中心_查看健康档案')
                })}>
                  <View className="patient-info">
                    <Text className="strong">{item.patientName}</Text> {item.patientGender} {item.age}岁
                  </View>
                  <View className="look-btn">
                    <Image className="plus" src={`${CDN_PREFIX}common/icon-plus.png`} />
                    查看档案
                  </View>
                </View>
              </SwiperItem>
            ))}
          </Swiper>
        )}
        {patientList.length > 0 && (
          <View className="carousel-pagination">
            {patientList.map((item, index) => (
              <Text key={index} className={`dot ${swiperCurrent === index ? 'active' : ''}`}></Text>
            ))}
          </View>
        )}
        <View className="personalcenter-page__main-minor flex-y flex-justify">
          <View
            className={`minor-part ${unfinishedInquiry ? 'red' : ''}`}
            style={{ backgroundImage: `url(${CDN_PREFIX}common/icon-inquiry.png)` }}
            onClick={() => handleNavigate(() => {
              const params = JSON.stringify({ defaultType: 1 });
              toWebview('/hospital/myinquiry', params, '个人中心_我的问诊');
            })}
          >
            问诊记录
          </View>
          <View
            className={`minor-part ${unusedPrescription ? 'red' : ''}`}
            style={{ backgroundImage: `url(${CDN_PREFIX}common/icon-rp.png)` }}
            onClick={() => handleNavigate(() => {
              const params = JSON.stringify({ defaultType: 1 });
              toWebview('/hospital/myprescription', params, '个人中心_处方管理');
            })}
          >
            处方管理
          </View>
        </View>
      </View>

      <View className="personalcenter-page__piece">
        <View className="personalcenter-page__piece-title">
          我的订单
          <View className="look-all" onClick={() => handleNavigate(() => {
            const params = JSON.stringify({ orderStatus: 0 });
            toWebview('/hospital/myorder', params, '个人中心_我的订单_查看全部');
          })}>查看全部 </View>
        </View>
        <View className="personalcenter-page__piece-orders flex">
          {orderStatusList.map((item, index) => (
            <View key={index} className="order-category" onClick={() => handleNavigate(() => {
              const params = JSON.stringify({ orderStatus: item.status });
              toWebview('/hospital/myorder', params, `个人中心_我的订单_${item.title}`);
            })}>
              <View className="category-icon">
                <Image className="img" src={item.icon} />
                {item.status === 1 && unPaidSum ? (
                  <View className="order-num">
                    <Text>{unPaidSum}</Text>
                  </View>
                ) : null}
              </View>
              <View className="category-title">{item.title}</View>
            </View>
          ))}
        </View>
      </View>

      <View className="personalcenter-page__piece">
        <View className="personalcenter-page__piece-title">服务与工具</View>
        <View className="personalcenter-page__piece-tool flex">
          {serviceToolsList.map((item, index) => (
            <View key={index} className="tool-item" onClick={() => handleServiceToolClick(item)}>
              <View className="category-icon">
                <Image className="img" src={item.icon} />
              </View>
              <View className="tool-name">{item.title}</View>
            </View>
          ))}
          {__DEBUG__ && (
            <Navigator url="/pages/debugger/index" className="tool-item">
              <View className="category-icon">
                <Image className="img" src={`${CDN_PREFIX}common/icon-toggle.svg`} />
              </View>
              <View className="tool-name">环境切换{ENV}</View>
            </Navigator>
          )}
        </View>
      </View>

      <AuthModal show={isShowAuthModal} onClose={() => setIsShowAuthModal(false)} onAuthSuccess={handleAuthSuccess} />

      <View>
        {footerBannerList.length > 1 ? (
          <Block>
            <View className="footer-swiper">
              <Swiper className="swiper" autoplay={true} interval={3000} onChange={(e) => setFooterSwiperActiveIndex(e.detail.current)}>
                {footerBannerList.map((item, index) => (
                  <SwiperItem key={index} className="slide_item" onClick={() => {
                    // This one does not require auth
                    const { link, type, appId = '', adName = '', extraData = {}, adPosition, adNo, adRequestUrl } = item;
                    onAdClick({
                      adRequestUrl: link,
                      adType: type,
                      appId,
                      adName,
                      extraData,
                      adPosition,
                      adNo
                    });
                    if (type === 'applet' && appId) {
                      Taro.navigateToMiniProgram({ appId, path: link, extraData: item.extraData });
                    } else if (type === 'h5' && adRequestUrl) {
                      toWebview(adRequestUrl, '', 'ad_click');
                    }
                  }}>
                    <Image src={item.url} className="swiper_image" />
                  </SwiperItem>
                ))}
              </Swiper>
              <View className="carousel-pagination">
                {footerBannerList.map((item, index) => (
                  <Text key={index} className={`dot ${footerSwiperActiveIndex === index ? 'active' : ''}`}></Text>
                ))}
              </View>
            </View>
            <View className="footer-swiper__placeholder"></View>
          </Block>
        ) : footerBannerList.length === 1 ? (
          <Block>
            <View className="footer-swiper">
              <Image src={footerBannerList[0].url} className="swiper_image" onClick={() => {
                  const { link, type, appId = '', adRequestUrl, adName, extraData, adPosition, adNo } = footerBannerList[0];
                  onAdClick({
                    adRequestUrl: link,
                    adType: type,
                    appId,
                    adName,
                    extraData,
                    adPosition,
                    adNo
                  });
                  if (type === 'applet' && appId) {
                    Taro.navigateToMiniProgram({ appId, path: link, extraData: footerBannerList[0].extraData });
                  } else if (type === 'h5' && adRequestUrl) {
                    toWebview(adRequestUrl, '', 'ad_click');
                  }
              }}/>
            </View>
            <View className="footer-swiper__placeholder"></View>
          </Block>
        ) : null}
      </View>
    </View>
  );
};

export default PersonalCenter;
