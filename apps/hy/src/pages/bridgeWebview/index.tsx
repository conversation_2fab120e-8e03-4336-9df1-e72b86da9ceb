import React, { useState, useEffect } from 'react';
import { WebView } from '@tarojs/components';
import Taro, { getCurrentInstance, setNavigationBarTitle } from '@tarojs/taro';
import { getWebviewUrl } from '@/utils/tools';
import getSearch from '@/utils/getSearch';

const BridgeWebviewPage = () => {
  const [webviewSrc, setWebviewSrc] = useState('');
  const instance = getCurrentInstance();

  useEffect(() => {
    const { router } = instance;
    if (!router || !router.params) {
      console.error('无法获取页面参数');
      return;
    }

    const { target = '', title = '' } = router.params;

    if (title) {
      setNavigationBarTitle({ title: title });
    }

    getSearch().then(() => {
      // 移除多余的 decodeURIComponent，因为 getWebviewUrl 内部会处理
      const finalSrc = getWebviewUrl(target, '');
      setWebviewSrc(finalSrc);
    });

  }, [instance.router?.params]);

  return (
    <WebView src={webviewSrc} />
  );
};

export default BridgeWebviewPage;
