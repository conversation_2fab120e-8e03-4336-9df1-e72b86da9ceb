import React, { useState, useEffect } from 'react';
import { View, Text, Button, Image, Checkbox, CheckboxGroup, Label } from '@tarojs/components';
import Taro, { getCurrentInstance } from '@tarojs/taro';
import './index.scss';

// 导入工具函数和资源
import { bindUserPhone } from '@/utils/fetch';
import { xflowPushEvent } from '@/utils/pageTrack';
import { webviewUrl } from '@/utils/config';
import storage from '@/utils/storage';
import format from '@/utils/format';
import loginIcon from '@/images/login/icon.png';
import closeAgreeIcon from '@/images/close-agree.png';

interface LoginPageProps {}

const LoginPage: React.FC<LoginPageProps> = () => {
  // 状态管理
  const [checked, setChecked] = useState(false);
  const [showAgreement, setShowAgreement] = useState(false);
  const [showtip, setShowtip] = useState(false);
  const [loading, setLoading] = useState(false);

  // 路由参数
  const [returnUrl, setReturnUrl] = useState('');
  const [pathname, setPathname] = useState('');
  const [search, setSearch] = useState('');
  const [toggleAccountChannelResource, setToggleAccountChannelResource] = useState('');

  // 获取路由参数
  useEffect(() => {
    const instance = getCurrentInstance();
    const { returnUrl = '', pathname = '', search = '', toggleAccountChannelResource = '' } = instance.router?.params || {};

    setReturnUrl(returnUrl);
    setPathname(decodeURIComponent(pathname));
    setSearch(decodeURIComponent(search));
    setToggleAccountChannelResource(toggleAccountChannelResource);

    console.log('login页面参数:', { returnUrl, pathname, search, toggleAccountChannelResource });
  }, []);

  // 处理复选框变化
  const handleCheckboxChange = (e: any) => {
    const isChecked = !!(e.detail && e.detail.value.length > 0);
    setChecked(isChecked);
    if (showtip && isChecked) {
      setShowtip(false);
    }
  };

  // 显示协议弹窗或提示
  const handleShowAgreement = () => {
    if (!checked) {
      setShowAgreement(true);
      xflowPushEvent({
        event: 'click',
        eventTag: 'PROTOCOL_POPUP_SHOW_YSK_MA',
        text: '显示协议弹窗',
      });
    } else {
      // 如果已经同意协议，显示提示
      setShowtip(true);
      setTimeout(() => setShowtip(false), 2000);
    }
  };

  // 切换协议弹窗显示状态
  const toggleAgreement = () => {
    setShowAgreement(!showAgreement);
    if (showAgreement) {
      xflowPushEvent({
        event: 'click',
        eventTag: 'PROTOCOL_REJECT_CLICK_YSK_MA',
        text: '不同意与退出弹窗',
        attributes: '不同意与退出弹窗',
      });
    }
  };

  // 同意协议并获取手机号
  const handleAgreeAndGetPhone = (e: any) => {
    xflowPushEvent({
      event: 'click',
      eventTag: 'PROTOCOL_AGREEMENT_CLICK_YSK_MA',
      text: '同意协议',
    });
    setChecked(true);
    setShowAgreement(false);
    // 直接调用获取手机号
    handleGetPhoneNumber(e);
  };

  // 获取手机号授权
  const handleGetPhoneNumber = async (e: any) => {
    if (!e.detail.encryptedData || !e.detail.iv) {
      console.log('用户取消授权或授权失败');
      // 用户取消授权时的埋点
      xflowPushEvent({
        event: 'click',
        eventTag: 'MOBILE_AUTHORIZATION_CANCEL_YSK_MA',
        text: '用户取消手机号授权',
      });
      return;
    }

    setLoading(true);

    try {
      // 埋点：开始授权
      xflowPushEvent({
        event: 'click',
        eventTag: 'MOBILE_AUTHORIZATION_START_YSK_MA',
        text: '开始手机号授权',
      });

      await bindUserPhone({
        encryptedData: e.detail.encryptedData,
        iv: e.detail.iv,
        eventType: 1,
        toggleAccountChannelResource,
      });

      const { accountPhone = '' } = storage.get('userInfo') || {};

      if (accountPhone) {
        // 埋点：授权成功
        xflowPushEvent({
          event: 'click',
          eventTag: 'MOBILE_AUTHORIZATION_SUCCESS_YSK_MA',
          text: '手机号授权成功',
        });

        // 登录成功，处理页面跳转
        await handleLoginSuccess();
      } else {
        // 埋点：获取手机号失败
        xflowPushEvent({
          event: 'click',
          eventTag: 'MOBILE_AUTHORIZATION_FAIL_YSK_MA',
          text: '获取手机号失败',
        });

        Taro.showToast({
          icon: 'none',
          title: '获取手机号码出错'
        });
      }
    } catch (error) {
      console.error('登录失败:', error);

      // 埋点：登录异常
      xflowPushEvent({
        event: 'click',
        eventTag: 'LOGIN_ERROR_YSK_MA',
        text: '登录过程异常',
        attrs: { error: error?.message || '未知错误' }
      });

      Taro.showToast({
        icon: 'none',
        title: '登录失败，请重试'
      });
    } finally {
      setLoading(false);
    }
  };

  // 处理登录成功后的跳转
  const handleLoginSuccess = async () => {
    try {
      if (returnUrl) {
        const decodedReturnUrl = decodeURIComponent(returnUrl);

        // 埋点：跳转到指定页面
        xflowPushEvent({
          event: 'click',
          eventTag: 'LOGIN_REDIRECT_YSK_MA',
          text: '登录成功跳转',
          attrs: { returnUrl: decodedReturnUrl }
        });

        // 如果是tab页面，使用switchTab
        if (['/pages/index', '/pages/personalcenter'].includes(decodedReturnUrl)) {
          return Taro.switchTab({ url: decodedReturnUrl });
        }

        // 其他页面使用redirectTo
        return Taro.redirectTo({ url: decodedReturnUrl });
      }

      // 如果没有returnUrl，跳转到webview页面
      if (pathname) {
        const src = `${webviewUrl}${pathname}`;
        const param = JSON.stringify(format.deserialize(search));

        // 埋点：跳转到webview
        xflowPushEvent({
          event: 'click',
          eventTag: 'LOGIN_TO_WEBVIEW_YSK_MA',
          text: '登录成功跳转到webview',
          attrs: { pathname, search }
        });

        return Taro.redirectTo({
          url: `/pages/webview/index?src=${encodeURIComponent(src)}&param=${encodeURIComponent(param)}`
        });
      }

      // 埋点：跳转到首页
      xflowPushEvent({
        event: 'click',
        eventTag: 'LOGIN_TO_HOME_YSK_MA',
        text: '登录成功跳转到首页',
      });

      // 默认跳转到首页
      Taro.switchTab({ url: '/pages/index/index' });
    } catch (error) {
      console.error('页面跳转失败:', error);

      // 埋点：跳转失败
      xflowPushEvent({
        event: 'click',
        eventTag: 'LOGIN_REDIRECT_ERROR_YSK_MA',
        text: '登录后页面跳转失败',
        attrs: { error: error?.message || '未知错误' }
      });

      // 跳转失败时，默认跳转到首页
      Taro.switchTab({ url: '/pages/index/index' });
    }
  };

  // 跳转到协议页面
  const handleGoToAgreement = (type: 'agreement' | 'private') => {
    // 埋点：点击协议链接
    xflowPushEvent({
      event: 'click',
      eventTag: type === 'agreement' ? 'USER_AGREEMENT_CLICK_YSK_MA' : 'PRIVACY_POLICY_CLICK_YSK_MA',
      text: type === 'agreement' ? '点击用户协议' : '点击隐私政策',
    });

    let url = '';
    if (type === 'agreement') {
      url = `${webviewUrl}/hospital/agreement?channelSource=h5`;
    } else {
      url = `${webviewUrl}/hospital/private?channelSource=h5`;
    }

    Taro.redirectTo({
      url: `/pages/webview/index?src=${encodeURIComponent(url)}`
    });
  };



  return (
    <View className="login_page">
      <View className="icon_wrapper">
        <Image src={loginIcon} />
        <View className="text">7*24小时免费不限次在线咨询</View>
      </View>

      {/* 登录按钮 */}
      <Button
        className={`btn ${checked ? 'hide' : 'show'}`}
        onClick={handleShowAgreement}
      >
        手机号快捷登录
      </Button>

      <Button
        className={`btn ${checked ? 'show' : 'hide'}`}
        openType="getPhoneNumber"
        onGetPhoneNumber={handleGetPhoneNumber}
        loading={loading}
      >
        手机号快捷登录
      </Button>

      {/* 协议复选框 */}
      <View className="checkbox_wrap">
        <View className={`tips ${showtip ? 'showtip' : 'hide'}`}>请先勾选同意</View>
        <CheckboxGroup onChange={handleCheckboxChange}>
          <Label className="flex">
            <View className="check-box">
              <Checkbox className="checkbox" value="notice" checked={checked} />
            </View>
            <View className="auth_modal__agreement">
              <View className="text">我已阅读并同意</View>
              <View className="link" onClick={() => handleGoToAgreement('agreement')}>
                《众安用户注册协议》
              </View>
              <View className="text">和</View>
              <View className="link" onClick={() => handleGoToAgreement('private')}>
                《个人隐私政策协议》
              </View>
              <View className="text">，未注册绑定的手机号验证成功后将自动注册。</View>
            </View>
          </Label>
        </CheckboxGroup>
      </View>

      {/* 协议弹框 */}
      <View className={`login-popup ${showAgreement ? 'show' : ''}`}>
        <View className="box agreement" onClick={(e) => e.stopPropagation()}>
          <Label className="pay-popup_agreement">
            <View className="title">用户协议及隐私保护</View>
            <View className="text">
              我已阅读并同意
              <Text className="agreement" onClick={() => handleGoToAgreement('agreement')}>
                《众安用户注册协议》
              </Text>
              和
              <Text className="agreement" onClick={() => handleGoToAgreement('private')}>
                《个人隐私政策协议》
              </Text>
              ，未注册绑定的手机号验证成功后将自动注册
            </View>
            <View className="pay-popup_operate">
              <Button className="pay-popup_operate-btn cancel" onClick={toggleAgreement}>
                不同意
              </Button>
              <Button
                className="pay-popup_operate-btn agree"
                openType="getPhoneNumber"
                onGetPhoneNumber={handleAgreeAndGetPhone}
              >
                同意
              </Button>
            </View>
          </Label>
          <Image onClick={toggleAgreement} className="close" src={closeAgreeIcon} />
        </View>
      </View>
    </View>
  );
};

export default LoginPage;
