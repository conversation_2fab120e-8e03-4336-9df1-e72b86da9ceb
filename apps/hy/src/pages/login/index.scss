.login_page {
  background: #ffffff;
  min-height: 100vh;
  font-family: PingFangSC-Regular, PingFang SC;

  .icon_wrapper {
    padding: 220rpx 0 0;

    image {
      margin: 0 auto;
      display: block;
      width: 378rpx;
      height: 166rpx;
    }

    .text {
      margin-top: 22rpx;
      text-align: center;
      color: #999999;
      font-size: 30rpx;
      line-height: 42rpx;
    }
  }

  .btn {
    width: 630rpx;
    height: 88rpx;
    line-height: 88rpx;
    margin: 98rpx auto 0;
    background: #00bc70;
    border-radius: 44rpx;
    color: #ffffff;
    font-size: 34rpx;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;

    &:active {
      opacity: 0.6;
    }

    &:after,
    &:before {
      display: none;
    }
  }

  .hide {
    display: none;
  }

  .show,
  .showtip {
    display: block;
  }

  .checkbox_wrap {
    margin-top: 50rpx;
    padding: 0 40rpx;
    font-size: 28rpx;
    text-align: left;
    position: relative;

    .checkbox {
      transform: scale(0.6) translate(-5rpx);
    }

    .check-box {
      width: 32rpx;
      margin-right: 20rpx;
    }

    .tips {
      width: 147rpx;
      height: 32rpx;
      background: rgba(0, 0, 0, 0.6);
      position: absolute;
      left: 57rpx;
      top: -35rpx;
      border-radius: 18rpx 18rpx 18rpx 0rpx;
      font-size: 20rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 32rpx;
      text-align: center;
    }
  }

  .flex {
    display: flex;
    align-self: center;
  }

  .auth_modal__agreement {
    font-size: 26rpx;

    .text {
      display: inline;
      color: rgba(51, 51, 51, 0.6);
    }

    .link {
      display: inline;
      color: #00bc7d;
    }
  }
}

// 复选框样式
checkbox .wx-checkbox-input {
  border-radius: 50%;
}

checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  border: none;
  background: url('https://cdn-qcloud.zhongan.com/a00000/za-asclepius/applets/guide/tick.png') no-repeat center center #00bc70;
  background-size: 100% 100%;
}

checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
  width: 32rpx;
  height: 32rpx;
  line-height: 30rpx;
  text-align: center;
  font-size: 0;
  color: #00bc70;
  background: transparent;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
}

// 协议弹框样式
.login-popup {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 20;
  display: none;

  .box {
    width: 80%;
    background: #FFFFFF;
    border-radius: 24PX;
    margin-top: 50%;
    margin-left: 10%;
    position: relative;
  }

  .title {
    text-align: center;
    font-size: 37rpx;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #1E1E1E;
    padding-top: 50rpx;
  }

  .text {
    font-size: 24rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #999999;
    padding: 36rpx 50rpx;
  }

  .agreement {
    color: #00BC70;
  }

  &.show {
    display: block;
  }

  .pay-popup_operate {
    padding: 0 46rpx 46rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-btn {
      border-radius: 50rpx;
      font-weight: 400;
      box-sizing: border-box;
      font-size: 36rpx;
      width: 45%;
      height: 80rpx;
      line-height: 80rpx;

      &.cancel {
        color: #00BC70;
        border: 1rpx solid #00BC70;
      }

      &.agree {
        background: #00BC70;
        color: #fff;
      }
    }
  }

  .close {
    width: 72rpx;
    height: 72rpx;
    position: absolute;
    bottom: -40rpx;
    left: 50%;
    transform: translateX(-50%) translateY(100%);
  }
}
