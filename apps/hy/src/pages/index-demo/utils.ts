import { getStorageSync, setStorage } from '@/utils/storage';

function getYMD() {
  const date = new Date();
  const dayStr = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
  return dayStr;
}

export function checkSignModalState() {
  const ymdStr = getYMD();
  const signModalSession = getStorageSync('signModal');

  if (!signModalSession) {
    setStorage('signModal', ymdStr);
    // 第一次进入首页
    return true;
  }

  const isEqual = signModalSession === ymdStr;

  // 不是同一天进入
  if (!isEqual) {
    // 设置为最新值
    setStorage('signModal', ymdStr);
    return true;
  }
  // 同一天多次进入首页
  return false;
}

export default function noop() {}
