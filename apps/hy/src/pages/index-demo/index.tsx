import React, { useContext } from 'react';
import { View } from '@tarojs/components';
import Taro, { useShareAppMessage, useReady, useRouter } from '@tarojs/taro';

import { navigateToLink } from '@/utils';

import Debug from '@/utils/debug';

import Demo from '@/components/demo';
import ValidateElement from '@/components/validate-element';
import SubscribeMask from '@/components/subscribe-mask';

import FloatBanner from '@/components/float-banner';
import PageModal from '@/components/page-modal';
import SubscribeMaskProvider from '@/context/subscribe-mask';
import useConfig from '@/hooks/useConfig';
import { FLOAT_BANNER_LOCATION } from '@/constants';

import useRecommendConfig from '@/hooks/useRecommendConfig';
import Banner from '@/components/banner';
import withComponents from '@/components/withComponents';
import TopHeader from './components/top-header';

import ArticleList from './components/articles';
import Recommend from './components/recommend';
import ActivityEnter from './components/activity-enter';
import ActivityBanner from './components/activity-banner';
import PopupBanner from '../../components/popup-banner';
import Insurance from './components/insurance';
import EverybodyBuy from './components/everybody-buy';
import Claim from './components/claim';

import './index.scss';

const debug = Debug('Main');

function Index() {
  const { params } = useRouter();

  const { data: config } = useConfig();
  const { data: recommend } = useRecommendConfig();

  useReady(() => {
    // appId === 'wx00bc516a4d1619ef' && extraData?.['out_trade_no']
    if (params?.appid === 'wx00bc516a4d1619ef' && params?.out_trade_no) {
      navigateToLink('https://im2.zhongan.io/zaim/?accessId=1623289758025&tenantID=1514946886000043&color=green&channelId=A1&subject=微信支付联系商家或投诉&biz=微信', { navigateType: 'redirectTo' });
    }
  });
  useShareAppMessage(() => {
    debug.debug('useShareAppMessage');

    return {
      title: '国民医保，保障您和家人健康无忧',
      path: '/pages/index/index?form=share',
    };
  });

  const gray = config?.config?.grayFilter ? 'gray' : '';

  const banner = recommend?.banner_below_king_position_in_home_page || [];

  // if (config?.config?.grayFilter) {
  //   Taro.setTabBarItem({
  //     index: 0,
  //     text: '首页',
  //     iconPath: '/assets/tab-bar/home.png',
  //     selectedIconPath: '/assets/tab-bar/home.png',
  //   });

  //   Taro.setTabBarStyle({
  //     color: '#f1f1f1',
  //     selectedColor: '#f1f1f1',
  //     backgroundColor: '#fff',
  //     borderStyle: 'white',
  //   });
  // }

  return (
    <>
      <Demo />
      {/* { renderModal()} */}
      <View className={gray}>
        <SubscribeMaskProvider>
          <TopHeader />
          <SubscribeMask top />
          <PageModal />
        </SubscribeMaskProvider>
        <Banner banners={banner} className="mt-20 mb-0 mh-auto" width="710" />
        <Insurance />
        <EverybodyBuy />
        <Recommend />
        <ActivityBanner />
        <ActivityEnter />
        <Claim data={config?.claim_example} />
        <ArticleList />
        <ValidateElement env="weapp">
          <View className="footer-logo pb-60" />
        </ValidateElement>
        <PopupBanner />
        <FloatBanner position={FLOAT_BANNER_LOCATION.HOME_PAGE_WINDOW} />
      </View>
    </>
  );
}

// export default Index;
export default withComponents(Index);
