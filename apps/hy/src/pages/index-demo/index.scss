.gray {
  filter: grayscale(100%);
}

.lifestyle {
  z-index: 100;
  position: fixed;
  left: 20px;
  right: 20px;
  bottom: 10px;
  background-color: #fff;

  > .close-btn {
    position: absolute;
    top: -10px;
    right: -10px;
    background-size: 36px;
    background-image: url('https://cdn-health.zhongan.com/magiccube/resource/rl/5IApiBS477.png');
    width: 36px;
    height: 36px;
  }
}

.customer-service-icon {
  background-image: url("https://cdn-health.zhongan.com/hic/library/resource/634661e7dea0db001c89325f/lutnIMMeLz.png");
  background-size: 40px;
}

.activity-icon {
  background-size: 40px 40px;
  background-image: url('https://cdn-health.zhongan.com/hic/library/resource/6358a400659445001c0eda0d/ZCWLmAdlCN.png');
}

.policy-icon {
  background-size: 40px 40px;
  background-image: url('https://cdn-health.zhongan.com/hic/library/resource/6358a400659445001c0eda0d/1N_gH4AA4R.png');
}

.policy {
  .number-scroll-view {
    font-size: 32px;
    color: #F35200;
    font-weight: 600;
    align-items: center;
  }
}
