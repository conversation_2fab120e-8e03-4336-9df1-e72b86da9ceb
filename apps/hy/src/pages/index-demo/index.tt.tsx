import React, { useContext } from 'react';
import { View } from '@tarojs/components';
import { useShareAppMessage, useReady, useRouter } from '@tarojs/taro';
import Debug from '@/utils/debug';

import Demo from '@/components/demo';

import FloatBanner from '@/components/float-banner';
import PageModal from '@/components/page-modal';
import useConfig from '@/hooks/useConfig';

import useRecommendConfig from '@/hooks/useRecommendConfig';
import Banner from '@/components/banner';
import PopupBanner from '@/components/popup-banner';
import withComponents from '@/components/withComponents';
import TopHeader from './components/top-header';

import ArticeList from './components/articles';
import Recommend from './components/recommend';
import Insurance from './components/insurance';
import EverybodyBuy from './components/everybody-buy';
import Claim from './components/claim';

import './index.scss';

const debug = Debug('Main');

function Index() {
  const { data: config } = useConfig();
  const { data: recommend } = useRecommendConfig();

  useShareAppMessage(() => {
    debug.debug('useShareAppMessage');

    return {
      title: '国民医保，保障您和家人健康无忧',
      path: '/pages/index/index?form=share',
    };
  });

  const gray = config?.config?.grayFilter ? 'gray' : '';
  const banner = recommend?.banner_below_king_position_in_home_page || [];

  // useLiveRoomStatus();

  // if (config?.config?.grayFilter) {
  //   Taro.setTabBarItem({
  //     index: 0,
  //     text: '首页',
  //     iconPath: '/assets/tab-bar/home.png',
  //     selectedIconPath: '/assets/tab-bar/home.png',
  //   });

  //   Taro.setTabBarStyle({
  //     color: '#f1f1f1',
  //     selectedColor: '#f1f1f1',
  //     backgroundColor: '#fff',
  //     borderStyle: 'white',
  //   });
  // }

  return (
    <>
      <Demo />
      {/* { renderModal()} */}
      <View className={gray}>
        <TopHeader />
        <PageModal />
        <Banner banners={banner} className="mt-20 mb-0 ml-auto mr-auto" />
        <Insurance />
        <EverybodyBuy />
        <Recommend />
        <Claim data={config?.claim_example} />
        <ArticeList />
        {/* <AwemeData awemeId="24381806838" onError={(e) => console.log(e)} />
        {/* <LivePreview
          awemeId="70516451305"
        /> */}
        {/* <live />  */}
        <View className="footer-logo" />
        <PopupBanner />
        <FloatBanner position="home_page_window" />
      </View>
    </>
  );
}

// export default Index;
export default withComponents(Index);
