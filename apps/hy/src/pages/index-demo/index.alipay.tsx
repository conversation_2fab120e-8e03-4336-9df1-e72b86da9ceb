import React, { useEffect, useState } from 'react';
import { View, Image, Button } from '@tarojs/components';
import config from '@/config';
import cloud from '@tbmp/mp-cloud-sdk';
import { useLoad, useRouter } from '@tarojs/taro';

type UrlType = 'MiniProgramPage' | 'MiniProgramWebView' ;

function Index() {
  const { params } = useRouter(true);
  const { target, urlType = 'MiniProgramPage', encOrderNo } = params;
  const makeUrl = React.useCallback((_url, token) => {
    // 检查是否为非中安域名，如果是则直接返回原始URL
    const domain = _url.match(/(?:https?:\/\/)(?:[^.]+\.)?([^.]+\.[a-zA-Z]{2,})/);
    if (domain && domain[1] !== 'zhongan.com') {
      return _url;
    }

    // 获取主机地址
    let { host } = config.API.web;
    const testEnvMatch = _url.match(/^(https?:\/\/(?:ihealth-test\d?)\.[^:/\n?]+)/i);
    if (testEnvMatch && testEnvMatch[1]) {
      const [_, hostMatch] = testEnvMatch;
      host = hostMatch;
    }

    // 替换token并构建最终URL
    const urlWithToken = _url.replace(/{TOKEN}/g, token);
    return `${host}/api/pocket/mpAuth?token=${token}&key=taobaoOpenId&url=${encodeURIComponent(urlWithToken)}&appId=${config.appId}`;
  }, [config.API.web, config.appId]);
  //   try {
  //     const result = await cloud.topApi.invoke({
  //       api: 'taobao.miniapp.userInfo.get',
  //       authScope: 'scope.userInfo',
  //     });
  //     console.log('result:', result?.result?.model.open_id);
  //     const openId = result?.result?.model.open_id;
  //     my.tb.request({
  //       url: `https://ihealth-test.zhongan.com/api/lemon/v1/taobao/login/c20250410102815002?openId=${openId}`,
  //       method: 'GET',
  //       success: (res) => {
  //         const newUrl = makeUrl(url, JSON.parse(res.content).result);
  //         console.log('newUrl:', newUrl);
  //         my.redirectTo({
  //           url: `/pages/web/index?url=${encodeURIComponent(newUrl)}`,
  //         });
  //       },
  //       fail: (res) => {
  //         my.alert({
  //           title: 'fail',
  //           content: JSON.stringify(res),
  //         });
  //       },
  //     });
  //   } catch (e) {
  //     my.alert({ content: `error ${e.message}` });
  //   }
  // };

  const goto = async (url) => {
    try {
      const result = await cloud.application.httpRequest({
        path: '/v1/taobao/getOpenId',
        method: 'GET',
      });
      const newUrl = makeUrl(url, result);
      my.redirectTo({
        url: `/pages/web/index?url=${encodeURIComponent(newUrl)}`,
      });
    } catch (e) {
      // ({ content: `error ${e.message}` });
    }
  };

  useEffect(() => {
    const newTarget = decodeURIComponent(target!);
    if (newTarget && urlType === 'MiniProgramWebView') {
      if (newTarget === '/policy/list') {
        goto(`${config.API.web.host}/policy/list?channelCode=c20250410102815002&tabs=[1,2]&encOrderNo=${encOrderNo}`);
      } else {
        goto(newTarget);
      }
    }
  }, [target]);

  if (target) {
    return <View><Image src="https://cdn-health.zhongan.com/hic/library/resource/6358a400659445001c0eda0d/4yjY5bpFRj.png" style={{ width: '750rpx', height: '1624rpx' }} /></View>;
  }

  return (
    <View style={{ margin: '100rpx 30rpx' }}>
      <Button
        type="primary"
        onClick={() => {
          my.tb.navigateToTaobaoPage({
            appCode: 'shop',
            appParams: {
              shopId: '107607983',
            },
            fail: (res) => {
              // my.alert({
              //   title: 'fail',
              //   content: JSON.stringify(res),
              // });
            },
          });
        }}
      >
        返回店铺
      </Button>
    </View>
  );
}

export default Index;
