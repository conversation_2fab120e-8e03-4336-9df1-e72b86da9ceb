import React from 'react';
import { View, Image } from '@tarojs/components';
import { navigateTo } from '@/utils';
import useConfig from '@/hooks/useConfig';
import Skeleton from '@/components/skeleton';

function ActivityBanner() {
  const { data, isLoading } = useConfig();

  const recommend = data?.home_block_banner || [];

  if (!isLoading && recommend?.length < 1) {
    return null;
  }

  const renderContent = () => {
    if (isLoading) {
      return (
        <View>
          <Skeleton.Row>
            <Skeleton.Image style={{ width: '300rpx', height: '205rpx', marginRight: '20px' }} />
            <Skeleton.Image style={{ width: '300rpx', height: '205rpx' }} />
          </Skeleton.Row>
        </View>
      );
    }
    return (
      <>
        {
          recommend?.map((item, index) => (
            <View
              key={item.img}
              className={`flex-1 ${index === 0 ? 'mr-20' : ''}`}
              data-ilog={{
                CommonClick: index === 0 ? 'xcx_sy_mfzq' : 'xcx_sy_hdzq',
                custom_event_description: index === 0 ? '免费专区入口' : '活动专区入口',
              }}
              onClick={() => {
                navigateTo(item);
              }}
            >
              <Image src={item.img} className="w-305 h-200" />
            </View>
          ))
        }
      </>
    );
  };

  return (
    <View className="mr-30 ml-30 bg-fff p-30 b-rd-14 mt-20">
      {/* <View className={styles.title}>免费领保障</View> */}
      <View className="flex">
        {renderContent()}
      </View>
    </View>
  );
}

export default ActivityBanner;
