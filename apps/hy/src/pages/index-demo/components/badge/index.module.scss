.remark-area {
  &-red {
    position: absolute;
    width: 10px;
    height: 10px;
    border-radius: 100%;
    background-color: #ff3100;
    top: -5px;
    right: -5px;
    z-index: 10;
  }

  &-hot {
    position: absolute;
    width: 24px;
    height: 28px;
    top: -18px;
    right: -14px;
    z-index: 10;
    background: url('https://cdn-health.zhongan.com/magiccube/resource/rl/lExd_Z3veG.png')
      left top/contain no-repeat;
  }

  &-custom {
    position: absolute;
    max-width: 90px;
    padding: 0 6px;
    height: 26px;
    font-size: 20px;
    line-height: 26px;
    text-align: center;
    background: linear-gradient(270deg, #ff5600 0%, #ffb050 100%, #ffb050 100%);
    border-radius: 2px;
    top: -18px;
    left: 100%;
    z-index: 10;
    transform: skew(-15deg) translateX(-10px);
    word-break: keep-all;
    .text {
      color: #fff;
      font-weight: bold;
    }
  }
}
