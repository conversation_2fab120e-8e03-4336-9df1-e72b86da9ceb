import React from 'react';
import { View, Text } from '@tarojs/components';
import classNames from 'classnames';
import styles from './index.module.scss';

type ItemType = string | number | null;

interface NavigationType {
  navigationTypeCode?: ItemType;   // 导航栏样式  。1-热点  2-红点  3-自定义
  navigationTypeDesc?: ItemType;   // 导航栏样式描述：热点，红点，自定义
  navigationCustomDesc?: ItemType; // 自定义导航栏描述
}

interface RemarkData {
  data: NavigationType;
}

function Badge(props: RemarkData) {
  const { data: { navigationTypeCode, navigationCustomDesc } = {} } = props;
  const isShowCustomText = !!(navigationTypeCode === 3 && navigationCustomDesc);
  const remarkCls = classNames(styles['sign-days-item'],
    {
      [styles['remark-area-red']]: navigationTypeCode === 2,
      [styles['remark-area-hot']]: navigationTypeCode === 1,
      [styles['remark-area-custom']]: isShowCustomText,
    });

  return (
    <View className={remarkCls}>
      {isShowCustomText && <View className={styles.text}>{navigationCustomDesc}</View>}
    </View>
  );
}

export default Badge;
