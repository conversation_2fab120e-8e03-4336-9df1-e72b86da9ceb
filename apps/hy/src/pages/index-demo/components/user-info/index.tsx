import React, { useEffect, useState } from 'react';
import { useSubscribeWxMessage } from '@/context/subscribe-mask';
import { useAccount, useUser } from '@/hooks';
import { View, Text } from '@tarojs/components';
import { POLICY_URL } from '@/utils/sites';
import Passport from '@/components/passport';
import Events from '@/track';
import { useDidShow } from '@tarojs/taro';
import NumberScroll from '@/components/number-scroll';
import { eventsBus } from '@/utils/events';
import usePageModal from '@/hooks/usePageModal';
import CustomerService from '../customer-service';
import SignActivity from '../sign-activity';

const isWeChat = process.env.TARO_ENV === 'weapp';

function UserInfo() {
  const { data: user, refetch, isLoading: userLoading } = useUser();
  const { data: account, isLoading: accountLoading } = useAccount();
  const subscribeWxMessage = useSubscribeWxMessage();
  const [onPageHide, setOnPageHide] = useState(false);

  const { isLoading, show } = usePageModal();

  useDidShow(() => {
    refetch();
  });

  useEffect(() => {
    const pageHide = () => {
      setOnPageHide(true);
    };
    eventsBus.on('PAGE_MODAL_HIDE', pageHide);
    return () => {
      eventsBus.off('PAGE_MODAL_HIDE', pageHide);
    };
  }, []);

  const { phone = '' } = user || {};
  const { policyNum = 0 } = account || {};

  // if (userLoading || accountLoading) {
  //   return null;
  // }

  const renderDescription = () => {
    if (!phone) {
      return '登录查看保障';
    }

    if (!policyNum) {
      return '您暂未开启保障';
    }

    if (policyNum || (!isLoading && !show)) {
      const count = onPageHide || (!isLoading && !show) ? policyNum : 0;
      return (
        <View className="flex-1 flex align-center policy">
          { count === 0 ? <Text className="color-F35200">{count}</Text> : <NumberScroll number={count} /> }
          <Text className="pl-10">份保单</Text>
        </View>
      );
    }
    return null;
    // return policyNum ? <View><Text className="color-F35200 pl-10 pr-10 fw-600">{policyNum}</Text>份保单 </View> : '您暂未开启保障';
  };

  return (
    <>
      <View className="flex-1 pr-30 relative">
        <View className="fw-500 color-999 mb-10 text-26">{ phone ? <><View className="policy-icon w-40 h-40 inline-block va--10 mr-10" />我的家庭保单</> : '未登录'}</View>
        <Passport
          scene="首页-我的保单"
          url={POLICY_URL}
          urlType="MiniProgramWebView"
          webViewNeedLogin="1"
          webViewShareLink="0"
          popup={isWeChat}
          onClick={(arg) => {
            Events.click({ CommonClick: 'xcx_sy_bd', custom_event_description: '首页保单入口' });
            isWeChat ? subscribeWxMessage('insure', arg) : arg?.();
          }}
        >
          <View className="fw-600 color-333 text-32 relative flex align-center space-between pl-10">
            {renderDescription()}
            <View className="arrow-right arrow-right--gray" />
          </View>
        </Passport>
      </View>
      {isWeChat ? <SignActivity phone={phone} /> : <CustomerService />}
    </>
  );
}

export default React.memo(UserInfo);
