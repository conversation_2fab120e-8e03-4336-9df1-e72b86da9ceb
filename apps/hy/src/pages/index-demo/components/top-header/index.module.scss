.header {
  width: 100%;
  top: 0;
  left: 0;
  z-index: 1111;
  text-align: center;
  position: fixed;
  background-color: transparent;
  color: #fff;
  font-weight: 600;
}

.default {
  width: 750px;
  background: linear-gradient(180deg, #D7F5ED 0%, #EBF4F3 70%, rgba(249,249,249,0) 100%);
  background-size: 750px 456px;
  background-repeat: no-repeat;

  .header {
    color: #333;
  }
  // height: 543px;
}

.icon {
  background-image: url('https://cdn-health.zhongan.com/hic/library/resource/6358a400659445001c0eda0d/8qD24xALzb.png');
  background-size: 123px 148px;
  position: absolute;
  right: 80px;
  top: 100px;
  width: 123px;
  height: 148px;
}

.search {
  // width: 340px;
  height: 54px;
  display: flex;
  background: #fff;
  position: relative;
  margin-right: 50px;
  flex: 1;

  // &-icon {
  //   background-image: url("https://cdn-health.zhongan.com/hic/library/resource/6358a400659445001c0eda0d/MsSWOnrfUn.png");
  //   background-size: 30px 30px;
  //   width: 30px;
  //   height: 30px;
  //   position: absolute;
  //   left: 15px;
  //   top: 12px;
  // }

  &-btn {
    position: absolute;
    background-color: #00BC70;
    font-weight: 600;
    right: 6px;
    top: 4px;
    height: 46px;
    line-height: 46px;
    font-size: 26px;
    padding: 0 20px;
    border-radius: 24px;
    color: #fff;
    z-index: 22;
  }
}

:global(.user-info) {
  background-image: url('https://cdn-health.zhongan.com/hic/library/resource/6358a400659445001c0eda0d/eT8YpVsG1S.png');
  background-size: 710px 123px;
  :global(.arrow-right) {
    &::after {
      top: 3px;
    }
  }
}

// :global(.w-auto) {
//   width: auto !important;
// }
