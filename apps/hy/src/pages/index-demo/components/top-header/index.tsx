import React, { useState, useMemo, useRef, useEffect } from 'react';
import Taro, { usePageScroll } from '@tarojs/taro';
import { View, Image, Swiper, SwiperItem, Input } from '@tarojs/components';
import { bannerLinkTo, navigateTo, safeURL } from '@/utils';
import classnames from 'classnames';
import useRecommendConfig from '@/hooks/useRecommendConfig';
import useSystemInfo from '@/hooks/useSystemInfo';
import useSearch from '@/hooks/useSearch';
import track from '@/track';
import QuickEnter from '../quick-enter';
import UserInfo from '../user-info';

import styles from './index.module.scss';

interface INavInfo {
  search: React.CSSProperties;
  header: React.CSSProperties;
}

const { TARO_ENV } = process.env;
function TopHeader() {
  const { data: recommendConfig } = useRecommendConfig();

  const banner = recommendConfig?.home_page_head;

  const systemInfo = useSystemInfo();

  const barOffset = systemInfo.navBarHeight + systemInfo.contentTop;
  const initNavInfo = useMemo(() => ({
    header: {
      height: `${barOffset}px`,
    },
    search: {
      border: `${1 * systemInfo?.pixelRatio}rpx solid transparent`,
      borderRadius: `${(1 * systemInfo?.pixelRatio * 2 + 60)}rpx`,
    },
  }), [barOffset]);

  const offsetNavInfo = useMemo(() => ({
    header: {
      ...initNavInfo.header,
      backgroundColor: '#fff',
      color: '#333',
      animationName: 'fadeIn',
      animationDuration: '1s',
    },
    search: {
      border: `${1 * systemInfo?.pixelRatio}rpx solid #00BC70`,
      borderRadius: `${(1 * systemInfo?.pixelRatio * 2 + 60)}rpx`,
    },
  }), [initNavInfo]);
  const [topNav, setTopNav] = useState<INavInfo>(initNavInfo);

  usePageScroll((res) => {
    if (res.scrollTop > 10) {
      setTopNav(offsetNavInfo);
    } else {
      setTopNav(initNavInfo);
    }
  });

  useEffect(() => {
    if (banner?.length) {
      const [current] = banner;
      track.click({
        CommonClick: '广告位曝光',
        extra_infomation: current?.bannerLocation,
        extra_infomation1: current?.bannerName,
      });
    }
  }, [banner]);

  const { onChange, searchKeywords, goToSearch } = useSearch('首页');

  const renderNav = () => {
    return (
      <>
        <View className={styles.header} style={topNav.header}>
          <View className="flex align-center" style={{ paddingTop: systemInfo.contentTop, height: `${systemInfo.navBarHeight}px`, marginRight: systemInfo.menu.width }}>
            <View className="ph-20 text-34">众安健康</View>
            {/* <SearchInput position="home" className={styles.search} style={topNav.search} /> */}
            <View
              className={styles.search}
              style={topNav.search}
            >
              <View
                onClick={() => {
                  goToSearch('placeholder');
                }}
              >
                <View className="search-icon l-15 t-12" />
                <Swiper className="h-54 w-180 ml-40" vertical autoplay circular interval={3000} onChange={(e) => { onChange(e.detail.current); }}>
                  {
                    searchKeywords?.map((i, idx) => <SwiperItem className="color-999 h-54 lh-54 fw-400 text-26 w-auto pl-20 text-left" key={+idx}>{i.key}</SwiperItem>)
                  }
                </Swiper>
              </View>
              <View
                className={styles['search-btn']}
                onClick={() => {
                  goToSearch('defaultValue');
                }}
              >搜索
              </View>
            </View>
          </View>
        </View>
        { banner?.length ? null : <View style={{ height: `${barOffset}px`, width: '100%' }} />}
      </>
    );
  };

  const renderContent = () => {
    if (banner?.length) {
      return (
        <View className="relative wp-100 h-820">
          <Swiper autoplay circular className="h-730 w-750">
            {
            banner?.map((item, idx) => {
              return (
                <SwiperItem key={+idx}>
                  <Image
                    className="h-730 w-750"
                    mode="widthFix"
                    src={safeURL(item.pictureUrl)}
                    onClick={() => bannerLinkTo(item)}
                    lazyLoad
                    data-ilog={{
                      CommonClick: '广告位点击',
                      extra_infomation: item?.bannerLocation,
                      extra_infomation1: item?.bannerName,
                    }}
                  />
                </SwiperItem>
              );
            })
          }
          </Swiper>
          <View className="user-info w-710 h-123 bg-fff b-rd-14 flex ph-30 pv-15 border-box mt-20 mh-20" style={{ position: 'absolute', top: `${barOffset}px` }}>
            <UserInfo />
          </View>
          <View style={{ position: 'absolute', bottom: 0, left: '20rpx' }}>
            <QuickEnter className="bg-fff" />
          </View>
        </View>
      );
    }
    return (
      <>
        <View className={styles.icon} />
        <View className="user-info w-710 h-123 b-rd-14 flex ph-30 pv-15 border-box mt-20 mh-20 zIndex-11">
          <UserInfo />
        </View>
        <QuickEnter />
      </>
    );
  };

  const cls = classnames({
    [styles.default]: !banner?.length,
  });

  return (
    <View className={cls} style={{ position: 'relative' }}>
      {TARO_ENV !== 'alipay' ? renderNav() : null}
      {renderContent()}
    </View>
  );
}

export default React.memo(TopHeader);
