import React from 'react';
import { View } from '@tarojs/components';
import Skeleton from '@/components/skeleton';

export default () => {
  const items = Array.from(new Array(4).values());
  return (
    <>
      {
        items?.map((item, index) => (
          <View className="inline-block text-center ml-15 mr-60 mb-15" key={+index}>
            <Skeleton.View className="h-72 w-72" />
            <Skeleton.View className="w-72 h-20 mt-17" />
          </View>
        ))
      }
    </>
  );
};
