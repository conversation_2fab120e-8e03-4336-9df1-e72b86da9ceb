import React, { useState } from 'react';
import { Image, View, Text, ScrollView } from '@tarojs/components';
import { navigateTo } from '@/utils/navigate';
import subscribe from '@/utils/subscribe';
import useConfig from '@/hooks/useConfig';
import Skeleton from './skeleton';

import styles from './index.module.scss';

function QuckEnter(props) {
  const { data: config = { home_nav_icons: [] }, isLoading } = useConfig();
  const { className = '' } = props;
  const [progress, setProgress] = useState(0);

  const { home_nav_icons: nav = [] } = config;

  const goTo = (item) => {
    // setStorageSync('PRODUCT_ANCHOR', item?.navigateOptions?.switchTabParams);
    if (item?.subscribeId) {
      subscribe([item?.subscribeId])?.then(() => {
        navigateTo({
          ...item,
          url: `${item.url}?`,
        });
      });
    } else {
      navigateTo({
        ...item,
        url: item.url,
      });
    }
  };

  const renderContent = () => {
    if (isLoading) {
      return <Skeleton />;
    }
    return (
      <>
        {
          nav?.map((item, index) => (
            <View className="inline-block text-center ml-15 mr-60 mb-15" onClick={() => goTo(item)} data-ilog={{ CommonClick: 'xcx_sy_sydh', control_index: index + 1, custom_event_description: '首页导航' }} key={+index}>
              <Image src={item.image} className="h-72 w-72" lazyLoad />
              <Text className="color-666 text-26 block">{item.title}</Text>
            </View>
          ))
        }
      </>
    );
  };

  if (nav?.length < 1 && !isLoading) {
    return null;
  }

  const computeProgress = (res) => {
    const { detail } = res;
    const { scrollWidth, scrollLeft } = detail;
    let left = ((scrollLeft * 2) / (scrollWidth / 2)) * 32;
    left = Math.min(Math.max(left, 0), 28);
    setProgress(left);
  };

  return (
    <View className={`b-rd-14 mt-20 mh-auto w-710 border-box pt-20 pl-20 white-space-nowrap pb-15 ${className}`}>
      <ScrollView scrollX onScroll={(res) => computeProgress(res)} enhanced className="mb-10">
        {
          renderContent()
        }
      </ScrollView>
      <View className={styles.progress}>
        <View className={styles['progress-inner']} style={{ left: `${progress}rpx` }} />
      </View>
    </View>
  );
}

export default QuckEnter;
