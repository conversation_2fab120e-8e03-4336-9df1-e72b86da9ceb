import React, { useMemo } from 'react';
import { View, Text, Button, Image } from '@tarojs/components';
import Product from '@/components/product';
import useRecommendProduct from '@/hooks/useRecommendProduct';
import ProductSkeleton from '@/components/product-skeleton';
import track from '@/track';
import { Tabs } from '@/components/tabs';
import Taro from '@tarojs/taro';
import { navigateToLink } from '@/utils';
import useLiveRoomStatus, { getRoomStatus } from '@/hooks/useLiveRoomStatus';
import classNames from 'classnames';
import styles from './index.module.scss';

const tabsMap: {[key: string]: { type: string; title: string }} = {
  1: {
    type: '1',
    title: '为自己',
  },
  3: {
    type: '3',
    title: '为配偶',
  },
  2: {
    type: '2',
    title: '为父母',
  },
  4: {
    type: '4',
    title: '为子女',
  },
};

function Recommend() {
  const [currentTab, setCurrentTab] = React.useState(0);
  const { data: recommendProduct, isLoading } = useRecommendProduct();
  const { data: liveRoomStatus } = useLiveRoomStatus();

  const tabsItems = useMemo(() => {
    return recommendProduct?.map((item) => {
      return {
        value: item.crowd,
        title: tabsMap[item.crowd].title,
      };
    });
  }, [recommendProduct]);

  const product = useMemo(() => {
    const currentCrowd = tabsItems?.[currentTab]?.value;
    const items = recommendProduct?.find((item) => item.crowd === currentCrowd) as any;
    return items?.productList?.slice(0, 5);
  }, [recommendProduct, currentTab, tabsItems]);

  if (!isLoading && (!recommendProduct || recommendProduct?.length < 1)) {
    return null;
  }

  const renderContent = () => {
    if (isLoading) {
      return <View className="product-items"><ProductSkeleton count={3} /></View>;
    }
    return (
      <View className="product-items">
        {
        product?.map((item, index) => (
          <View
            className={styles['product-item']}
            key={item.id || +index}
          >
            { getRoomStatus(item?.webcastRoomId, liveRoomStatus)
              ? (
                <>
                  <View className={styles['live-header']}>
                    <Image src="https://cdn-health.zhongan.com/hic/library/resource/6358a400659445001c0eda0d/Jwv0BqnoDT.gif" className="w-20 h-20 mr-5" />
                    直播中
                  </View>
                  <Button
                    openType="openWebcastRoom"
                    dataAwemeId={getRoomStatus(item?.webcastRoomId, liveRoomStatus)!}
                    className={styles.live}
                  />
                  <View className={styles['live-footer']}>
                    进入直播间
                    <View className="arrow-right ml-5" style={{ '--width': '5px', '--height': '5px' }} />
                  </View>
                </>
              )
              : null}
            <Product.Item
              data-ilog={{ CommonClick: '首页-众安诚意推荐-产品-点击', extra_infomation2: item.goodsLink, extra_infomation1: index, extra_infomation: tabsItems?.[currentTab]?.title }}
              product={item}
              index={index}
              showLabel={false}
              showArrow={false}
              className={classNames({ 'top-icon': liveRoomStatus?.[item?.webcastRoomId] !== 'LIVING', [`top-${index}`]: liveRoomStatus?.[item?.webcastRoomId] !== 'LIVING' }, styles['product-item-content'])}
            />
            {
              item.recommendReason
                ? (
                  <View
                    className={styles['product-reason']}
                    onClick={() => {
                      if (item.recommendReasonUrl) {
                        navigateToLink(item.recommendReasonUrl);
                      }
                    }}
                  >
                    <Text>推荐理由：</Text>{item.recommendReason}
                    {item?.recommendReasonUrl ? <View className={styles.arrow} /> : <View className="w-33 h-33" /> }
                  </View>
                )
                : null
            }

          </View>
        ))
      }
      </View>
    );
  };

  return (
    <View className="mr-20 ml-20 bg-fff p-30 b-rd-14 mt-20">
      <View className="fw-500 text-32 mb-20 flex space-between">
        {process.env.TARO_ENV === 'alipay' ? '爱邦' : '众安'}诚意推荐
        <View
          onClick={() => {
            Taro.switchTab({ url: '/pages/productList/index' });
            track.click('首页-众安诚意推荐-更多-点击');
          }}
        >
          <Text className="pr-10 fw-normal text-28 color-999">更多</Text>
          <View className="arrow-right arrow-right--gray" />
        </View>
      </View>
      <Tabs
        tabList={tabsItems || []}
        current={currentTab}
        width="48rpx"
        onClick={(current) => {
          setCurrentTab(current);
          track.click('首页-众安诚意推荐-栏目-点击', { extra_infomation: tabsItems?.[current].title });
          // Events.click(tabsItems[current]?.ilog);
        }}
        className={styles['recommend-items']}
      />
      <View className={styles['product-items']}>
        { renderContent() }
      </View>
    </View>
  );
}

export default React.memo(Recommend);
