@mixin topIcon {
  content: '';
  width: 75px;
  height: 36px;
  // background-size: 75px 36px;
  position: absolute;
  left: -8px;
  top: 0;
}

.product {
  &-reason {
    height: 80px;
    line-height: 80px;
    // margin-top: 30px;
    border-radius: 8px;
    padding: 0 20px;
    font-size: 26px;
    color: #666;
    max-width: 630px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    background-image: url("https://cdn-health.zhongan.com/hic/library/resource/6358a400659445001c0eda0d/w51NR2YIRs.png");
    background-position: 30px 10px;
    background-repeat: no-repeat;
    background-size: 31px 24px;
    background-color: rgba(0,188,112,0.04);
    border: 1px solid rgba(0,188,112,0.3);
    display: flex;
    justify-content: space-between;
    align-items: center;

    text {
      font-weight: 600;
    }
  }

  &-item {
    margin-bottom: 40px;
    position: relative;
    -webkit-overflow-scrolling: touch;

    &:last-child {
      margin-bottom: 0;
    }

    &-content {
      margin-bottom: 20px;
    }
  }
}

.recommend-items {
  :global(.tabs__item) {
    margin-right: 0;
  }
}


.arrow {
  width: 33px;
  height: 33px;
  background-size: 33px;
  background-image: url('https://cdn-health.zhongan.com/hic/library/resource/6358a400659445001c0eda0d/USz1NZaIlB.png');
}

:global(.top-icon) {
  &::after  {
    @include topIcon;
    background-image: url('https://cdn-health.zhongan.com/hic/library/resource/6358a400659445001c0eda0d/9ufoLLwVLe.png');
    background-size: 375px 36px;
  }
}

:global(.top-0) {
  &::after {
    background-position: 0 0;
   // @include topIcon;
    // background-image: url('https://cdn-health.zhongan.com/magiccube/resource/rl/semtgwgOv4.png');
  }
}

:global(.top-1) {
 &::after {
   // @include topIcon;
   background-position: -75px 0;
  }
}

:global(.top-2) {
  &::after {
   // @include topIcon;
   background-position: -150px 0;
  }
}

:global(.top-3) {
  &::after {
   // @include topIcon;
   background-position: -225px 0;
  }
}

:global(.top-4) {
  &::after {
    background-position: -300px 0;
  }
}

.live {
  position: absolute;
  width: 150px;
  height: 150px;
  background-color: transparent;
  z-index: 1111;

  &-footer {
    position: absolute;
    top: 120px;
    width: 150px;
    left: 0;
    font-size: 18px;
    z-index: 1;
    text-align: center;
    height: 36px;
    line-height: 36px;
    background: linear-gradient(90deg, #FF983F 0%, #FF5023 100%);
    border-radius: 1px 0px 10px 10px;
    color: #fff;
  }

  &-header {
    width: 82px;
    height: 26px;
    background: linear-gradient(90deg, #FF983F 0%, #FF5023 100%);
    border-radius: 10px 0px 10px 0px;
    font-size: 15px;
    position: absolute;
    top:0;
    left: 0;
    z-index: 1;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
