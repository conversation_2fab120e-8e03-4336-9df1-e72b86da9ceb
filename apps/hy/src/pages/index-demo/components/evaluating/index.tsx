import React from 'react';
import { View } from '@tarojs/components';
import useConfig from '@/hooks/useConfig';
import Taro from '@tarojs/taro';

import styles from './index.module.scss';

function Evaluating() {
  const { data } = useConfig();
  const { home_evaluating: evaluatingEnterList = [] } = data || {};
  if (evaluatingEnterList.length < 1) {
    return null;
  }
  return (
    <View className={styles.evaluating}>
      <View className={styles.title}>保险产品评测</View>
      <View className={styles['evaluating-content']}>
        <View className={styles['evaluating-items']}>
          <View className={styles['evaluating-items-title']}>众安权威评测</View>
          {
            evaluatingEnterList?.map((item) => (
              <View key={item.evaluateUrl} className={styles['evaluating-item']} onClick={() => Taro.navigateTo({ url: item.evaluateUrl })}>
                {item.title}
                <View className={styles['icon-right']} />
              </View>
            ))
          }
        </View>
      </View>
    </View>
  );
}

export default Evaluating;
