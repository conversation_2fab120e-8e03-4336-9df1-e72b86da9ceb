import React from 'react';
import { View, Text, Image } from '@tarojs/components';
import { navigateTo } from '@/utils';
import { useHint } from '@/components/demo';
import useConfig from '@/hooks/useConfig';

function ActivityEnter() {
  const { data } = useConfig();
  const list = data?.home_group_product || [];
  const shouldHint = useHint();

  if (list.length < 1 || !shouldHint) {
    return null;
  }
  return (
    <View>
      {
        list?.map((group, index) => (
          <View className="mr-30 ml-30 bg-fff p-30 b-rd-14 mt-20" key={+index}>
            <View className="text-32 fw-500">
              {group.title}
              <Text className="color-00bc70">{group.subTitle}</Text>
            </View>
            <View className="mt-40">
              {
                group?.products?.map((item) => (
                  <View onClick={() => navigateTo(item)} className="flex align-center mt-30 bg-fff" key={item.title}>
                    <Image src={item.img} className="w-150 h-150" />
                    <View className="flex-1 ml-30">
                      <View className="text-30 fw-600 color-333 mt-10">{item.title}</View>
                      <View className="text-28 mt-10 color-666">{item.description}</View>
                      <View>
                        <Text className="color-ff2727">{item.before}</Text>
                        <Text className="text-36 color-ff2727">{item.money}</Text>
                        <Text className="color-ff2727">{item.after}</Text>
                        <Text className="color-999 ml-10 text-28">{item.unit}</Text>
                      </View>
                    </View>
                    <View className="w-30">
                      <View className="arrow-right arrow-right--gray" />
                    </View>
                  </View>
                ))
              }
            </View>
          </View>
        ))
      }
    </View>
  );
}

export default ActivityEnter;
