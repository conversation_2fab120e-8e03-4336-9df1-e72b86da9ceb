import React from 'react';
import { Image, Swiper, SwiperItem, View } from '@tarojs/components';
import { navigateToLink } from '@/utils';

import './index.scss';

function Claim(props) {
  const { data = [], className = '' } = props;
  if (!data?.length) {
    return null;
  }
  return (
    <View className="mr-20 ml-20 bg-fff p-30 b-rd-14 mt-20">
      <View className="fw-500 text-32 mb-20">理赔案例</View>
      <Swiper className={`h-910 ${className} swiper claim-swiper`} autoplay circular indicatorDots indicatorColor="#fff" indicatorActiveColor="#00BC70">
        {
        data?.map((b, idx) => (
          <SwiperItem key={+idx} className="h-910 w-632" onClick={() => navigateToLink(b.url)}>
            <Image src={b.img} className="h-910 w-632" />
          </SwiperItem>
        ))
      }
      </Swiper>
    </View>
  );
}

export default Claim;
