import React from 'react';
import { View } from '@tarojs/components';
import Skeleton from '@/components/skeleton';

import styles from './skeleton.module.scss';

export default () => {
  return (
    <>
      <View>
        <Skeleton.Row className={styles.row}>
          <Skeleton.Image className={styles.menu} />
          <Skeleton.Image className={styles.menu} />
          <Skeleton.Image className={styles.menu} />
        </Skeleton.Row>
      </View>
      <Skeleton.Image className={styles.image} />
    </>
  );
};
