import React, { useEffect, useMemo, useState } from 'react';
import { View, Swiper, SwiperItem, Image, ScrollView, Text, Button } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { bannerLinkTo, safeURL } from '@/utils';
import { Tabs } from '@/components/tabs';
import ValidateElement from '@/components/validate-element';
import track from '@/track';
import useRecommendConfig from '@/hooks/useRecommendConfig';
import useLiveRoomStatus, { getRoomStatus } from '@/hooks/useLiveRoomStatus';
import Skeleton from './skeleton';
import Badge from '../badge';

import styles from './index.module.scss';

function EverybodyBuy(props) {
  const { className = '' } = props;

  const { data, isLoading } = useRecommendConfig();
  const { data: liveRoomStatus } = useLiveRoomStatus();
  const list = useMemo(() => {
    return data?.home_page_everyone_is_buying?.slice?.(0, 6) || [];
  }, [data?.home_page_everyone_is_buying]);

  const [current, setCurrent] = useState(0);

  // const items = list?.filter((item) => item.title !== ' ' && item.title !== null);
  useEffect(() => {
    if (list?.length) {
      const info = list?.map((item) => item.navigationBar).join(',');
      track.click('首页-大家都在买-栏目-曝光', { extra_infomation: info });
    }
  }, [list]);

  if (!isLoading && list?.length < 1) {
    return null;
  }

  const renderTitle = (item) => {
    return (
      <>
        {item.navigationBar}
        <Badge data={item} />
      </>
    );
  };

  const tabList = list?.map((item) => {
    return { title: renderTitle(item) };
  });

  const renderContent = () => {
    if (isLoading) {
      return <Skeleton />;
    }

    return (
      <>
        <ValidateElement expression={list?.length > 1}>
          <View className="overflow-hidden">
            <ValidateElement expression={process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt'}>
              <View>
                <ValidateElement expression={tabList?.length > 0}>
                  <Tabs
                    tabList={tabList}
                    current={current}
                    onClick={(idx) => {
                      setCurrent(idx);
                      track.click('首页-大家都在买-栏目-点击', { extra_infomation: idx });
                    }}
                    scroll
                  />
                </ValidateElement>
                <View className="mt-30">
                  <Swiper className={styles['swiper-height']} nextMargin="30rpx" onChange={(e) => setCurrent(e.detail.current)} current={current} snapToEdge circular>
                    {
                  list?.map((item, idx) => (
                    <SwiperItem key={+idx} className={styles['swiper-height']}>
                      <View
                        className="inline-block w-590 h-329 overflow-y-hidden bg-590-329"
                        style={{ backgroundImage: `url(${safeURL(item.pictureUrl || item.img)})` }}
                        onClick={() => {
                          track.click('首页-大家都在买-产品-点击', { extra_infomation: idx });
                          bannerLinkTo(item);
                        }}
                      />
                      { getRoomStatus(item?.webcastRoomId, liveRoomStatus)
                        ? (
                          <View className={styles.live}>
                            <View className={styles['live-header']}>
                              <Image src="https://cdn-health.zhongan.com/hic/library/resource/6358a400659445001c0eda0d/Jwv0BqnoDT.gif" className="w-20 h-20 mr-5" />
                              直播中
                            </View>
                            <Button
                              openType="openWebcastRoom"
                              dataAwemeId={getRoomStatus(item?.webcastRoomId, liveRoomStatus)!}
                              className={styles['live-btn']}
                            />
                            <View className="text-17 ml-10">
                              进入直播间，查看产品讲解
                              <View className="arrow-right ml-10" style={{ '--width': '5px', '--height': '5px' }} />
                              <View className="arrow-right ml-3" style={{ '--width': '5px', '--height': '5px', opacity: '0.6' }} />
                            </View>
                          </View>
                        )
                        : null}
                    </SwiperItem>
                  ))
                }
                  </Swiper>
                  {/* <View className={styles.dots}>
                    {
                    data?.map((item, idx) => (<View className={`${styles.dot} ${idx === current ? activeDot : ''}`} />))
                  }
                  </View> */}
                </View>
              </View>
            </ValidateElement>
            <ValidateElement expression={process.env.TARO_ENV === 'alipay'}>
              <ScrollView className="overflow-hidden white-space-nowrap h-329" scrollX>
                {
                list?.map((item, idx) => (
                  <View className="inline-block w-590 h-329 overflow-y-hidden bg-590-329" key={+idx}>
                    <Image src={safeURL(item.pictureUrl || item.img)} mode="widthFix" onClick={() => bannerLinkTo(item)} />
                  </View>
                ))
              }
              </ScrollView>
            </ValidateElement>
          </View>
        </ValidateElement>
        <ValidateElement expression={list?.length === 1}>
          <View
            className="inline-block w-590 h-329 overflow-y-hidden bg-590-329"
            style={{ backgroundImage: `url(${safeURL(list[0].pictureUrl || list[0].img)})` }}
            onClick={() => {
              track.click('首页-大家都在买-产品-点击', { extra_infomation: 0 });
              bannerLinkTo(list[0]);
            }}
          />
        </ValidateElement>
      </>
    );
  };

  // const activeDot = styles['dot-acitve'];
  return (

    <View className={['mr-20 ml-20 bg-fff p-30 b-rd-14', className].filter(Boolean).join(' ')}>
      <View className="fw-500 text-32 mb-20 flex space-between">
        大家都在买
        <View
          onClick={() => {
            track.click('首页-大家都在买-更多-点击');
            Taro.switchTab({ url: '/pages/productList/index' });
          }}
        >
          <Text className="pr-10 fw-normal text-28 color-999">更多</Text>
          <View className="arrow-right arrow-right--gray mr-10" />
        </View>
      </View>
      {renderContent()}
    </View>
  );
}

export default EverybodyBuy;
