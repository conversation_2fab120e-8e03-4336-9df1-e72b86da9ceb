import React, { useMemo } from 'react';
import Passport from '@/components/passport';
import { View, Text } from '@tarojs/components';
import { useDidShow } from '@tarojs/taro';
import useSignActivity from '@/hooks/useSignActivity';

function SignActivity({ phone }) {
  const { data: signActivity, refetch: refetchSignActivity } = useSignActivity();

  useDidShow(() => {
    refetchSignActivity();
  });

  const amount = useMemo(() => {
    if (!signActivity?.result?.amount) {
      return '';
    }
    return signActivity?.result?.amount / 100;
  }, [signActivity?.result?.amount]);

  const sumAward = useMemo(() => {
    if (!signActivity?.result?.sumAward) {
      return '';
    }
    return signActivity?.result?.sumAward / 100;
  }, [signActivity?.result?.sumAward]);

  return (
    <View className="flex-1 pl-40 bd-l-1 bds-l-solid bdc-l-e6e6e6">
      <Passport
        url="/pages/activity/sign/index"
        urlType="MiniProgramPage"
        webViewNeedLogin="1"
        webViewShareLink="0"
        popup
      >
        <View className="fw-500 color-999 mb-10 text-26">
          <View className="activity-icon w-40 h-40 inline-block va--10 mr-10" />
          福利金<Text className="color-F35200 pl-10 pr-10 fw-600">{phone ? sumAward : null}</Text>
        </View>
        <View className="fw-600 color-333 text-32 relative flex align-center space-between pl-8">
          { (phone && signActivity?.result?.signInStatus) ? <View>立即查看</View> : <View>签到立领<Text className="color-F35200 pl-10 pr-10 fw-600">{amount}</Text></View>}
          <View className="arrow-right arrow-right--gray" />
        </View>
      </Passport>
    </View>
  );
}

export default React.memo(SignActivity);
