.box {
  background-image: url('https://cdn-health.zhongan.com/hic/library/resource/6358a400659445001c0eda0d/iCm1KeN9i1.png');
  background-size: 736px 376px;
  width: 736px;
  height: 376px;
  overflow: hidden;
  // margin-left: 10px;
  margin-bottom: 5px;
  margin-top: 5px;
  margin-left: auto;
  margin-right: auto;
}


.exchange {
  background-position: -5px top;
  background-image: url('https://cdn-health.zhongan.com/hic/library/resource/6358a400659445001c0eda0d/AiY2-ND3Uc.png');
  // margin-left: 5px;
}

.content {
  padding-top: 5px;
  overflow: hidden;
  padding-bottom: 30px;
  position: relative;
  // left: -10px;
  height: 250px;
}

// .items {
//   padding: 0 30px;
//   overflow: hidden;
//   width: 650px;
//   white-space: nowrap;
//   text-align: center;
//   margin: 0 auto;
// }

.filter {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  margin: 10px 0;

  &-item {
    flex: 1;
    // background: linear-gradient(270deg, #B7F3DD 0%, #EEFFF9 100%);
    // box-shadow: inset 1px 2px 2px 0px #FFFFFF;
    // border-radius: 14px 0px 0px 0px;
    font-size: 32px;
    font-weight: 600;
    color: #38705A;
    text-align: center;
    height: 80px;
    line-height: 90px;
  }

  .active {
    color: #333;
  }
}
