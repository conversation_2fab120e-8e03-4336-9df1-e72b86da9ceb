import React, { useEffect, useMemo, useState } from 'react';
import { View, Image, ScrollView } from '@tarojs/components';
import classNames from 'classnames';
import useRecommendConfig from '@/hooks/useRecommendConfig';

import { bannerLinkTo, safeURL } from '@/utils';
import track from '@/track';
import styles from './index.module.scss';

const menu = ['帮你选保险', '保险方案'];
const Insurance = () => {
  const [current, setCurrent] = useState(1);

  const { data: config } = useRecommendConfig();
  const scheme = config?.home_page_insurance_scheme_v2;
  const helper = config?.home_page_help_chose_insurance;

  const banner = useMemo(() => {
    return current === 1 ? scheme : helper;
  }, [scheme, helper, current]);

  useEffect(() => {
    if (banner?.length) {
      const info = banner?.map((item) => item.pictureName).join(',');
      track.click(`首页-${menu[current]}-曝光`, { extra_infomation: info });
    }
  }, [current, banner]);

  if (!banner?.length) {
    return null;
  }

  return (
    <View className={classNames(styles.box, { [styles.exchange]: current !== 0 })}>
      <View className={styles.filter}>
        {
          menu.map((i, idx) => (
            <View
              key={+idx}
              className={classNames(styles['filter-item'], { [styles.active]: idx === current })}
              onClick={() => {
                setCurrent(idx);
              }}
            >{i}
            </View>
          ))
        }
      </View>
      <View className={styles.content}>
        <ScrollView scrollX className="ph-30 overflow-hidden w-650 white-space-nowrap text-center mh-auto">
          {
            banner?.map((item, idx) => (
              item?.pictureUrl
                ? (
                  <View
                    key={+idx}
                    onClick={() => {
                      track.click(`首页-${menu[current]}-点击`, { extra_infomation: item.pictureUrl, extra_infomation1: item.pictureRedirectUrl });
                      bannerLinkTo(item);
                    }}
                    className="w-189 h-245 inline-block mh-10"
                  >
                    <Image src={safeURL(item.pictureUrl)} className="w-189 h-245" />
                  </View>
                )
                : null
            ))
          }
        </ScrollView>
      </View>
    </View>
  );
};

export default Insurance;
