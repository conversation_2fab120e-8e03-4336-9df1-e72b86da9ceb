import React from 'react';
import { View } from '@tarojs/components';
import { navigateToLink } from '@/utils';

function CustomerService() {
  return (
    <View className="flex-1 pl-40 bd-l-1 bds-l-solid bdc-l-e6e6e6">
      <View className="fw-500 color-999 mb-10 text-26">
        <View className="customer-service-icon w-40 h-40 inline-block va--10 mr-10" />
        为您答疑解惑
      </View>
      <View>
        <View
          className="fw-600 color-333 text-32 relative flex align-center space-between"
          onClick={() => navigateToLink('https://im2.zhongan.io/zaim/?accessId=1597715705790&tenantID=1514946886000043&color=green&themeID=ZAJK&channelId=jkxgzh&subject=众安健康微信公众号&biz=微信', {
            webViewNeedLogin: 1,
          })}
        >
          <View>在线客服</View>
          <View className="arrow-right arrow-right--gray" />
        </View>
      </View>
    </View>
  );
}

export default CustomerService;
