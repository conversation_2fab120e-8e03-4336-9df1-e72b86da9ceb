import React, { useEffect, useState } from 'react';
import { View } from '@tarojs/components';
import useArticleList from '@/hooks/useArticleList';
import Articles from '@/components/articles';
import Skeleton from './skeleton';

import styles from './index.module.scss';

function ArticleList() {
  const { data = {}, isLoading } = useArticleList();

  const articles = data?.result || [];

  if (!isLoading && articles?.length < 1) {
    return null;
  }
  return (
    <View className="mr-20 ml-20 bg-fff b-rd-14 mt-20 border-box">
      <View className="fw-500 text-32 flex pl-30 pr-30 pt-30 pb-20">
        买前必看
        <View className={styles.icon}>NEW!!!</View>
      </View>
      {
        isLoading
          ? <Skeleton />
          : (
            <Articles
              data={articles}
            />
          )
      }
    </View>
  );
}

export default ArticleList;
