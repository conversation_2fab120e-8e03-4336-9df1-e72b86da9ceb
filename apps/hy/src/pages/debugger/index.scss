.debug-pages {
  min-height: 100vh;
  background: #fff;
  padding: 80rpx 60rpx 0;
  &__cell {
    border-radius: 10rpx;
    border: 1rpx solid #e5e5e5;
    padding: 15rpx 30rpx;
    color: #333;
    font-size: 28rpx;
    position: relative;
    margin-bottom: 30rpx;
    .placeholder {
      color: #cfcfcf;
    }
    &.picker {
      &::before,
      &:after {
        position: absolute;
        content: '';
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 12rpx 0 12rpx 15rpx;
        border-color: transparent transparent transparent #929292;
        right: 14rpx;
        top: 50%;
        transform: translate(0, -50%);
      }
      &::before {
        border-color: transparent transparent transparent #fff;
        z-index: 2;
        right: 16rpx;
      }
    }
    .textarea {
      width: 100%;
      height: 300rpx;
      color: #333;
      font-size: 28rpx;
    }
  }
  .button {
    width: 100%;
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    margin: 0 auto 22rpx;
    background-size: 100%;
    font-size: 34rpx;
    color: #ffffff;
    background-color: #00bc70;
    border-radius: 44rpx;
    &.clear-cache {
      background-color: #ff4d4f;
    }
    &.ghost {
      background-color: transparent;
      border: 1rpx solid #00bc70;
      color: #00bc70;
    }
    & + .button {
      margin-left: 30rpx;
    }
  }
  &__env,
  &__msg {
    padding-bottom: 25rpx;
    color: #999;
    text-align: center;
    font-size: 26rpx;
  }
  &__env {
    text-align: left;
    text {
      color: #ff4d4f;
    }
    &.msg {
      color: #333;
    }
  }
}
