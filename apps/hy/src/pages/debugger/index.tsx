import { View, Text, Picker, Input, Textarea, Button } from '@tarojs/components';
import Taro, { useLoad } from '@tarojs/taro';
import React, { useState } from 'react';
import config from '@/utils/config';
import validate from '@/utils/validate';

import './index.scss';

const prefixCls = 'debug-pages';
const envList = [{
  value: 'dev',
  label: '测试环境'
}, {
  value: 'uat',
  label: '预发环境'
}, {
  value: 'prd',
  label: '线上环境'
}];

const DebugTool = () => {
  const [pickerIndex, setPickerIndex] = useState<number>(0);
  const [pickerValue, setPickerValue] = useState('');
  const [env, setEnv] = useState('');
  const [target, setTarget] = useState('');
  const [channelResourceCode, setChannelResourceCode] = useState('');

  const msgText = '清空缓存 或 切换环境后，请记得重启小程序哦 ^_^';

  const showToast = (toastPrefixes: string) => {
    Taro.showToast({
      icon: 'none',
      title: toastPrefixes + '，请重启小程序呀 ^_^'
    });
  }

  const handlePickerChange = (e: any) => {
    const newIndex = Number(e.detail.value || 0);
    const item = envList[newIndex];
    setPickerIndex(newIndex);
    setPickerValue(item.label);
    setEnv(item.value);
  };

  const handleChannelCodeChange = (e: any) => {
    setChannelResourceCode(e.detail.value);
  };

  const handleUrlChange = (e: any) => {
    setTarget(e.detail.value);
  };

  const handleReplaceEnv = () => {
    if (!env) {
      Taro.showToast({
        icon: 'none',
        title: '请选择环境'
      });
      return;
    };
    Taro.clearStorage();
    setTimeout(() => {
      const codeKey = `${env}_channelResourceCode`;
      Taro.setStorageSync('DEBUG_ENV', env);
      if (channelResourceCode) {
        Taro.setStorageSync(codeKey, channelResourceCode);
      }
      showToast(`切换 [ ${env} ] 成功`);
    }, 200);
  };

  const handleClearStorage = () => {
    Taro.clearStorage();
    showToast('清除成功');
  };

  const handleJumpToPage = () => {
    const isH5Url = /^https?:\/\/.+/i.test(target);
    const isAppletUrl = /^\/pages\/.+/i.test(target);
    if (!target || (!isH5Url && !isAppletUrl)) {
      Taro.showToast({
        icon: 'none',
        title: '请检查地址合法性'
      });
      return;
    };
    const url = isH5Url ? `/pages/webview/index?src=${encodeURIComponent(target)}` : target;
    const navAction = validate.isTabBarPage(url) ? 'switchTab' : 'navigateTo';
    Taro[navAction]({ url });
  };

  return (
    <View className={prefixCls}>
      <View className={`${prefixCls}__env`}>默认部署：<Text>线上环境</Text></View>
      <View className={`${prefixCls}__env`}>当前环境：{config.ENV}</View>
      <View className={`${prefixCls}__env msg`}>{msgText}</View>

      <Picker
        className={`${prefixCls}__cell picker`}
        value={pickerIndex}
        range={envList}
        rangeKey="label"
        onChange={handlePickerChange}
      >
        <View className={`picker ${pickerValue ? '' : 'placeholder'}`}>
          {pickerValue ? pickerValue : '请选择环境'}
        </View>
      </Picker>

      <View className={`${prefixCls}__cell`}>
        <Input
          className="input"
          placeholder="来源位Code，非必填"
          value={channelResourceCode}
          placeholderClass="placeholder"
          onInput={handleChannelCodeChange}
        />
      </View>

      <View className={`${prefixCls}__cell`}>
        <Textarea
          className="textarea"
          placeholder="请输入H5地址（http开头）或小程序原生地址（/pages/ 开头），也可带上相关参数"
          value={target}
          placeholderClass="placeholder"
          placeholderStyle="color:#cfcfcf;font-size:14px"
          maxlength={-1}
          onInput={handleUrlChange}
        />
      </View>

      <View className="user-info flex">
        <Button className="button" onClick={handleReplaceEnv}>切换环境</Button>
        <Button className="button ghost" onClick={handleJumpToPage}>仅浏览地址</Button>
      </View>

      <Button className="button clear-cache" onClick={handleClearStorage}>清空缓存</Button>
      <View className={`${prefixCls}__msg`}>{msgText}</View>
    </View>
  );
};

export default DebugTool;
