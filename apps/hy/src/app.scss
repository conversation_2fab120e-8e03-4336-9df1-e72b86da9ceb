@font-face {
  font-family: 'fzhzgbjw';
  src: url('https://cdn-qcloud.zhongan.com/common/font/fontSecond.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
page {
  position: relative;
  height: 100%;
  min-height: 100%;
  width: 100%;
  font-family: PingFangSC-Regular, PingFang SC;
}
view {
  box-sizing: border-box;
}
.container {
  height: 100%;
  background: linear-gradient(180deg, #ffffff 0%, #f5f7fe 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
}
::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}
.flex {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
}
.flex-x {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}
.flex-y {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
}
.flex-xy {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
}
.flex-justify {
  justify-content: space-between;
  -webkit-justify-content: space-between;
  -webkit-box-pack: justify;
}
.flex-column {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  flex-direction: column;
}
.flex-grow {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  flex-grow: 1;
  flex: 1;
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
button {
  margin: 0;
  padding: 0;
}
.custome_button {
  margin: 0;
  padding: 0;
  border: none;
  background: inherit;
  font-size: inherit;
  line-height: inherit;
  width: inherit;
  height: inherit;
  &:after {
    display: none;
  }
}

.custome_hover {
  opacity: 0.4;
}
/*公共的 骨架屏动画*/
@keyframes commonSkeletonLoading {
  0% {
    background-position: 100% 50%;
  }

  to {
    background-position: 0 50%;
  }
}
