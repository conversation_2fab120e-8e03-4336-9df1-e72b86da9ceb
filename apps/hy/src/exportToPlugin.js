module.exports = {
  whoami: 'hfe zunxiang',
  // eslint-disable-next-line object-shorthand
  wx: wx,
  getToken: () => {
    let env = 'prd';
    try {
      if (__wxConfig.envVersion !== 'release') {
        env = wx.getStorageSync('API_ENV');
      }
    } catch (e) {
      // todo
    }
    return wx.getStorageSync(`${env}-token`);
  },
  getEnv: () => {
    try {
      if (__wxConfig.envVersion !== 'release') {
        return wx.getStorageSync('API_ENV');
      }
    } catch (e) {
      // todo
    }
    return 'prd';
  },
};
