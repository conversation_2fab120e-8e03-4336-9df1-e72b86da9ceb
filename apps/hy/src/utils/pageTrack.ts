const app = getApp();

export const xflowPushEvent = (
  { event = 'click', eventTag, text = '', attrs = {}, attributes = '' },
  supplement = ''
) => {
  try {
    const extra = attributes
      ? { ZAHLWYY_CLICK_CONTENT: `${attributes}${supplement ? '_' + supplement : ''}` }
      : attrs;
    app.za.customTrack({
      event_type: event,
      event_value: eventTag,
      event_description: text,
      ZAHLWYY_CLICK_CONTENT: '首页_问一下',
      ...extra,
    });
  } catch (e) {
    console.log(e);
  }
};
