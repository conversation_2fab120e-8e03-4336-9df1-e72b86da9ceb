import Monitor from './monitor';


// export interface Options {
//   /**
//    *  六翼天使前端监控-接入管理 获取的 seraphId
//    */
//   seraphId: string;
//   /**
//    *  应用版本号
//    */
//   miniAppVersion: string;
//   /**
//    * test | pub | pre | prd
//    * 暂时所有环境的数据都会打到 prd, 暂未实现根据环境隔离查询
//    */
//   env?: string;
//   /**
//    *  是否启用采集, 默认 true, false 时 SDK 不会初始化
//    */
//   offCollect?: boolean;

//   /**
//    * 用户id, 登录后需要关联？todo
//    */
//   userId?: string;

//   /**
//    * 设备id
//    */
//   deviceId?: string;

//   /**
//    * 私有化部署或者子公司覆盖设置默认上报地址
//    */
//   SERAPH_URL?: string;

//   source?: string;

//   /**
//    * 截流上报时间间隔，单位ms，默认2000
//    */
//   queueInterval?: number;

//   /**
//    * 是否采集 js 错误, 默认 true
//    */
//   collectJsError?: boolean;

//   /**
//    * 是否采集 Promise 错误, 默认 true
//    */
//   collectPromiseError?: boolean;

//   /**
//    * 是否采集资源错误, 默认 true
//    */
//   collectResourceError?: boolean;

//   /**
//    * 是否采集请求调用及性能信息, 默认 true
//    */
//   collectRequestPref?: boolean;

//   /**
//    * 是否监听 vuew 的 errorHandler, 暂未实现!!
//    */
//   collectVueError?: boolean;

//   /**
//    * 是否采集性能信息, 默认 true
//    */
//   collectPerformance?: boolean;

//   /**
//    * 是否采集资源信息, 默认 true
//    */
//   collectResource?: boolean;

//   /**
//    * 是否采 WebVital 指标, 默认 true
//    */
//   collectWebVital?: boolean;

//   /**
//    * SDK 开发调试内部配置, 用户不需要设置 (test | prd)
//    */
//   sdkEnv?: boolean;

//   /**
//    * 是否开启调试日志, 默认 false
//    */
//   debug?: boolean;

//   /**
//    * 忽略上报的 url: 错误,资源,请求
//    * eg: ignoreUrls: [/.+?yourUrl.+/, "yourUrl"]
//    */
//   ignoreUrls?: Array<String | RegExp>;

//   /**
//    * 自定义属性, 每次上报都会携带并存储
//    * 也可以使用 setExtendsInfo 方法进行设置
//    */
//   extendsInfo?: any;

//   /**
//    * 每次上报会调用该函数, 可以自定义逻辑, 返回的对象会上报存储起来
//    */
//   getDynamic?: () => any;

//   /**
//    * middleware
//    * 返回 false 可以忽略上报该事件
//    * 也可修改事件内容等骚操作 :D
//    */
//   beforeSend?: (e: any) => boolean;

//   /**
//    * sdk初始化后的回调
//    */
//   afterInit?: () => any;
// }

function MonitorJS (options) {
  if (!global.__SERAPH_HAS_MONITOR__) {
    global.__SERAPH_MONITOR__ = {};
    if (!options.offCollect) {
      const m = new Monitor(options);
      global.__SERAPH_HAS_MONITOR__ = m;
      return m;
    }
  }
}

global.MonitorJS = MonitorJS;
export default MonitorJS;
