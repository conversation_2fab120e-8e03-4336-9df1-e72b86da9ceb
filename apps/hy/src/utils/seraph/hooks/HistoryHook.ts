import HistoryMonitor from '../base/historyMonitor'

export default class HistoryHook extends HistoryMonitor {
  constructor () {
    super()
    this.hookHistory()
  }

  hookHistory () {
    const that = this;
    let oldPage = Page;
    Page = function (e) {
      var t = e.onShow;
      e.onShow = function () {
        t && t.apply(this, arguments);
        try {
          let pages = wx.getCurrentPages();
          that.recordHistory({ url: pages[pages.length - 1] ? pages[pages.length - 1].route : '', type: 'onShow' });
        } catch (error) {
          console.error(error);
        }
      };
      return oldPage.apply(this, arguments);
    }
  }
}
