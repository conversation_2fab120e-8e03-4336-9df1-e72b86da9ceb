import RequestMonitor from '../base/requestMonitor'
import { MethodTypes } from '../base/baseConfig'
import utils from '../base/utils'

const WxXhrTypes = {
  request: 'request',
  downloadFile: 'downloadFile',
  uploadFile: 'uploadFile'
}

class RequestHook extends RequestMonitor {
  constructor ({ historyUtil, ignoreUrls }) {
    super({ historyUtil, ignoreUrls });
    this.hookRequest();
  }

  hookRequest () {
    const that = this;
    const hookMethods = Object.keys(WxXhrTypes);
    hookMethods.forEach((hook) => {
      //@ts-ignore
      const originRequest = wx[hook];
      Object.defineProperty(wx, hook, {
        writable: true,
        enumerable: true,
        configurable: true,
        value: function (...args) {
          // 拿到入参
          const options = args[0]
          const { url } = options
          let method = '';
          const currentUrl = encodeURIComponent(url)
          if (options.method) {
            method = options.method
          } else if (hook === WxXhrTypes.uploadFile) {
            method = MethodTypes.Post
          } else {
            method = MethodTypes.Get
          }

          const startTime = Date.now();
          // 成功回调
          const successHandler = function (res) {
            that.method = method;
            that.url = currentUrl;
            that.async = true;
            that.category = hook;

            const endTime = Date.now();
            that.endTime = endTime;
            that.cbTime = endTime - startTime;
            that.status = res.statusCode;
            that.msg = res.errMsg; // 成功失败状态都有errMsg
            that.record();
            if (utils.isFunction(options.success)) {
              return options.success(res)
            }
          }

          const _fail = options.fail;
          // 失败回调
          const failHandler = function (err) {
            that.method = method;
            that.url = currentUrl;
            that.async = true;
            that.category = hook;
            
            // 系统和网络层面的失败
            const endTime = Date.now();
            that.endTime = endTime;
            that.cbTime = endTime - startTime;
            that.msg = err.errMsg;
            that.status = 0;
            that.record();
            if (utils.isFunction(_fail)) {
              return _fail(err)
            }
          }
          const actOptions = {
            ...options,
            success: successHandler,
            fail: failHandler
          }
          return originRequest.call(this, actOptions)
        }
      })
    })
  }
}

export default RequestHook;
