export default {
  type (obj) {
    return Object.prototype.toString.call(obj).replace(/\[object\s|\]/g, '')
  },

  isObjectLike(value) {
    return typeof value === 'object' && value !== null;
  },

  isString (str) {
    const type = typeof str;
    return type === 'string' || (type === 'object' && str != null && !Array.isArray(str) && this.type(str) === 'String');
  },

  isNumber(value) {
    return typeof value === 'number' || (this.isObjectLike(value) && this.type(value) === 'Number');
  },

  isSymbol (val) {
    const type = typeof val;
    return type === 'symbol' || (type === 'object' && val != null && this.type(val) === 'Symbol');
  },

  isFunction (func) {
    return this.type(func) === 'Function'
  },

  isArray (list) {
    return this.type(list) === 'Array'
  },
  // 生成随机字符串
  randomID (len, radix) {
    const chars =
      '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split(
        ''
      )
    const uuid = []
    let i
    radix = radix || chars.length

    if (len) {
      // Compact form
      for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * radix)]
    } else {
      // rfc4122, version 4 form
      let r

      // rfc4122 requires these characters
      uuid[8] = uuid[13] = uuid[18] = uuid[23]
      uuid[14] = '4'

      // Fill in random data. At i==19 set the high bits of clock sequence as
      // per rfc4122, sec. 4.1.5
      for (i = 0; i < 36; i++) {
        if (!uuid[i]) {
          r = 0 | (Math.random() * 16)
          uuid[i] = chars[i == 19 ? (r & 0x3) | 0x8 : r]
        }
      }
    }

    return uuid.join('').toLowerCase()
  },
  /*
   * 是否为null
   * @param {String} str
   */
  isNull (str) {
    return str == undefined || str == '' || str == null
  },
  /**
   * 对象是否为空
   * @param {*} obj
   */
  objectIsNull (obj) {
    return this.jsonStringify(obj) === '{}'
  },

  /**
   * 是否是对象
   * @param {*} obj
   */
  isObject (obj) {
    return this.type(obj) === 'Object';
  },
  /**
   * 对象转成otel logs格式
   */
  formatToKeyValue (obj) {
    if(this.isObject(obj) && this.jsonStringify(obj) !== '{}') {
      return Object.keys(obj).map((key) => {
        const keyValue = this.isNumber(obj[key]) ? 'doubleValue' : 'stringValue';
        return {
          key: key,
          value: {
            [keyValue]: this.isNumber(obj[key]) ? obj[key] : this.toString(obj[key])
          }
        }
      })
    }
    return [];
  },
  /**
   * 获取扩展信息
   */
  getExtendsInfo () {
    try {
      const ret = {};
      let extendsInfo = global.__SERAPH_MONITOR__._M_EXTENDS_INFO_ || {};
      let dynamicParams;
      if (this.isFunction(global.__SERAPH_MONITOR__._getDynamic_)) {
        dynamicParams = global.__SERAPH_MONITOR__._getDynamic_();
      }
      // 判断动态方法返回的参数是否是对象
      if (this.isObject(dynamicParams)) {
        extendsInfo = { ...extendsInfo, ...dynamicParams };
      }
      // 遍历扩展信息，排除动态方法
      for (const key in extendsInfo) {
        if (!this.isFunction(extendsInfo[key])) {
          // 排除获取动态方法
          ret[key] = extendsInfo[key];
        }
      }
      return ret;
    } catch (error) {
      console.error('call getExtendsInfo error', error);
      return {};
    }
  },
  /**
   * 获取网络类型
   */
  async getNetworkType() {
    if(!wx.getNetworkType) {
      console.error('不支持getNetworkType方法');
      return "";
    }
    const res = await wx.getNetworkType().then(res => {
      const networkType = res.networkType || "";
      return networkType;
    }).catch(err => {
      console.error('Failed to get network type:', err);
      return "";
    });
    return res;
  },
  jsonStringify (data) {
    try {
      return JSON.stringify(data)
    } catch (error) {
      return '{}'
    }
  },
  toString (value) {
    if (value == null) {
      return '';
    }
    if (typeof value === 'string') {
      return value;
    }
    if (Array.isArray(value)) {
      return this.jsonStringify(value);
    }
    if (this.isSymbol(value)) {
      return value.toString();
    }
    const result = `${value}`;
    return result;
  },
  now () {
    if ('performance' in global) {
      return performance.now()
    }
    return -1
  },
  /**
   * 时间戳毫秒转成纳秒
   */
  timeUnixNanoFormat(t) {
    if(typeof t !== 'number') {
      return '';
    }
    return `${t}000000`;
  },
}
