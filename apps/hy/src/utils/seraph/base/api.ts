import { DEFAULT_URL } from './baseConfig'
import utils from './utils'
import Console from './console'
import { setStorageSync, getStorageSync } from './storage'
import { Serialize } from '../../serialization';

class API {

  url = DEFAULT_URL();

  report (data) {
    this.sendInfo(data);
  }

  getSessionId () {
    if (!global.__SERAPH_MONITOR__._SESSION_ID_) { global.__SERAPH_MONITOR__._SESSION_ID_ = utils.randomID() }
    return global.__SERAPH_MONITOR__._SESSION_ID_;
  }

  // 扩展一些固定数据 比如url 和 seraphId等
  async extendData (data) {
    const newWorkType = await utils.getNetworkType();
    let _extends = [];
    if (utils.isObject(utils.getExtendsInfo()) && utils.jsonStringify(utils.getExtendsInfo()) !== '{}') {
      // _data.extends = utils.getExtendsInfo();
      _extends = utils.formatToKeyValue(utils.getExtendsInfo());
    }

    const _data = {
      resourceLogs: [
        {
          resource: {
            attributes: [
              ..._extends,
              {
                key: "source",
                value: {
                  stringValue: utils.toString(global.__SERAPH_MONITOR__._SOURCE_),
                }
              },
              {
                key: "env",
                value: {
                  stringValue: utils.toString(global.__SERAPH_MONITOR__._ENV_),
                }
              },
              {
                key: "jsSdkVersion", // 写死，暂无法支持读取package.json
                value: {
                  stringValue:  "1.0.0",
                }
              },
              {
                key: "url",
                value: {
                  stringValue: this.getUrl(),
                }
              },
              {
                key: "seraphId",
                value: {
                  stringValue: utils.toString(global.__SERAPH_MONITOR__._SERAPHID_),
                }
              },
              {
                key: "sessionId",
                value: {
                  stringValue: this.getSessionId(),
                }
              },
              {
                key: "network",
                value: {
                  stringValue: newWorkType,
                }
              },
              {
                key: "deviceId",
                value: {
                  stringValue: this.getDeviceId(),
                }
              },
              {
                key: "userId",
                value: {
                  stringValue: this.getUserId(),
                }
              },
              {
                key: "miniAppVersion",
                value: {
                  stringValue: global.__SERAPH_MONITOR__._OPTIONS.miniAppVersion ? utils.toString(global.__SERAPH_MONITOR__._OPTIONS.miniAppVersion) : '1.0.0',
                }
              },
              {
                key: "reportId",
                value: {
                  stringValue: utils.randomID(),
                }
              },
            ]
          },
          scopeLogs: [{
            scope: {},
            logRecords: data
          }]
        }
      ]
    }
    Console('report data =', _data);
    return _data;
  }

  /**
   * 发送消息
   */
  async sendInfo (data) {
    try {
      if(utils.isArray(data) && data.length !== 0) {
        const sendData = await this.extendData(data);
        Console(utils.jsonStringify(sendData))
        wx.request({
          url: this.url,
          method: 'POST',
          header: {
            'content-type': 'application/json'
          },
          data: utils.jsonStringify(sendData),
          success: function () {
            
          },
          fail: (err) => {
            // 上报接口有问题会死循环
            console.error(`${this.url}发生错误:`, err)
          }
        })
      } else {
        console.error('数据发送格式有误', data)
      }
    } catch (error) {
      console.error(error)
    }
  }

  getUrl () {
    if(!wx.getCurrentPages) {
      console.error('不支持getCurrentPages方法');
      return "";
    }
    const pages = wx.getCurrentPages();
    const currentPage = pages[pages.length - 1] || {};
    // za_mp_url_query 是 xflow 往currentPage上注入的字段，用于记录当前页面的参数
    const pathParams = currentPage.za_mp_url_query || Serialize(currentPage.options || {});
    const pathUrl = currentPage.route + '?' + pathParams; 
    return pathUrl;
  }

  getDeviceId () {
    const localDid = getStorageSync('_seraph_device_id_');
    if (localDid) {
      return localDid;
    }
    const genDid = utils.randomID();
    setStorageSync('_seraph_device_id_', utils.randomID());
    return genDid;
  }

  getUserId () {
    const localUid = getStorageSync('_seraph_user_id_');
    if (localUid) {
      return localUid;
    } else {
      return "";
    }
  }
}

export default API;
