export const EventCategoryEnum = {
  PV: 'pv',
  PERFORMANCE: 'performance',
  RESOURCE_TIMING: 'resource_timing',
  DEVICE: 'device',
  CUSTOM_EVENT: 'custom_event',
}

export const ErrorCategoryEnum = {
  JS_ERROR: 'js_error',
  RESOURCE_ERROR: 'resource_error',
  PROMISE_ERROR: 'promise_error',
  CROSS_SCRIPT_ERROR: 'cross_srcipt_error',
  UNKNOW_ERROR: 'unknow_error'
}

// 枚举值固定，监控平台支持根据类型选择查询数据
export const ErrorLevelEnum = {
  ERROR: 'Error',
  WARN: 'Warning',
  INFO: 'Info'
}

/** 自定义事件级别，默认是 info, 可枚举项 debug | info | warn | error */
export const EventLevelEnum = {
  DEBUG: 'debug',
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error',
}

export const HttpTypeEnum = {
  REQUEST: 'request',
  DOWNLOADFILE: 'downloadFile',
  UPLOADFILE: 'uploadFile'
}

export const MethodTypes = {
  Get: 'GET',
  Post: 'POST',
  Put: 'PUT',
  Delete: 'DELETE'
}

export const DEFAULT_URL = () => {
  if (global.__SERAPH_MONITOR__.SERAPH_URL) {
    return global.__SERAPH_MONITOR__.SERAPH_URL
  }
  const env = global.__SERAPH_MONITOR__._SDK_ENV_ || ''
  return env && env === 'test' ? 'https://new-static-seraph-test.zhongan.io/v1/miniapp/logs' : 'https://static-seraph.zhongan.io/v1/miniapp/logs'
}
