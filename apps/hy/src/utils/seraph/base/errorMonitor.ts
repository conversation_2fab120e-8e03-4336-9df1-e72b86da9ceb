import { ErrorLevelEnum, ErrorCategoryEnum } from './baseConfig'
import BaseMonitor from './baseMonitor'
import TaskQueue from './taskQueue'
import HistoryHook from '../hooks/HistoryHook'
import utils from './utils'

/**
 * 监控基类
 */
class ErrorMonitor extends BaseMonitor {
  /**
   * 上报错误地址
   */
  constructor ({ historyUtil }) {
    super()
    this.historyUtil = historyUtil
    this.category = ErrorCategoryEnum.UNKNOW_ERROR // 错误类型
    this.level = ErrorLevelEnum.INFO // 错误等级
    this.msg = '' // 错误信息
    this.url = '' // 错误信息地址
    this.timestamp = undefined // 当前时间戳
  }

  /**
   * 记录错误信息
   */
  recordError () {
    try {
      if (!this.msg) {
        return
      }
      const errorInfo = this.handleErrorInfo()

      // 记录日志
      TaskQueue.add(errorInfo)
    } catch (error) {
      console.error(error)
    }
  }

  handleErrorInfo () {
    const url = this.historyUtil.getCurrentUrl();
    let reasonObj = {};
    try {
      if(!utils.objectIsNull(this.onUnhandledRejectionReason) && !utils.isArray(this.onUnhandledRejectionReason)) {
        reasonObj = this.onUnhandledRejectionReason;
      }
    } catch (error) {
      console.log(error)
    }
    const logRecord = {
      timeUnixNano: utils.timeUnixNanoFormat(this.timestamp),
      severityText: this.level, // 错误级别
      body: {
        stringValue: utils.isString(this.msg) ? this.msg : utils.toString(this.msg),
      },
      attributes: [
        ...utils.formatToKeyValue(reasonObj),
        {
          key: "category",
          value: {
            stringValue: this.category
          }
        },
        {
          key: "pvId",
          value: {
            stringValue: this.historyUtil.getCurrentPVID()
          }
        },
        {
          key: "url",
          value: {
            stringValue: url,
          }
        },
      ]
    }
    const recordInfo = {
      category: this.category,
      url,
      logRecord
    }
    return recordInfo;
  }
}
export default ErrorMonitor;
