import { EventLevelEnum, DEFAULT_URL } from './baseConfig'
import BaseMonitor from './baseMonitor'
import TaskQueue from './taskQueue'
import Console from './console'
import utils from './utils'

/**
 * 监控基类
 */
class CustomEventMonitor extends BaseMonitor {

  /**
   * 上报错误地址
   */
  constructor ({ historyUtil }) {
    super()
    this.historyUtil = historyUtil
    this.level = EventLevelEnum.INFO // 自定义事件级别
    this.name = '' // 自定义事件名称
    this.source = '' // 自定义事件值
    this.value = void 0 // 自定义事件值
    this.category = ''
  }

  recordCustomEvent () {
    try {
      if (!this.name) {
        console.warn('自定义事件名称不能缺失')
        return;
      }
      const info = this.handleInfo()

      Console('请求信息', info)
      // 记录日志
      TaskQueue.add(info)
    } catch (error) {
      console.error(error)
    }
  }

  /**
   * 处理错误信息
   */
  handleInfo () {
    const url = this.historyUtil.getCurrentUrl();
    const logRecord = {
      timeUnixNano: utils.timeUnixNanoFormat(this.timestamp),
      attributes: [
        {
          key: "category",
          value: {
            stringValue: this.category
          }
        },
        {
          key: "name",
          value: {
            stringValue: this.name
          }
        },
        {
          key: "source",
          value: {
            stringValue: this.source
          }
        },
        {
          key: "value",
          value: {
            doubleValue: this.value
          }
        },
        {
          key: "level",
          value: {
            stringValue: this.level
          }
        },
        {
          key: "pvId",
          value: {
            stringValue: this.historyUtil.getCurrentPVID()
          }
        },
        {
          key: "url",
          value: {
            stringValue: url,
          }
        },
      ]
    }
    const recordInfo = {
      category: this.category,
      url: url,
      logRecord
    }
    console.log('recordInfo', recordInfo)
    return recordInfo;
  }
}
export default CustomEventMonitor;
