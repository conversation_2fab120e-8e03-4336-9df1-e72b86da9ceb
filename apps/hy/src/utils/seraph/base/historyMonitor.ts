import BaseMonitor from './baseMonitor'
import TaskQueue from './taskQueue'
import { EventCategoryEnum } from './baseConfig'
import Console from './console'
import utils from './utils'

export default class HistoryMonitor extends BaseMonitor {

  constructor () {
    super();
    this.lastUrl = '';
    this.now = utils.now();
    this.histories = [];
  }

  // 获取当前pvid
  getCurrentPVID () {
    if (!this.histories.length) {
      return '';
    }
    return this.histories[0].pvId;
  }

  // 获取当前path
  getCurrentUrl () {
    if (!this.histories.length) {
      return '';
    }
    return this.histories[0].url;
  }

  // 根据timeOrigin获取当前history信息
  getHistoryByTimeOrigin (timeOrigin) {
    const idx = this.histories.findIndex((h) => h.timeOrigin > timeOrigin)
    if (idx === -1) {
      return this.histories[0]
    }
    return this.histories[idx + 1]
  }

  // 根据timeOrigin获取url信息
  getUrlByTimeOrigin (timeOrigin) {
    const idx = this.histories.findIndex((h) => h.timeOrigin > timeOrigin)
    if (idx === -1) {
      if (!this.histories.length) {
        return '';
      }
      return encodeURIComponent(this.histories[0].url)
    }
    if (!this.histories[idx + 1]) {
      return '';
    }
    return encodeURIComponent(this.histories[idx + 1].url || '')
  }

  recordHistory ({ url, type }) {
    try {
      const pvId = utils.randomID();
      this.histories.unshift({ url, timeOrigin: utils.now(), pvId });
      if (this.histories.length > 200) {
        this.histories.splice(100);
      }
      const historyInfo = this.handleHistoryInfo({ url, type, pvId });
      this.lastUrl = url;
      if (!historyInfo) {
        return false;
      }

      TaskQueue.add(historyInfo);
      Console(`PV: ${JSON.stringify(historyInfo)}`);
      return true;
    } catch (error) {
      console.error(error);
      return false;
    }
  }

  handleHistoryInfo ({ url, type, pvId }) {
    const category = EventCategoryEnum.PV;
    const timestamp = Date.now();
    const logRecord = {
      timeUnixNano: utils.timeUnixNanoFormat(timestamp),
      attributes: [
        {
          key: "category",
          value: {
            stringValue: category
          }
        },
        {
          key: "from",
          value: {
            stringValue: this.lastUrl
          }
        },
        {
          key: "to",
          value: {
            stringValue: url
          }
        },
        {
          key: "type",
          value: {
            stringValue: type
          }
        },
        {
          key: "timestamp",
          value: {
            doubleValue: timestamp,
          }
        },
        {
          key: "pvId",
          value: {
            stringValue: pvId
          }
        },
      ]
    }

    const recordInfo = {
      category,
      logRecord
    }
    return recordInfo;
  }
}
