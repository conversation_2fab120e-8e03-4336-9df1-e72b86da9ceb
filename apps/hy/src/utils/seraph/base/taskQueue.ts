import API from './api';

const TaskQueue = {
  queues: [],
  timer: null,
  intervalTime: 2000,

  add (data) {
    if (typeof global.__SERAPH_MONITOR__._OPTIONS.beforeSend === 'function') {
      if (!global.__SERAPH_MONITOR__._OPTIONS.beforeSend(data.logRecord)) {
        return false
      }
    }

    if (Array.isArray(global.__SERAPH_MONITOR__._OPTIONS.ignoreUrls)) {
      const urlProp = 'url';
      const ignoreUrls = global.__SERAPH_MONITOR__._OPTIONS.ignoreUrls;
      if (
        ignoreUrls.find((i) => {
          if (i instanceof RegExp) return i.test(data[urlProp])
          if (typeof i === 'string' && data[urlProp]) return data[urlProp].includes(i)
          return false
        })
      ) {
        return false
      }
    }

    this.queues.push(data.logRecord);
    this.fire();
  },

  reportQueue () {
    new API().report(this.queues);
    // 清空队列
    this.queues = [];
  },

  fire () {
    if (!this.queues || this.queues.length === 0) {
      return
    }
    // 队列长度超过20时上报一次
    if (this.queues.length > 20) {
      if (this.timer) clearInterval(this.timer)
      this.reportQueue();
    } else {
      // 做一次节流上传
      if (this.timer) clearInterval(this.timer);
      this.timer = setTimeout(() => {
        this.reportQueue();
      }, global.__SERAPH_MONITOR__._QUEUE_INTERVAL || this.intervalTime);
    }
  }
}

export default TaskQueue;
