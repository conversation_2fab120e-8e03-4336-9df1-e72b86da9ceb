import { ErrorLevelEnum, DEFAULT_URL } from './baseConfig'
import BaseMonitor from './baseMonitor'
import TaskQueue from './taskQueue'
import Console from './console'
import utils from './utils'
import HistoryHook from '../hooks/HistoryHook'

/**
 * 监控基类
 */
class RequestMonitor extends BaseMonitor {
  /**
   * 上报错误地址
   */
  constructor ({ historyUtil, ignoreUrls }) {
    super()
    this.historyUtil = historyUtil
    this.ignoreUrls = ignoreUrls || []
    this.level = ErrorLevelEnum.INFO // 错误等级
    this.msg = '' // 错误信息
    this.url = '' // 错误信息地址
    this.method = ''
    this.category = ''
    this.endTime = undefined
  }

  record () {
    try {
      const _U = encodeURIComponent(DEFAULT_URL())
      // 过滤掉错误上报地址
      if (
        (
          _U &&
          this.url &&
          this.url.toLowerCase().indexOf(_U.toLowerCase()) >= 0
        ) ||
        this.ignoreUrls.some(regexp => regexp.test(decodeURIComponent(this.url)))
      ) {
        // if (this.msg !== '') console.error('统计错误接口异常', this.msg) // 小程序接口正确返回也会有errMsg
        return
      }
      const info = this.handleInfo()

      Console('请求信息', info)
      // 记录日志
      TaskQueue.add(info)
    } catch (error) {
      console.error(error)
    }
  }

  /**
   * 处理错误信息
   */
  handleInfo () {
    const logRecord = {
      timeUnixNano: utils.timeUnixNanoFormat(this.endTime),
      body: {
        stringValue: utils.isString(this.msg) ? this.msg : utils.toString(this.msg),
      },
      attributes: [
        {
          key: "category",
          value: {
            stringValue: this.category
          }
        },
        {
          key: "method",
          value: {
            stringValue: this.method // 请求类型
          }
        },
        {
          key: "url",
          value: {
            stringValue: this.url // 错误信息地址
          }
        },
        {
          key: "cbTime",
          value: {
            doubleValue: this.cbTime // 执行时间
          }
        },
        {
          key: "async",
          value: {
            stringValue: utils.toString(this.async) // 是否异步
          }
        },
        {
          key: "status",
          value: {
            doubleValue: this.status // 请求状态码
          }
        },
        {
          key: "type",
          value: {
            stringValue: this.type
          }
        },
        {
          key: "pvId",
          value: {
            stringValue: this.historyUtil.getCurrentPVID()
          }
        }
      ]
    }
    const recordInfo = {
      category: this.category,
      url: this.url,
      logRecord
    }
    return recordInfo;
  }
}
export default RequestMonitor;
