import { EventCategoryEnum, EventLevelEnum } from './../base/baseConfig';
import CustomEventMonitor from "../base/customEventMonitor";

// export type ISendCustomEvent = {
//   name: string;
//   source: string;
//   value: number;
//   level?: EventLevelEnum;
// }

/**
 * @description 发送自定义事件
 */
class Handler extends CustomEventMonitor {
  constructor ({ historyUtil }) {
    super({ historyUtil })
  }

  sendCustomEvent(params) {
    if (!(params.level && Object.values(EventLevelEnum).includes(params.level))) {
      console.error('自定义事件级别level，默认是 info, 可枚举项 debug | info | warn | error ');
      return
    }
    this.level = params.level || EventLevelEnum.INFO
    this.name = params.name
    this.source = params.source
    this.value = params.value
    this.category = EventCategoryEnum.CUSTOM_EVENT
    this.timestamp = Date.now()

    this.recordCustomEvent();
  }
}

export default Handler