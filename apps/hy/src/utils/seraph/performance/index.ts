import { EventCategoryEnum } from '../base/baseConfig'
import TaskQueue from '../base/taskQueue'
import utils from '../base/utils'

class MonitorPerformance {
  constructor ({ historyUtil }) {
    this.historyUtil = historyUtil;
    this.record();
  }

  record () {
    if(!wx.getPerformance) {
      console.error('不支持getPerformance方法');
      return;
    }
    setTimeout(() => {
      try {
        const performance = wx.getPerformance();
        if(!performance.createObserver) return;
        const observer = performance.createObserver((entryList) => {
          if(!entryList.getEntries) return;
          this.handleEntryList(entryList.getEntries() || []).map((item) => {
            const e = item;
            return { ...e, pvId: this.historyUtil.getCurrentPVID(), category: EventCategoryEnum.PERFORMANCE };
          })
          .forEach((e) => {
            const entryListInfo = this.handleEntryListInfo(e);
            TaskQueue.add(entryListInfo);
          })
        });
        //@ts-ignore
        observer.observe({ entryTypes: ['render', 'script', 'navigation'] });
      } catch (error) {
        console.error(error);
      }
    }, 100)
  }

  handleEntryList (entryList) {
    const entryTimesList = [];
    if (!entryList || entryList.length == 0) {
      return [];
    }
    entryList.forEach((entity) => {
      entryTimesList.push(entity);
    })
    return entryTimesList;
  }

  handleEntryListInfo (info) {
    const timestamp = Date.now();
    const logRecord = {
      timeUnixNano: utils.timeUnixNanoFormat(timestamp),
      attributes: utils.formatToKeyValue(info)
    }
    const recordInfo = {
      category: info.category,
      logRecord
    }
    return recordInfo;
  }
}

export default MonitorPerformance;
