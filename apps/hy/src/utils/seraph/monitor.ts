import Console from './base/console';
import { JsError } from './error';
import RequestHook from './hooks/RequestHook';
import HistoryHook from './hooks/HistoryHook';
import CustomEventHandler from './customEvent';
import MonitorPerformance from './performance';
import { setStorageSync, removeStorageSync } from './base/storage';

class MonitorJS {
  constructor(options) {
    if (!options) {
      throw new Error('请传入 SDK 初始化参数');
    }

    global.__SERAPH_MONITOR__.__DEBUGGER__ = options.debug;

    global.__SERAPH_MONITOR__._OPTIONS = options;

    Console('SDK 初始化', global.__SERAPH_MONITOR__._OPTIONS);

    if (options.SERAPH_URL) {
      global.__SERAPH_MONITOR__.SERAPH_URL = options.SERAPH_URL;
    }
    if (options.deviceId) {
      setStorageSync('_seraph_device_id_', options.deviceId);
    }

    this.setUser({ userId: options.userId });

    // 保存全局数据
    global.__SERAPH_MONITOR__._SDK_ENV_ = options.sdkEnv || 'prd';
    // seraphId 注入全局
    global.__SERAPH_MONITOR__._SERAPHID_ = options.seraphId || '';
    // source注入全局
    global.__SERAPH_MONITOR__._SOURCE_ = options.source || 'miniapp';
    // env注入全局
    global.__SERAPH_MONITOR__._ENV_ = options.env || 'prd';
    global.__SERAPH_MONITOR__._QUEUE_INTERVAL = options.queueInterval;
    // 扩展信息
    global.__SERAPH_MONITOR__._M_EXTENDS_INFO_ = options.extendsInfo || {};
    global.__SERAPH_MONITOR__._getDynamic_ = options.getDynamic;

    this.init(options);

    if (typeof options.afterInit === 'function') {
      options.afterInit();
    }
  }

  /**
   * 初始化
   * @param {*} options
   */
  init(options) {
    options = options || {};

    this.historyHook = new HistoryHook();

    if (options.collectJsError !== false) {
      new JsError({ historyUtil: this.historyHook }).handleError();
    }
    if (options.collectRequestPref !== false) {
      new RequestHook({ historyUtil: this.historyHook, ignoreUrls: options.ignoreUrls });
    }

    if (options.collectPerformance !== false) {
      this.performanceMonitor = new MonitorPerformance({ historyUtil: this.historyHook });
    }

    this.customEventHandler = new CustomEventHandler({ historyUtil: this.historyHook });
  }

  /**
   * 增加自定义属性, 每次上报请求都会携带改对象的属性
   * @param infos 设置任意对象
   */
  setExtendsInfo(infos) {
    global.__SERAPH_MONITOR__._M_EXTENDS_INFO_ = infos || {};
  }

  /**
   * 自定义用户 id, 标识当前用户, 影响 uv 计算, 后台也可根据 userId 过滤查询
   * @param param {用户id}
   */
  setUser({ userId }) {
    if (userId) {
      setStorageSync('_seraph_user_id_', userId);
    } else {
      removeStorageSync('_seraph_user_id_');
    }
  }

  sendCustomEvent(params) {
    this.customEventHandler && this.customEventHandler.sendCustomEvent(params);
  }
}


export default MonitorJS;