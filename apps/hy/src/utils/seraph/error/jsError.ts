import ErrorMonitor from '../base/errorMonitor'
import { ErrorCategoryEnum, ErrorLevelEnum } from '../base/baseConfig'
import utils from '../base/utils'

/**
 * 捕获JS错误
 */
class JSError extends ErrorMonitor {
  constructor ({ historyUtil }) {
    super({ historyUtil })
  }

  /**
   * 注册onerror事件
   */
  handleError () {    
    const that = this;
    console.log('handleError', App)
    //@ts-ignore
    let originApp = App;
    //@ts-ignore
    App = function (appOptions) {
      console.log('appOptions', appOptions)
      let 
        r = appOptions.onUnhandledRejection,
        t = appOptions.onError;
      appOptions.onError = function () {
        t && t.apply(this, arguments);
        // console.log(arguments)
        try {
          that.level = ErrorLevelEnum.WARN;
          that.category = ErrorCategoryEnum.JS_ERROR;
          that.msg = arguments[0];
          that.timestamp = Date.now();
          that.recordError();
        } catch (error) {
          console.error('js错误解析异常: ', error)
        }
      };
      appOptions.onUnhandledRejection = function (event) {
        r && r.apply(this, arguments);
        if(!event || !event.reason) return;
        try {
            that.level = ErrorLevelEnum.WARN;
            that.category = ErrorCategoryEnum.PROMISE_ERROR;
            that.msg = utils.isObject(event.reason) ? (event.reason.errMsg || 'onUnhandledRejection错误') : event.reason;
            if(utils.isObject(event.reason)) {
              that.onUnhandledRejectionReason = event.reason;
            }
            that.timestamp = Date.now();
            that.recordError()
          } catch (error) {
            console.error('js错误解析异常: ', error)
          }
      };
      return originApp.apply(this, arguments); // todo arguments
    }
  }
}

export default JSError;
