import config from './config';
import storage from './storage';
import format from './format';
import { fetchJson } from './fetch'
import getSearch from "./getSearch";
import { getIsBlackUser } from "./getChannelInfo";
import { Deserialize } from './serialization';
import Taro from '@tarojs/taro';
import { globalData } from './global';


const webviewUrl = globalData.webviewUrl;

export function event(fn, authType = 'none') {
  return function (...rest) {
    const context = this;
    const { accountPhone = '' } = storage.get('userInfo');
    const realName = storage.get('realName') || '';
    context.timer && clearTimeout(context.timer);
    if (accountPhone == '' && (authType === 'phone' || authType === 'real')) {
      context._showModal && context._showModal();
    } else if (realName !== 'Y' && authType === 'real') {
      const src = encodeURIComponent(`${webviewUrl}/hospital/certification`);
      Taro.navigateTo({ url: `/pages/webview?src=${src}` })
    } else {
      // typeof arguments[0] === 'string' ? fn.call(context, arguments[0]) : fn.call(context, { ...arguments[0] });
      fn.apply(context,rest)
    }
  }
}

export function jump_gj() {
  const context = this;
  fetchJson({
    url: '/zaApi/v1/patient/gjmall/indexUrl',
    method: 'POST',
    needLogin: true,
    needLoading: true,
    data: {
      // policyNo: '******************',
    },
  }).then(res => {
    if (res.code === '0') {
      const { url = '', effectiveDate } = res.result;
      res.result.effectiveDate = effectiveDate ? format.date(effectiveDate, 'yyyy/MM/dd') : '';
      if (!url) {
        const pages = Taro.getCurrentPages();
        const currentPage = pages[pages.length - 1];
        const currentRoute = `/${currentPage.route}`;
        console.log(currentRoute);
        if (currentRoute !== '/pages/index') {
          Taro.switchTab({ url: '/pages/index' });
          return
        }
        context._showGJModal && context._showGJModal(res.result);
      } else {
        Taro.navigateTo({
          url: `./webview?src=${encodeURIComponent(res.result.url)}`,
        });
      }
    }
  });
}

export function registerUserAuth({ partnerCode, businessType, thirdOrderId = '' }) {
  fetchJson({
    url: `/zaApi/v1/patient/partner/user/auth/isUserAuth`,
    method: 'POST',
    data: {
      thirdPlatformCode: partnerCode,
      businessType,
      thirdOrderId,
    },
    needLogin: true,
    needLoading: true,
    success: (res) => {
      const { code, result } = res || {};
      if (code === '0') {
        const { appId, isUserAuth = '', jumpType, partnerServerUrl = '' } = result || {};
        let { token = ''} = result || {};
        if (isUserAuth === 'N') {
          getSearch().then((result) => {
            const search = format.serialize({ partnerCode, businessType, thirdOrderId, ...result });
            const src = encodeURIComponent(`${webviewUrl}/hospital/auth?${search}`);

            Taro.navigateTo({ url: `./webview?src=${src}` });
          });
        } else if (jumpType === 'applet') {
          let path = partnerServerUrl;

          if (token) {
            path += token;
          }

          Taro.navigateToMiniProgram({
            appId,
            path,
            envVersion: 'release',
            success(res) {
              // 打开成功
              console.log(res);
            },
            fail(error) {
              console.log(error);
              let title = '获取授权信息失败';
              try {
                if (error && (error.errMsg || '').indexOf('fail cancel') > -1) {
                  // title = '取消购药';
                  return;
                }
              } catch (e) {
                console.log(e);
              }
              Taro.showToast({
                title,
                icon: 'none',
                duration: 2000
              })
            }
          });
        } else {
          //上药云慢病里token存在待转义字符
          // if(partnerCode === 'SYY'){
          //   let { cardType, cardNumber, orderNumber} = Deserialize(token);
          //   console.log('cardType',cardType,cardNumber,orderNumber)
          //   cardType = encodeURIComponent(cardType);
          //   cardNumber = encodeURIComponent(cardNumber);
          //   orderNumber = encodeURIComponent(orderNumber);
          //   token = `&cardType=${cardType}&cardNumber=${cardNumber}&orderNumber=${orderNumber}`;
          // }
          const path = `${partnerServerUrl}${token}`;
          Taro.navigateTo({ url: `./webview?src=${encodeURIComponent(path)}` });


        }
      }
    },
  });
}

// 针对引导用户去加微信页的引导
export const jumpToWechatGuidance = (word = '') => {
  let isBlackUser = getIsBlackUser();
  if (isBlackUser) {
    const errorUrl = `?src=` + encodeURIComponent(`${webviewUrl}/hospital/error`) + `&param=${JSON.stringify({ "type": "black" })}`;
    Taro.navigateTo({
      url: `/pages/webview${errorUrl}`,
    });
    return;
  };
  Taro.navigateTo({ url: `/pages/guidance/index?word=${word}` });
}
