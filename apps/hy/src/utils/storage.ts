import config from './config';

class storage {
  constructor(props) {
    this.props = props || {};
    this.source = wx || this.props.source;
  }
  get(key) {
    key = `${config.ENV}_${key}`;
    const data = this.source,
      timeout = data.getStorageSync(`${key}__expires__`) || 0;
    if (timeout === 0) {
      const value = data.getStorageSync(key);
      return value;
    }
    // 过期失效
    if (Date.now() >= timeout) {
      this.remove(key);
      return;
    }
    const value = data.getStorageSync(key);
    return value;
  }

  // 设置缓存
  // timeout：过期时间（分钟）
  set(key, value, timeout) {
    key = `${config.ENV}_${key}`;
    let data = this.source;
    let _timeout = timeout || 0;
    data.setStorageSync(key, value);
    if (_timeout === 0) {
      return value;
    }
    data.setStorageSync(`${key}__expires__`, Date.now() + 1000 * 60 * _timeout);
    return value;
  }

  remove(key) {
    key = `${config.ENV}_${key}`;
    let data = this.source;
    data.removeStorageSync(key);
    data.removeStorageSync(`${key}__expires__`);
    return undefined;
  }
}
const _storage = new storage();

export const everyDayOnceExpiredStorage = (storageKey) => {
  if (!storageKey) {
    return;
  }
  // 凌晨 0 点过期
  const day = new Date();
  day.setDate(day.getDate() + 1);
  day.setHours(0, 0, 0, 0);
  const minutes = (day.getTime() - new Date().getTime()) / 1000 / 60;
  _storage.set(storageKey, 1, minutes);
};

export default _storage;

