
// Base64编码
import { Base64 } from 'js-base64';

/**
 * 自定义编码函数，去除Base64中的/+和=
 * @param {string} str 要编码的字符串
 * @return {string} 编码后的字符串
 */
function customEncode(str) {
  // 先进行Base64编码
  const base64 = Base64.encode(str);
  
  // 替换特殊字符
  return base64
    .replace(/\//g, '_') // 将/替换为_
    .replace(/\+/g, '-') // 将+替换为-
    .replace(/=/g, '');  // 去掉=
}

/**
 * 自定义解码函数
 * @param {string} str 编码后的字符串
 * @return {string} 解码后的原始字符串
 */
function customDecode(str) {
  // 还原特殊字符
  let base64 = str
    .replace(/_/g, '/') // 将_还原为/
    .replace(/-/g, '+'); // 将-还原为+
  
  // 添加等号补位
  const padLength = 4 - (base64.length % 4);
  if (padLength < 4) {
    base64 += '='.repeat(padLength);
  }
  
  // Base64解码
  return Base64.decode(base64);
}


/**
 * 编码函数，支持Unicode字符
 * @param {string} str 要编码的字符串
 * @return {string} 编码后的字符串
 */
function safeEncode(str) {
  // 先进行URL编码处理Unicode字符
  const encoded = encodeURIComponent(str);
  // 再进行自定义Base64编码
  return customEncode(encoded);
}

/**
 * 解码函数，支持Unicode字符
 * @param {string} str 编码后的字符串
 * @return {string} 解码后的原始字符串
 */
function safeDecode(str) {
  // 先进行自定义Base64解码
  const decoded = customDecode(str);
  // 再进行URL解码还原Unicode字符
  return decodeURIComponent(decoded);
}

export {
    safeDecode,
    safeEncode
}