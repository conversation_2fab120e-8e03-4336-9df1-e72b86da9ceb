import selfMiniIlog from './selfLog';
import storage, { everyDayOnceExpiredStorage } from './storage';
import config, { webviewUrl } from '@/utils/config'

export const JSONPARSE = (processParam, positionName) => {
  if (!processParam) {
    return {};
  }
  processParam = processParam.replace(/\'/g, '"');
  let tryData = {};
  try {
    tryData = processParam && JSON.parse(processParam);
  } catch (error) {
    selfMiniIlog.error({
      ...(error || {}),
      processParam,
      msg: `微信小程序${positionName}的JSON.parse报错了`,
    });
  }
  return tryData;
};

export const timeDiff = ({ endTime, status }) => {
  const diff = (new Date(endTime.replace(/-/g, '/')).getTime() - Date.now()) / 1000;
  let day = Math.floor(diff / 86400) <= 0 ? 0 : Math.floor(diff / 86400);
  let hour = Math.floor((diff % 86400) / 3600) <= 0 ? 0 : Math.floor((diff % 86400) / 3600);
  let minute =
    Math.floor(((diff % 86400) % 3600) / 60) <= 0 ? 0 : Math.floor(((diff % 86400) % 3600) / 60);
  let seconds = Math.floor(((diff % 86400) % 3600) % 60) <= 0 ? 0 : Math.floor(((diff % 86400) % 3600) % 60);

  //如果没有inquiryEndTime的话，这直接返回了
  if (!endTime) {
    return null;
  }

  if (status === 1 || status === 2 || status === 7) {
    if (day) {
      return `${day}天后开始`;
    } else if (hour) {
      return `${hour}小时后开始`;
    } else if (minute) {
      return `${minute}分钟后开始`;
    } else {
      return `${seconds}秒后开始`;
    }
  }

  if (status === 3) {
    if (day) {
      return `${day}天后结束`;
    } else if (hour) {
      return `${hour}小时后结束`;
    } else if (minute) {
      return `${minute}分钟后结束`;
    } else {
      return `${seconds}秒后结束`;
    }
  }
  return null;
};

export const createSkeletonPlaceholderList = (length, item = {}) => {
  return Array.from({ length }).map((k) => ({
    attachmentDomain: {
      attachmentDownloadUrl: '',
    },
    ...item,
  }));
};

export const getWebviewUrl = (url, search = '') => {
  let res = decodeURIComponent(url) || '';
  let paramSplicing = (search && (res.includes('?') ? '&' : '?')) || '';
  const baseSrc = `${res}${paramSplicing}${search}`;
  const [path = ''] = res.split('?'); // 下面if判断要针对pathname 不能笼统的看url
  if (/^https?:\/\/.+/.test(path)) {
    if (!/za-doctor|localhost/.test(path)) {
      return res;
    }
    return baseSrc;
  }
  return `${webviewUrl}${baseSrc}`;
};

const LITTLE_RED_DOT = 'LITTLE_RED_DOT';
export const zaMallTabBarLittleRedDotShow = () => {
  const zaMallLittleRedDot = storage.get(LITTLE_RED_DOT) || 0;
  if (zaMallLittleRedDot) {
    return;
  };
  wx.showTabBarRedDot({ index: 1 });
}
export const zaMallTabBarLittleRedDotHide = () => {
  const LITTLE_RED_DOT = 'LITTLE_RED_DOT';
  const zaMallLittleRedDot = storage.get(LITTLE_RED_DOT) || 0;
  if (zaMallLittleRedDot) {
    return;
  };
  wx.hideTabBarRedDot({ index: 1 });
  everyDayOnceExpiredStorage(LITTLE_RED_DOT);
}

/**
 * 移除为空参数
 * @param {*} args
 * @returns
 * - 入参 {a:1, b:'', c: { e: 1, d: ''}}
 * - result {a:1, c: {e: 1}}
 */
export function clearParams(args) {
  if (Object.prototype.toString.call(args) === '[object Array]') {
    return args.map((obj) => this.clearParams(obj));
  }
  if (Object.prototype.toString.call(args) !== '[object Object]') {
    return {};
  }
  const result = args;
  for (const key in result) {
    if (result[key] === '' || result[key] === undefined) {
      delete result[key];
    }
    if (
      Object.prototype.toString.call(result[key]) === '[object Object]' ||
      Object.prototype.toString.call(result[key]) === '[object Array]'
    ) {
      // 空对象移除
      if (JSON.stringify(result[key]) === '{}') {
        delete result[key];
      }
      this.clearParams(result[key]);
    }
  }
  return result;
}
// 浮点数计算
export const CalculateFunc = {
  /**
     * 两个浮点数相减
     */
  subNum(a = 0, b = 0) {
    let c;
    let d;
    let e;
    try {
      c = a.toString().split('.')[1].length;
    } catch (f) {
      c = 0;
    }
    try {
      d = b.toString().split('.')[1].length;
    } catch (f) {
      d = 0;
    }
    return e = Math.pow(10, Math.max(c, d)), (this.mulNum(a, e) - this.mulNum(b, e)) / e;
  },

  // 多个浮点数相减
  subNums() {
    if (arguments.length < 2) {
      throw 'params wrong';
    }
    let res = this.subNum(arguments[0], arguments[1]);
    for (let i = 2; i < arguments.length; i++) { // arguments.length表示传入参数的个数
      res = this.subNum(res, arguments[i]);
    }
    return res;
  },

  /**
     * 浮点数相加
     * @param arg1
     * @param arg2
     * @returns {number}
     */
  addNum(a = 0, b = 0) {
    let c;
    let d;
    let e;
    try {
      c = a.toString().split('.')[1].length;
    } catch (f) {
      c = 0;
    }
    try {
      d = b.toString().split('.')[1].length;
    } catch (f) {
      d = 0;
    }
    return e = Math.pow(10, Math.max(c, d)), (this.mulNum(a, e) + this.mulNum(b, e)) / e;
  },

  // 多个浮点数相加
  addNums() {
    if (arguments.length < 2) {
      throw 'params wrong';
    }
    let res = this.addNum(arguments[0], arguments[1]);
    for (let i = 2; i < arguments.length; i++) { // arguments.length表示传入参数的个数
      res = this.addNum(res, arguments[i]);
    }
    return res;
  },

  /**
     * 浮点数相乘
     * @param a
     * @param b
     * @returns {number}
     */
  mulNum(a, b) {
    let c = 0;
    const d = a.toString();
    const e = b.toString();
    try {
      c += d.split('.')[1].length;
    } catch (f) {
    }
    try {
      c += e.split('.')[1].length;
    } catch (f) {
    }
    return Number(d.replace('.', '')) * Number(e.replace('.', '')) / Math.pow(10, c);
  },

  // 多个浮点数相乘
  mulNums() {
    if (arguments.length < 2) {
      throw 'params wrong';
    }
    let res = this.mulNum(arguments[0], arguments[1]);
    for (let i = 2; i < arguments.length; i++) { // arguments.length表示传入参数的个数
      res = this.mulNum(res, arguments[i]);
    }
    return res;
  },

  /**
     * 浮点数相除
     * @param a
     * @param b
     */
  divNum(a, b) {
    let c;
    let d;
    let e = 0;
    let f = 0;
    try {
      e = a.toString().split('.')[1].length;
    } catch (g) {
    }
    try {
      f = b.toString().split('.')[1].length;
    } catch (g) {
    }
    return c = Number(a.toString().replace('.', '')), d = Number(b.toString().replace('.', '')), this.mulNum(c / d, Math.pow(10, f - e));
  },

  // 多个浮点数相除
  divNums() {
    if (arguments.length < 2) {
      throw 'params wrong';
    }
    let res = this.divNum(arguments[0], arguments[1]);
    for (let i = 2; i < arguments.length; i++) { // arguments.length表示传入参数的个数
      res = this.divNum(res, arguments[i]);
    }
    return res;
  },
};

export const getDomain = (url = '') => {
  const regex = /^(?:https?:\/\/)?(?:[^@\n]+@)?(?:www\.)?([^:/\n]+)/im;
  return (url.match(regex) || [])[1];
}

/**
 * 过滤 URL 上的 searchParams (不支持 URL & URLSearchParams API 的环境使用)
 * @param {string} url
 * @param {string[]} filterParams 需要过滤的参数名: ['a', 'b']
 * @returns {string}
 */
export const filterURLParams = (url = '', filterParams = [])=> {
  if (!url || !filterParams.length) return url;
  const [href, search] = url.split('?');
  const searchParams = search ? search.split('&') : [];
  const filterSearchParams = searchParams.filter((param) => !filterParams.includes(param.split('=')[0]));
  return `${href}?${filterSearchParams.join('&')}`;
};

/**
 * 增加天数
 * @param {Date} date
 * @param {number} days
 * @returns {Date}
 */
export function addDays(date, days) {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

/**
 * @description 图片上传
 */
export const uploadImage = async (params) => {
  const { filePath, businessNo, attachmentType = 'userHeadPortrait' } = params;
  const baseUrl = config.baseAPI['zaApi'].host;
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: baseUrl + '/v1/attchment/upload/byUser',
      filePath: filePath,
      name: 'file',
      header: {
        'token': storage.get('Token') || '',
        'platform': 'wechatapp',
        'content-type': 'multipart/form-data',
      },
      formData: {
        businessNo: businessNo || '',
        attachmentType,
      },
      success: (imgRes) => {
        //do something
        const data = JSONPARSE(imgRes.data, 'uploadimg');
        resolve(data.result);
      },
      fail: (error) => {
        console.log(config.baseAPI);
        console.log('图片上传失败', error);
        reject(error);
      }
    })
  });
}
