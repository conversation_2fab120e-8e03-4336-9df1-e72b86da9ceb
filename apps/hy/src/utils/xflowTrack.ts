import { Serialize } from "./serialization";

const app = getApp();

export const xflowPushEvent = (
  option
) => {
  try {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1] || {};
    const pathParams = currentPage.sensors_mp_encode_url_query || Serialize(currentPage.options || {});
    const pathUrl = currentPage.route + '?' + pathParams; 
    app.za.customTrack({
      ...option,
      event_url: pathUrl,
    });
  } catch (e) {
    console.log(e);
  }
};


/**
 * 发送web-view组件错误信息的公共方法
 * @param {Object} options - 请求配置
 * @param {string} options.url - 请求URL
 * @param {Object} options.data - 请求数据
 * @param {Object|Error} error - 错误对象
 * @param {string} level - 错误级别，默认为'error'
 */
export function sendCustomEvent (name, options, error, level = 'error') {
  if (!global._SERAPH_) {
    console.error('全局SERAPH对象未初始化');
    return;
  }
  
  try {
    global._SERAPH_.sendCustomEvent({
      name,
      source: JSON.stringify({ 
        ...options, 
        ...(error || {}).data,
        timestamp: Date.now(),
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown'
      }),
      level,
    });
  } catch (e) {
    console.error('发送错误信息失败', e);
  }
}