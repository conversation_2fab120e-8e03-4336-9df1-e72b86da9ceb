import dev from './env/dev'; //本地或开发
import prd from './env/prd'; //线上
import uat from './env/pre'; //体验环境

import Taro from '@tarojs/taro';

const { miniProgram: { envVersion = 'release' } = {} } = Taro.getAccountInfoSync();
const getMiniEnv = () => {
  const defaultEnv = 'prd'; //'dev | uat | prd';
  const prevEvn = Taro.getStorageSync('DEBUG_ENV') || defaultEnv;
  return envVersion == 'release' ? 'prd' : prevEvn; //该行切勿修改，确保线上环境一定是prd，如开发需要 可修改 defaultEnv;
}

const ENV = getMiniEnv();

const activityMap = {
  'dev': 370004,
  'uat': 145002,
  'prd': 150002,
}
// 药品商城入口 channpoit
const mallMap = {
  'dev': 'Jk4QLN7T',
  'uat': 'D2dSNTW5',
  'prd': '2Y0hcqGQ',
}

export let configs = {
  dev,
  uat,
  prd
};
const currentEnv = configs[ENV] || {};
var config = {
  ENV,
  baseAPI: currentEnv,
  activityId: activityMap[ENV],
  drugStorePoint: mallMap[ENV],
  envVersion,
};

export const __DEBUG__ = (ENV != "prd") || envVersion != 'release';

export const webviewUrl = config.baseAPI.zaApi.webview;
export const mallWebviewUrl = config.baseAPI.mallApi.host;

export default config;
