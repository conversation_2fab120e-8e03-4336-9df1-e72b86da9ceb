var _={},zaWeMini={para:{name:"za",server_url:"https://xflowcloud.zhongan.io/nginx/cloud_xcx_sdk",send_timeout:1e3,max_string_length:300,datasend_timeout:6e3,source_channel:[],autoTrack:{appLaunch:!0,appShow:!0,appHide:!0,pageShow:!0,pageShare:!0,mpClick:!0,mpFavorite:!0},is_persistent_save:!1,app_name:"",app_id:"",source_id:"",ABTestUrl:"",tenantCode:""}};_.ilog4EventName="xcxIlog4Custom",_.ilog4LatestDesc="ilog5\u5355\u8fb9\u53d1\u9001ilog4\u81ea\u5b9a\u4e49\u4e8b\u4ef6",_.ilog4BothDesc="ilog5\u4e24\u8fb9\u53d1\u9001ilog4\u81ea\u5b9a\u4e49\u4e8b\u4ef6";var hasAdaptedIlog4=!1,adaptIlog4=function(e,t,i){var n=e&&e.adaptIlog4;try{if(!0===t){if("both"===n||"latest"===n){var a=i&&i.zaTitle,r=i&&i.zaIdx;(a||r)&&zaWeMini.customTrack({event_type:a||r,event_value:_.ilog4EventName,event_description:"ilog5\u70b9\u51fb\u53d1\u9001ilog4\u81ea\u5b9a\u4e49\u4e8b\u4ef6"})}return}var o=getApp();if("both"===n)for(var s=0;s<10;s++)setTimeout((function(){try{var e=o.zast&&o.zast.sendEvent;_.isObject(o.zast)&&_.isFunction(e)&&!hasAdaptedIlog4&&(hasAdaptedIlog4=!0,o.zast&&(o.zast.sendEvent=function(){zaWeMini.customTrack({event_type:arguments&&arguments[0],event_value:_.ilog4EventName,event_description:_.ilog4BothDesc}),e.apply(o.zast.__proto__,arguments)}))}catch(e){logger.info("adaptIlog4 error",e)}}),1e3*s);else"latest"===n&&(o.zast=o.zast||{},o.zast.sendEvent=function(){zaWeMini.customTrack({event_type:arguments&&arguments[0],event_value:_.ilog4EventName,event_description:_.ilog4LatestDesc})})}catch(e){logger.info("adaptIlog4 error",e)}};zaWeMini._proxy={type:{$MPLaunch:"launch",$MPShow:"show",$MPShare:"share",$MPViewScreen:"page",$MPHide:"hide",$MPClick:"click",$MPCustom:"custom",$MPLogin:"SignUp",$MPABTest:"ABTestTrigger",$MPAddFavorites:"MPAddFavorites"},clickEventTypes:["tap","longpress","longtap"]};var mpHook={data:1,onLoad:1,onShow:1,onReady:1,onPullDownRefresh:1,onShareAppMessage:1,onShareTimeline:1,onReachBottom:1,onPageScroll:1,onResize:1,onTabItemTap:1,onHide:1,onUnload:1},logger="object"===typeof logger?logger:{};logger.info=function(){if(zaWeMini.para.debug&&"object"===typeof console&&console.log)try{return console.log.apply(console,arguments)}catch(e){console.log(arguments[0])}},zaWeMini.setPara=function(e){zaWeMini.para=_.extend2Lev(zaWeMini.para,e);var t=[];if(_.isArray(zaWeMini.para.source_channel))for(var i=zaWeMini.para.source_channel.length,n=" utm_source utm_medium utm_campaign utm_content utm_term sa_utm ",a=0;a<i;a++)-1===n.indexOf(" "+zaWeMini.para.source_channel[a]+" ")&&t.push(zaWeMini.para.source_channel[a]);zaWeMini.para.source_channel=t,_.isObject(zaWeMini.para.register)&&_.extend(_.info.properties,zaWeMini.para.register),"number"!==typeof zaWeMini.para.send_timeout&&(zaWeMini.para.send_timeout=1e3),zaWeMini.para.server_url||logger.info("\u8bf7\u4f7f\u7528 setPara() \u65b9\u6cd5\u8bbe\u7f6e server_url \u6570\u636e\u63a5\u6536\u5730\u5740")},zaWeMini.status={};var ArrayProto=Array.prototype,FuncProto=Function.prototype,ObjProto=Object.prototype,slice=ArrayProto.slice,toString=ObjProto.toString,hasOwnProperty=ObjProto.hasOwnProperty,LIB_VERSION="2.0.2",LIB_NAME="MiniProgram",source_channel_standard="utm_campaign utm_source utm_medium utm_term utm_content bizOrigin messageNo taskCode clickid ABTVersion",latest_source_channel=["$latest_utm_campaign","$latest_utm_source","$latest_utm_campaign","$latest_utm_term","$latest_bizOrigin"],mp_scene={1001:"\u53d1\u73b0\u680f\u5c0f\u7a0b\u5e8f\u4e3b\u5165\u53e3,'\u6700\u8fd1\u4f7f\u7528'\u5217\u8868",1005:"\u9876\u90e8\u641c\u7d22\u6846\u7684\u641c\u7d22\u7ed3\u679c\u9875",1006:"\u53d1\u73b0\u680f\u5c0f\u7a0b\u5e8f\u4e3b\u5165\u53e3\u641c\u7d22\u6846\u7684\u641c\u7d22\u7ed3\u679c\u9875",1007:"\u5355\u4eba\u804a\u5929\u4f1a\u8bdd\u4e2d\u7684\u5c0f\u7a0b\u5e8f\u6d88\u606f\u5361\u7247",1008:"\u7fa4\u804a\u4f1a\u8bdd\u4e2d\u7684\u5c0f\u7a0b\u5e8f\u6d88\u606f\u5361\u7247",1011:"\u626b\u63cf\u4e8c\u7ef4\u7801",1012:"\u957f\u6309\u56fe\u7247\u8bc6\u522b\u4e8c\u7ef4\u7801",1013:"\u624b\u673a\u76f8\u518c\u9009\u53d6\u4e8c\u7ef4\u7801",1014:"\u5c0f\u7a0b\u5e8f\u6a21\u7248\u6d88\u606f",1017:"\u524d\u5f80\u4f53\u9a8c\u7248\u7684\u5165\u53e3\u9875",1019:"\u5fae\u4fe1\u94b1\u5305",1020:"\u516c\u4f17\u53f7 profile \u9875\u76f8\u5173\u5c0f\u7a0b\u5e8f\u5217\u8868",1022:"\u804a\u5929\u9876\u90e8\u7f6e\u9876\u5c0f\u7a0b\u5e8f\u5165\u53e3",1023:"\u5b89\u5353\u7cfb\u7edf\u684c\u9762\u56fe\u6807",1024:"\u5c0f\u7a0b\u5e8f profile \u9875",1025:"\u626b\u63cf\u4e00\u7ef4\u7801",1026:"\u9644\u8fd1\u5c0f\u7a0b\u5e8f\u5217\u8868",1027:"\u9876\u90e8\u641c\u7d22\u6846\u641c\u7d22\u7ed3\u679c\u9875'\u4f7f\u7528\u8fc7\u7684\u5c0f\u7a0b\u5e8f'\u5217\u8868",1028:"\u6211\u7684\u5361\u5305",1029:"\u5361\u5238\u8be6\u60c5\u9875",1030:"\u81ea\u52a8\u5316\u6d4b\u8bd5\u4e0b\u6253\u5f00\u5c0f\u7a0b\u5e8f",1031:"\u957f\u6309\u56fe\u7247\u8bc6\u522b\u4e00\u7ef4\u7801",1032:"\u624b\u673a\u76f8\u518c\u9009\u53d6\u4e00\u7ef4\u7801",1034:"\u5fae\u4fe1\u652f\u4ed8\u5b8c\u6210\u9875",1035:"\u516c\u4f17\u53f7\u81ea\u5b9a\u4e49\u83dc\u5355",1036:"App \u5206\u4eab\u6d88\u606f\u5361\u7247",1037:"\u5c0f\u7a0b\u5e8f\u6253\u5f00\u5c0f\u7a0b\u5e8f",1038:"\u4ece\u53e6\u4e00\u4e2a\u5c0f\u7a0b\u5e8f\u8fd4\u56de",1039:"\u6447\u7535\u89c6",1042:"\u6dfb\u52a0\u597d\u53cb\u641c\u7d22\u6846\u7684\u641c\u7d22\u7ed3\u679c\u9875",1043:"\u516c\u4f17\u53f7\u6a21\u677f\u6d88\u606f",1044:"\u5e26 shareTicket \u7684\u5c0f\u7a0b\u5e8f\u6d88\u606f\u5361\u7247\uff08\u8be6\u60c5)",1045:"\u670b\u53cb\u5708\u5e7f\u544a",1046:"\u670b\u53cb\u5708\u5e7f\u544a\u8be6\u60c5\u9875",1047:"\u626b\u63cf\u5c0f\u7a0b\u5e8f\u7801",1048:"\u957f\u6309\u56fe\u7247\u8bc6\u522b\u5c0f\u7a0b\u5e8f\u7801",1049:"\u624b\u673a\u76f8\u518c\u9009\u53d6\u5c0f\u7a0b\u5e8f\u7801",1052:"\u5361\u5238\u7684\u9002\u7528\u95e8\u5e97\u5217\u8868",1053:"\u641c\u4e00\u641c\u7684\u7ed3\u679c\u9875",1054:"\u9876\u90e8\u641c\u7d22\u6846\u5c0f\u7a0b\u5e8f\u5feb\u6377\u5165\u53e3",1056:"\u97f3\u4e50\u64ad\u653e\u5668\u83dc\u5355",1057:"\u94b1\u5305\u4e2d\u7684\u94f6\u884c\u5361\u8be6\u60c5\u9875",1058:"\u516c\u4f17\u53f7\u6587\u7ae0",1059:"\u4f53\u9a8c\u7248\u5c0f\u7a0b\u5e8f\u7ed1\u5b9a\u9080\u8bf7\u9875",1064:"\u5fae\u4fe1\u8fdeWi-Fi\u72b6\u6001\u680f",1067:"\u516c\u4f17\u53f7\u6587\u7ae0\u5e7f\u544a",1068:"\u9644\u8fd1\u5c0f\u7a0b\u5e8f\u5217\u8868\u5e7f\u544a",1069:"\u79fb\u52a8\u5e94\u7528",1071:"\u94b1\u5305\u4e2d\u7684\u94f6\u884c\u5361\u5217\u8868\u9875",1072:"\u4e8c\u7ef4\u7801\u6536\u6b3e\u9875\u9762",1073:"\u5ba2\u670d\u6d88\u606f\u5217\u8868\u4e0b\u53d1\u7684\u5c0f\u7a0b\u5e8f\u6d88\u606f\u5361\u7247",1074:"\u516c\u4f17\u53f7\u4f1a\u8bdd\u4e0b\u53d1\u7684\u5c0f\u7a0b\u5e8f\u6d88\u606f\u5361\u7247",1077:"\u6447\u5468\u8fb9",1078:"\u8fdeWi-Fi\u6210\u529f\u9875",1079:"\u5fae\u4fe1\u6e38\u620f\u4e2d\u5fc3",1081:"\u5ba2\u670d\u6d88\u606f\u4e0b\u53d1\u7684\u6587\u5b57\u94fe",1082:"\u516c\u4f17\u53f7\u4f1a\u8bdd\u4e0b\u53d1\u7684\u6587\u5b57\u94fe",1084:"\u670b\u53cb\u5708\u5e7f\u544a\u539f\u751f\u9875",1089:"\u5fae\u4fe1\u804a\u5929\u4e3b\u754c\u9762\u4e0b\u62c9",1090:"\u957f\u6309\u5c0f\u7a0b\u5e8f\u53f3\u4e0a\u89d2\u83dc\u5355\u5524\u51fa\u6700\u8fd1\u4f7f\u7528\u5386\u53f2",1091:"\u516c\u4f17\u53f7\u6587\u7ae0\u5546\u54c1\u5361\u7247",1092:"\u57ce\u5e02\u670d\u52a1\u5165\u53e3",1095:"\u5c0f\u7a0b\u5e8f\u5e7f\u544a\u7ec4\u4ef6",1096:"\u804a\u5929\u8bb0\u5f55",1097:"\u5fae\u4fe1\u652f\u4ed8\u7b7e\u7ea6\u9875",1099:"\u9875\u9762\u5185\u5d4c\u63d2\u4ef6",1102:"\u516c\u4f17\u53f7 profile \u9875\u670d\u52a1\u9884\u89c8",1103:"\u53d1\u73b0\u680f\u5c0f\u7a0b\u5e8f\u4e3b\u5165\u53e3,'\u6211\u7684\u5c0f\u7a0b\u5e8f'\u5217\u8868",1104:"\u5fae\u4fe1\u804a\u5929\u4e3b\u754c\u9762\u4e0b\u62c9,'\u6211\u7684\u5c0f\u7a0b\u5e8f'\u680f",1106:"\u804a\u5929\u4e3b\u754c\u9762\u4e0b\u62c9,\u4ece\u9876\u90e8\u641c\u7d22\u7ed3\u679c\u9875,\u6253\u5f00\u5c0f\u7a0b\u5e8f",1124:"\u626b'\u4e00\u7269\u4e00\u7801'\u6253\u5f00\u5c0f\u7a0b\u5e8f",1125:"\u957f\u6309\u56fe\u7247\u8bc6\u522b'\u4e00\u7269\u4e00\u7801'",1126:"\u626b\u63cf\u624b\u673a\u76f8\u518c\u4e2d\u9009\u53d6\u7684'\u4e00\u7269\u4e00\u7801'",1129:"\u5fae\u4fe1\u722c\u866b\u8bbf\u95ee",1131:"\u6d6e\u7a97\u6253\u5f00\u5c0f\u7a0b\u5e8f"},globalTitle={},page_route_map=[],mpshow_time=null,is_first_launch=!1;zaWeMini.lib_version=LIB_VERSION,function(){FuncProto.bind;var e=ArrayProto.forEach,t=ArrayProto.indexOf,i=Array.isArray,n={},a=_.each=function(t,i,a){if(null==t)return!1;if(e&&t.forEach===e)t.forEach(i,a);else if(t.length===+t.length){for(var r=0,o=t.length;r<o;r++)if(r in t&&i.call(a,t[r],r,t)===n)return!1}else for(var s in t)if(hasOwnProperty.call(t,s)&&i.call(a,t[s],s,t)===n)return!1};_.logger=logger,_.extend=function(e){return a(slice.call(arguments,1),(function(t){for(var i in t)void 0!==t[i]&&(e[i]=t[i])})),e},_.extend2Lev=function(e){return a(slice.call(arguments,1),(function(t){for(var i in t)void 0!==t[i]&&(_.isObject(t[i])&&_.isObject(e[i])?_.extend(e[i],t[i]):e[i]=t[i])})),e},_.coverExtend=function(e){return a(slice.call(arguments,1),(function(t){for(var i in t)void 0!==t[i]&&void 0===e[i]&&(e[i]=t[i])})),e},_.isArray=i||function(e){return"[object Array]"===toString.call(e)},_.isFunction=function(e){try{return/^\s*\bfunction\b/.test(e)}catch(e){return!1}},_.isArguments=function(e){return!(!e||!hasOwnProperty.call(e,"callee"))},_.toArray=function(e){return e?e.toArray?e.toArray():_.isArray(e)||_.isArguments(e)?slice.call(e):_.values(e):[]},_.values=function(e){var t=[];return null==e||a(e,(function(e){t[t.length]=e})),t},_.include=function(e,i){var r=!1;return null==e?r:t&&e.indexOf===t?-1!=e.indexOf(i):(a(e,(function(e){if(r||(r=e===i))return n})),r)}}(),_.trim=function(e){return e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")},_.isObject=function(e){return void 0!==e&&null!==e&&"[object Object]"==toString.call(e)},_.isEmptyObject=function(e){if(_.isObject(e)){for(var t in e)if(hasOwnProperty.call(e,t))return!1;return!0}return!1},_.isUndefined=function(e){return void 0===e},_.setDefaultValue=function(e){var t=e;for(var i in t)hasOwnProperty.call(t,i)&&_.isUndefined(t[i])&&"accountId"!==i&&(t[i]="");return t},_.handleUnfinedValue=function(e){return"undefined"==typeof e||""===e?"":e},_.isString=function(e){return"[object String]"==toString.call(e)},_.isDate=function(e){return"[object Date]"==toString.call(e)},_.isBoolean=function(e){return"[object Boolean]"==toString.call(e)},_.isNumber=function(e){return"[object Number]"==toString.call(e)&&/[\d\.]+/.test(String(e))},_.isJSONString=function(e){try{JSON.parse(e)}catch(e){return!1}return!0},_.decodeURIComponent=function(e){var t="";try{t=decodeURIComponent(e)}catch(i){t=e}return t},_.encodeDates=function(e){return _.each(e,(function(t,i){_.isDate(t)?e[i]=_.formatDate(t):_.isObject(t)&&(e[i]=_.encodeDates(t))})),e},_.formatDate=function(e,t){function i(e){return e<10?"0"+e:e}return e.getFullYear()+"-"+i(e.getMonth()+1)+"-"+i(e.getDate())+" "+i(e.getHours())+":"+i(e.getMinutes())+":"+i(e.getSeconds())+(!0===t?"":"."+i(e.getMilliseconds()))},_.searchObjDate=function(e){_.isObject(e)&&_.each(e,(function(t,i){_.isObject(t)?_.searchObjDate(e[i]):_.isDate(t)&&(e[i]=_.formatDate(t))}))},_.formatString=function(e){return e.length>zaWeMini.para.max_string_length?(logger.info("\u5b57\u7b26\u4e32\u957f\u5ea6\u8d85\u8fc7\u9650\u5236\uff0c\u5df2\u7ecf\u505a\u622a\u53d6--"+e),e.slice(0,zaWeMini.para.max_string_length)):e},_.searchObjString=function(e){_.isObject(e)&&_.each(e,(function(t,i){_.isObject(t)?_.searchObjString(e[i]):_.isString(t)&&(e[i]=_.formatString(t))}))},_.unique=function(e){for(var t,i=[],n={},a=0;a<e.length;a++)t=e[a],t in n||(n[t]=!0,i.push(t));return i},_.strip_empty_properties=function(e){var t={};return _.each(e,(function(e,i){null!=e&&(t[i]=e)})),t},_.getCurrentPage=function(){var e={};try{var t=getCurrentPages();e=t[t.length-1]}catch(e){logger.info(e)}return e},_.getCurrentPath=function(){var e="\u672a\u53d6\u5230";try{var t=getCurrentPages(),i=t[t.length-1];e=i.route}catch(e){logger.info(e)}return e},_.getPath=function(e){return e="string"===typeof e?e.replace(/^\//,""):"\u53d6\u503c\u5f02\u5e38",e},zaWeMini.initialState={queue:[],isComplete:!1,systemIsComplete:!1,storeIsComplete:!1,checkIsComplete:function(){this.systemIsComplete&&this.storeIsComplete&&(this.isComplete=!0,this.queue.length>0&&(_.each(this.queue,(function(e){zaWeMini[e[0]].apply(zaWeMini,slice.call(e[1]))})),this.queue=[]))}};try{var oldSetNavigationBarTitle=wx.setNavigationBarTitle;Object.defineProperty(wx,"setNavigationBarTitle",{get:function(){return function(e){var t=_.getCurrentPath();e=_.isObject(e)?e:{},globalTitle[t]=e.title,oldSetNavigationBarTitle.call(this,e)}}})}catch(e){logger.info(e)}function omitEmptyValue(e){var t={};for(var i in e)hasOwnProperty.call(e,i)&&e[i]&&(t[i]=e[i]);return t}function getUTMMixObj(e){if(!e)return{};var t={};try{for(var i=source_channel_standard.split(" "),n=0;n<i.length;n++){var a=i[n];a&&e[a]&&(t[a]=e[a])}}catch(e){}return t}function getExtendsInfo(e){try{var t={},i=_.extend({},zaWeMini.para.extendsInfo||{}),n={};if("function"===typeof i.getDynamic)try{n=i.getDynamic(e)}catch(e){}for(var a in"object"===typeof n&&(i={...i,...n}),i)if(hasOwnProperty.call(i,a))if("function"!==typeof i[a])t[a]=i[a];else if("function"===typeof i[a]&&"getDynamic"!==a)try{t[a]=i[a]()}catch(e){logger.info("\u52a8\u6001\u914d\u7f6e\u9879\u6267\u884c\u9519\u8bef")}return t}catch(e){return logger.info("call getExtendsInfo error",e),{}}}function mp_proxy(e,t,i){var n=zaWeMini.autoTrackCustom[i];if(e[t]){var a=e[t];e[t]=function(){"onLaunch"===t&&(this[zaWeMini.para.name]=zaWeMini),!zaWeMini.para.autoTrackIsFirst||_.isObject(zaWeMini.para.autoTrackIsFirst)&&!zaWeMini.para.autoTrackIsFirst[i]?(a.apply(this,arguments),n.apply(this,arguments)):(!0===zaWeMini.para.autoTrackIsFirst||_.isObject(zaWeMini.para.autoTrackIsFirst)&&zaWeMini.para.autoTrackIsFirst[i])&&(n.apply(this,arguments),a.apply(this,arguments))}}else e[t]=function(){"onLaunch"===t&&(this[zaWeMini.para.name]=zaWeMini),n.apply(this,arguments)}}function clickTrack(e){try{var t=e.currentTarget,i=e.type;if(t&&-1!=zaWeMini._proxy.clickEventTypes.indexOf(i)){var n=zaWeMini.para.autoTrack["mpClick"];if("function"===typeof n&&!1===n(arguments[0]))return!1;var a={};a.datasets=t&&t.dataset,zaWeMini.para&&zaWeMini.para.isPlugin&&zaWeMini.dynamicInfo&&zaWeMini.dynamicInfo.$url_path&&"\u672a\u53d6\u5230"===_.getCurrentPath()?a.$url_path=zaWeMini.dynamicInfo.$url_path:a.$url_path=_.getCurrentPath(),zaWeMini.para&&zaWeMini.para.adaptIlog4&&adaptIlog4(zaWeMini.para,!0,a.datasets),zaWeMini.track("$MPClick",a)}}catch(e){}}function click_proxy(e,t){var i=e[t];e[t]=function(){var e=i.apply(this,arguments),t=arguments[0];return _.isObject(t)&&clickTrack(t),e}}function tabProxy(e){var t=e["onTabItemTap"];e["onTabItemTap"]=function(e){t&&t.apply(this,arguments);var i={};e&&(i["$element_content"]=e.text),i["$element_type"]="tabBar",i["$url_path"]=_.getCurrentPath(),zaWeMini.autoTrackCustom.trackCustom("mpClick",i,"$MPClick")}}_.setRefPage=function(){var e={route:"\u76f4\u63a5\u6253\u5f00",title:""};try{var t=_.getCurrentPage();if(t&&t.route){var i=t.route,n=_.getPageTitle(i);e.route=i,e.title=n;var a=page_route_map.length,r="";a>=1&&(r=page_route_map[a-1].route),r!==i&&(a>=2?(page_route_map.shift(),page_route_map.push(e)):page_route_map.push(e))}}catch(e){logger.info(e)}},_.getRefPage=function(){var e={route:"\u76f4\u63a5\u6253\u5f00",title:""};return page_route_map.length>1&&(e.title=page_route_map[0].title,e.route=page_route_map[0].route),e},_.setPageRefData=function(e){var t=_.getRefPage();_.isObject(e)&&(e.$referrer=t.route,e.$referrer_title=t.title)},_.getPageTitle=function(e){if("\u672a\u53d6\u5230"===e||!e)return!1;var t="";try{if(__wxConfig){var i=__wxConfig,n=__wxConfig.page||{},a=n[e]||n[e+".html"],r={},o={};if(i.global&&i.global.window&&i.global.window.navigationBarTitleText&&(r.titleVal=i.global.window.navigationBarTitleText),a&&a.window&&a.window.navigationBarTitleText&&(o.titleVal=a.window.navigationBarTitleText),!o.titleVal&&__wxAppCode__){var s=__wxAppCode__[e+".json"];s&&s["navigationBarTitleText"]&&(o.titleVal=s["navigationBarTitleText"])}if(_.each(globalTitle,(function(i,n){if(n===e)return t=i})),0===t.length){var c=_.extend(r,o);t=c.titleVal||""}}}catch(i){logger.info(i)}return t},_.getMethods=function(e){var t=[];for(var i in e)"function"!==typeof e[i]||mpHook[i]||t.push(i);return t},_.getCustomUtmFromQuery=function(e,t,i,n){if(!_.isObject(e))return{};var a={};if(e["sa_utm"])for(var r in e)"sa_utm"!==r?_.include(zaWeMini.para.source_channel,r)&&(a[i+r]=e[r]):a[n+r]=e[r];else for(var r in e)if(-1===(" "+source_channel_standard+" ").indexOf(" "+r+" "))_.include(zaWeMini.para.source_channel,r)&&(a[i+r]=e[r]);else{var o={[r]:e[r]};zaWeMini.registerApp(o),a[t+r]=e[r]}return a},_.getObjFromQuery=function(e){var t=e.split("?"),i={};return t&&t[1]?(_.each(t[1].split("&"),(function(e){var t=e.split("=");t[0]&&t[1]&&(i[t[0]]=t[1])})),i):{}},_.setStorageSync=function(e,t){var i=function(){wx.setStorageSync&&wx.setStorageSync(e,t)};try{i()}catch(e){logger.info("set Storage fail --",e);try{i()}catch(e){logger.info("set Storage fail again --",e)}}},_.getStorageSync=function(t){var i="";try{i=wx.getStorageSync&&wx.getStorageSync(t)}catch(e){try{i=wx.getStorageSync&&wx.getStorageSync(t)}catch(e){logger.info("getStorage fail")}}return i},_.getMPScene=function(e){return"number"===typeof e||"string"===typeof e&&""!==e?(e=String(e),mp_scene[e]||e):void 0},_.detectOptionQuery=function(e){if(!e||!_.isObject(e.query))return{};var t={};function i(e){var t=["utm_source","utm_content","utm_medium","utm_campaign","utm_term","sa_utm"],i=t.concat(zaWeMini.para.source_channel),n=new RegExp("("+i.join("|")+")%3D","i"),a=Object.keys(e);return!(1!==a.length||"scene"!==a[0]||!n.test(e.scene))}return t.query=_.extend({},e.query),"string"===typeof t.query.scene&&i(t.query)&&(t.scene=t.query.scene,delete t.query.scene),e.query.q&&e.query.scancode_time&&"101"===String(e.scene).slice(0,3)&&(t.q=String(t.query.q),delete t.query.q,delete t.query.scancode_time),t},_.getMixedQuery=function(e){var t=_.detectOptionQuery(e),i=t.scene,n=t.q,a=t.query;for(var r in a)a[r]=_.decodeURIComponent(a[r]);return i&&(i=_.decodeURIComponent(i),i=-1!==i.indexOf("?")?"?"+i.replace(/\?/g,""):"?"+i,_.extend(a,_.getObjFromQuery(i))),n&&_.extend(a,_.getObjFromQuery(_.decodeURIComponent(n))),a},_.setUtm=function(e,t){var i={},n=_.getMixedQuery(e),a=_.getCustomUtmFromQuery(n,"$","_","$"),r=_.getCustomUtmFromQuery(n,"$latest_","_latest_","$latest_");return i.pre1=a,i.pre2=r,_.extend(t,a),i},_.wxrequest=function(e){var t=wx.request&&wx.request(e);setTimeout((function(){_.isObject(t)&&_.isFunction(t.abort)&&t.abort()}),zaWeMini.para.datasend_timeout)},_.info={currentProps:{},properties:{$lib:LIB_NAME,$lib_version:String(LIB_VERSION)},getSystem:function(){var e=this.properties;function t(){wx.getNetworkType&&wx.getNetworkType({success:function(t){e.$network_type=t["networkType"]},complete:n})}function i(e){var t=e.toLowerCase();return"ios"===t?"iOS":"android"===t?"Android":e}function n(){wx.getSystemInfo&&wx.getSystemInfo({success:function(t){e.$manufacturer=t["brand"],e.$model=t["model"],e.$screen_width=Number(t["screenWidth"]),e.$screen_height=Number(t["screenHeight"]),e.$os=i(t["platform"]),e.$os_version=t["system"].indexOf(" ")>-1?t["system"].split(" ")[1]:t["system"],e.$wx_version=t["version"],e.$language=t["language"]},complete:function(){zaWeMini.initialState.systemIsComplete=!0,zaWeMini.initialState.checkIsComplete()}})}t()}},zaWeMini._=_,zaWeMini.prepareData=function(e,t){var i={distinct_id:this.store.getDistinctId(),properties:{}};_.extend(i,e),_.isObject(e.properties)&&!_.isEmptyObject(e.properties)&&_.extend(i.properties,e.properties),e.type&&"profile"===e.type.slice(0,7)?(i.properties=_.extend({},_.info.properties,zaWeMini.store.getProps(),_.info.currentProps),i.profile_properties=e.properties):(i.properties=_.extend({},_.info.properties,zaWeMini.store.getProps(),_.info.currentProps,i.properties),"object"===typeof zaWeMini.store._state&&"number"===typeof zaWeMini.store._state.first_visit_day_time&&zaWeMini.store._state.first_visit_day_time>(new Date).getTime()?i.properties.$is_first_day=!0:i.properties.$is_first_day=!1),_.searchObjDate(i),_.searchObjString(i),zaWeMini.sendStrategy.send(i,t)},zaWeMini.store={verifyDistinctId:function(e){return"number"===typeof e&&(e=String(e),/^\d+$/.test(e)||(e="unexpected_id")),"string"===typeof e&&""!==e||(e="unexpected_id"),e},storageInfo:null,getUUID:function(){return Date.now()+"-"+Math.floor(1e7*Math.random())+"-"+Math.random().toString(16).replace(".","")+"-"+String(31242*Math.random()).replace(".","").slice(0,8)},getStorage:function(){return this.storageInfo||(this.storageInfo=zaWeMini._.getStorageSync("_za_sdk_wechat")||""),this.storageInfo},_state:{},mem:{mdata:[],getLength:function(){return this.mdata.length},add:function(e){this.mdata.push(e)},clear:function(e){this.mdata.splice(0,e)}},toState:function(e){var t=null;_.isJSONString(e)?(t=JSON.parse(e),t.distinct_id?(this._state=t,this.set("isnew",!1)):(this.set("distinct_id",this.getUUID()),this.set("isnew",!0))):_.isObject(e)?(t=e,t.distinct_id?(this._state=t,this.set("isnew",!1)):(this.set("distinct_id",this.getUUID()),this.set("isnew",!0))):(this.set("isnew",!0),this.set("distinct_id",this.getUUID()))},getFirstId:function(){return this._state.first_id},getDistinctId:function(){return this._state.distinct_id},getProps:function(){return this._state.props||{}},setProps:function(e,t){var i=this._state.props||{};t?this.set("props",e):(_.extend(i,e),this.set("props",i))},set:function(e,t){var i={};for(var n in"string"===typeof e?i[e]=t:"object"===typeof e&&(i=e),this._state=this._state||{},i)this._state[n]=i[n];this.save()},change:function(e,t){this._state[e]=t},save:function(){zaWeMini._.setStorageSync("_za_sdk_wechat",this._state)},init:function(){var e=this.getStorage();if(e)this.toState(e);else{is_first_launch=!0;var t=new Date,i=t.getTime();t.setHours(23),t.setMinutes(59),t.setSeconds(60),this.set({distinct_id:this.getUUID(),uid:this.getUUID(),first_visit_time:i,first_visit_day_time:t.getTime(),isnew:!0,app_name:zaWeMini.para.app_name})}}},zaWeMini.setProfile=function(e,t){if(!_.isObject(e))return!1;zaWeMini.prepareData({type:"profile_set",properties:e},t)},zaWeMini.setOnceProfile=function(e,t){if(!_.isObject(e))return!1;zaWeMini.prepareData({type:"profile_set_once",properties:e},t)},zaWeMini.appendProfile=function(e,t){if(!_.isObject(e))return!1;_.each(e,(function(t,i){_.isString(t)?e[i]=[t]:_.isArray(t)||(delete e[i],logger.info("appendProfile\u5c5e\u6027\u7684\u503c\u5fc5\u987b\u662f\u5b57\u7b26\u4e32\u6216\u8005\u6570\u7ec4"))})),zaWeMini.prepareData({type:"profile_append",properties:e},t)},zaWeMini.incrementProfile=function(e,t){if(!_.isObject(e))return!1;var i=e;_.isString(e)&&(e={},e[i]=1),zaWeMini.prepareData({type:"profile_increment",properties:e},t)},zaWeMini.deleteProfile=function(e,t){zaWeMini.prepareData({type:"profile_delete"},t)},zaWeMini.unsetProfile=function(e,t){var i=e;if(_.isString(e)&&(e=[i]),!_.isArray(e))return logger.info("profile_unset\u7684\u53c2\u6570\u662f\u6570\u7ec4"),!1;{const t={};_.each(e,(function(e){_.isString(e)?t[e]=!0:logger.info("profile_unset\u7ed9\u7684\u6570\u7ec4\u91cc\u9762\u7684\u503c\u5fc5\u987b\u65f6string,\u5df2\u7ecf\u8fc7\u6ee4\u6389",e)})),e=t}zaWeMini.prepareData({type:"profile_unset",properties:e},t)},zaWeMini.track=function(e,t,i){this.prepareData({type:"track",event:e,properties:t},i)},zaWeMini.customTrack=function(e,t){e=e||{};var i=_.extend({},e);zaWeMini.para&&zaWeMini.para.isPlugin&&zaWeMini.dynamicInfo&&zaWeMini.dynamicInfo.$url_path&&"\u672a\u53d6\u5230"===_.getCurrentPath()?e.$url_path=zaWeMini.dynamicInfo.$url_path:e.$url_path=_.getCurrentPath(),this.prepareData({type:"track",event:"$MPCustom",properties:e,originalProperties:i},t)},zaWeMini.triggerABTest=function(e,t,i,n,a){n=n||{};var r=_.extend({},n);zaWeMini.para&&zaWeMini.para.isPlugin&&zaWeMini.dynamicInfo&&zaWeMini.dynamicInfo.$url_path&&"\u672a\u53d6\u5230"===_.getCurrentPath()?n.$url_path=zaWeMini.dynamicInfo.$url_path:n.$url_path=_.getCurrentPath(),r.experiment_code=e,r.ABTVersion=t,r.control_content=i,this.prepareData({type:"track",event:"$MPABTest",properties:n,originalProperties:r},a)},zaWeMini.fetchABTestWithCode=function(e,t){try{if(e){const i=zaWeMini.abTestData||[];if(_.isArray(i)){const n=i.find((t=>t.ec===e));return t&&n&&zaWeMini.triggerABTest(n.ec,n.esc,"",{}),n}return null}return null}catch(t){return null}},zaWeMini.fetchABTestWithParamName=function(e,t){try{if(e){const i=zaWeMini.abTestData||[];if(_.isArray(i)){const n=i.find((t=>!!_.isArray(t.paramList)&&t.paramList.find((t=>t.name===e))));return t&&n&&zaWeMini.triggerABTest(n.ec,n.esc,"",{}),n}return null}return null}catch(t){return null}},zaWeMini.pageTrack=function(e,t){e=e||{},zaWeMini.para&&zaWeMini.para.isPlugin&&e.$url_path?(zaWeMini.dynamicInfo=zaWeMini.dynamicInfo||{},zaWeMini.dynamicInfo.$url_path=e.$url_path):e.$url_path=_.getCurrentPath(),this.prepareData({type:"track",event:"MPViewScreen",properties:e},t)},zaWeMini.setLogin=function(e,t){"string"!==typeof e&&"number"!==typeof e||!e||(zaWeMini.registerApp&&zaWeMini.registerApp({accountId:e}),this.prepareData({type:"track",event:"$MPLogin",properties:{accountId:e}},t))},zaWeMini.getSDKData=function(){function e(e,t){return e?e+"="+t+";":""}var t=";",i=zaWeMini.store.getStorage&&zaWeMini.store.getStorage().openid;return t=t+e("_xflow_traceid",_.handleUnfinedValue(_.info&&_.info.currentProps&&_.info.currentProps.trace_id))+e("_xflow_session_id",_.handleUnfinedValue(_.info&&_.info.currentProps&&_.info.currentProps.session_id))+e("_xflow_session_time",_.handleUnfinedValue(_.info&&_.info.currentProps&&_.info.currentProps.session_time))+e("_xflow_uid",_.handleUnfinedValue(zaWeMini.store.getStorage&&zaWeMini.store.getStorage().uid))+(i?e("_xflow_openid",_.handleUnfinedValue(i)):""),t},zaWeMini.registerApp=function(e){_.isObject(e)&&!_.isEmptyObject(e)&&(_.info.currentProps=_.extend(_.info.currentProps,e))},zaWeMini.register=function(e){_.isObject(e)&&!_.isEmptyObject(e)&&zaWeMini.store.setProps(e)},zaWeMini.clearAllRegister=function(){zaWeMini.store.setProps({},!0)},zaWeMini.clearAllProps=function(e){var t=zaWeMini.store.getProps(),i={};_.isArray(e)&&(_.each(t,(function(t,n){_.include(e,n)||(i[n]=t)})),zaWeMini.store.setProps(i,!0))},zaWeMini.clearAppRegister=function(e){_.isArray(e)&&_.each(_.info.currentProps,(function(t,i){_.include(e,i)&&delete _.info.currentProps[i]}))},zaWeMini.setLatestChannel=function(e){function t(e,t){var i=!1;for(var n in t)e[t[n]]&&(i=!0);return i}_.isEmptyObject(e)||(t(e,latest_source_channel)&&(zaWeMini.clearAppRegister(latest_source_channel),zaWeMini.clearAllProps(latest_source_channel)),zaWeMini.para.is_persistent_save?zaWeMini.register(e):zaWeMini.registerApp(e))},zaWeMini.initial=function(){this._.info.getSystem(),this.store.init()},zaWeMini.init=function(e){if(!0===this.hasInit)return!1;if(zaWeMini.registerApp&&zaWeMini.registerApp({session_id:zaWeMini.store&&zaWeMini.store.getUUID&&zaWeMini.store.getUUID(),session_time:_.formatDate(new Date,!0),trace_id:zaWeMini.store&&zaWeMini.store.getUUID&&zaWeMini.store.getUUID()}),this.hasInit=!0,zaWeMini.setPara(e),zaWeMini.para.ABTestUrl){const e=zaWeMini.store.getStorage().openid||zaWeMini.store.getStorage().uid,t={};zaWeMini.para.tenantCode&&(t["webgw-tenant-id"]=zaWeMini.para.tenantCode),_.wxrequest({url:zaWeMini.para.ABTestUrl+"?sf="+e,method:"GET",header:t,success:function(e){e&&e.data&&200===e.data.status&&(zaWeMini.abTestData=e.data.data||[])}})}zaWeMini.initialState.storeIsComplete=!0,zaWeMini.initialState.checkIsComplete()},zaWeMini.dataToWebview=function(e){if(!this.hasInit)return logger.info("SDK needs to be initialized first, please use this method after zaWeMini.init();"),"";try{var t=_.info&&_.info.currentProps||{},i=zaWeMini.store.getStorage&&zaWeMini.store.getStorage()||{},n=_.extend({uid:i.uid,trace_id:t.trace_id,session_id:t.session_id,session_time:t.session_time,open_id:i.openid,union_id:i.unionId||t.$unionId},!0===e?omitEmptyValue(_.extend({sdk_type:"xcx",xcx_sdk_source:"wechat",web_handle_xcx:1,accountId:t.accountId},getUTMMixObj(t))):{}),a="xflow_d_t_wv="+JSON.stringify(n);return encodeURIComponent(a)}catch(e){}return""},zaWeMini.getPresetProperties=function(){if(_.info&&_.info.properties&&_.info.properties.$lib){var e={};_.each(_.info.currentProps,(function(t,i){0===i.indexOf("$")&&(e[i]=t)}));var t=_.extend(e,{$url_path:_.getCurrentPath()},_.info.properties,zaWeMini.store.getProps());return delete t.$lib,t}return{}},zaWeMini.sendStrategy={dataHasSend:!0,dataHasChange:!1,onAppHide:function(){},send:function(e,t){if(!zaWeMini.para.server_url)return!1;this.sendData(e,t)},sendData:function(e,t){const i=!e.type||"profile"!==e.type.slice(0,7);if(e=i?this.conversionData(e):this.conversionProfileData(e),!e||void 0!==e.event_name||!zaWeMini.para.isPlugin){if(i)try{e=_.extend({type:"event",event_name:e.event_name||e.type,source_id:e.source_id,sdk_type:"xcx",xcx_sdk_source:"wechat",trace_id:e.trace_id,debug:zaWeMini.para.debug?"true":void 0},_.setDefaultValue(e.common),e.infos,e.extendsInfo)}catch(e){}zaWeMini.para.debug&&(logger.info("open_id",e.open_id),logger.info(e)),e=JSON.stringify(e),!0!==zaWeMini.para.noLog&&(_.wxrequest({url:zaWeMini.para.server_url,method:"POST",data:encodeURIComponent(e),success:function(e){},complete:function(){"function"===typeof t&&t()}}),zaWeMini.store.set("isnew",!1),zaWeMini.store.storageInfo.isnew=!1)}},is_first_batch_write:!0,conversionData:function(e){var t=e.properties,i=this.conversionType(e.event),n="custom"===i?_.handleUnfinedValue(t&&t.event_value)||"custom":i,a=getExtendsInfo(n),r=getUTMMixObj(t),o={type:"event",event_name:n,source_id:zaWeMini.para.source_id,trace_id:t.trace_id,common:_.extend({resolution:t.$screen_width+"x"+t.$screen_height,device_model:t.$model,network:t.$network_type,event_time:_.formatDate(new Date,!0),event_timestamp:Date.now(),sdk_version:t.$lib_version,language:t.$language,wx_xcx_version:t.$wx_version,wx_xcx_id:zaWeMini.para.appid,wx_xcx_name:zaWeMini.para.app_name,url:t.$url_path,url_param:t.$url_query||"",title:t.$title||"",refer_url:t.$referrer||"",referrer_title:t.$referrer_title||"",open_id:zaWeMini.store.getStorage().openid,uid:zaWeMini.store.getStorage().uid,session_id:t.session_id,session_time:t.session_time,channel:t.$latest_scene,channel_id:t.$latest_scene_id,device_id:zaWeMini.store.getStorage().openid||zaWeMini.store.getStorage().uid,device_brand:t.$manufacturer,os_type:t.$os,platform_os_version:t.$os_version,accountId:t.accountId||void 0,union_id:zaWeMini.store.getStorage().unionId||t.$unionId},r),infos:this.setInfoByType(t,i,e),extendsInfo:a||{}};return o},conversionProfileData:function(e){var t=e.properties,i={type:"profile",source_id:zaWeMini.para.source_id,event_name:"modify_profile",profile_name:e.type,event_time:_.formatDate(new Date,!0),event_timestamp:Date.now(),sdk_type:"xcx",xcx_sdk_source:"wechat",sdk_version:t.$lib_version,accountId:t.accountId||void 0,device_id:zaWeMini.store.getStorage().openid||zaWeMini.store.getStorage().uid,open_id:zaWeMini.store.getStorage().openid,union_id:zaWeMini.store.getStorage().unionId||t.$unionId,uid:zaWeMini.store.getStorage().uid,properties:e.profile_properties,debug:zaWeMini.para.debug?"true":void 0};return i},conversionType:function(e){return void 0!=zaWeMini._proxy.type[e]?zaWeMini._proxy.type[e]:zaWeMini.para.isPlugin?void 0:"page"},setInfoByType:function(e,t,i){var n={};try{if("share"===t)return{share_title:_.handleUnfinedValue(e.shareTitle||e.$share_title),description:_.handleUnfinedValue(e.shareDesc||e.$share_description),share_from:_.handleUnfinedValue(e.$from),share_method:_.handleUnfinedValue(e.$share_method)};if("click"===t){var a=e.datasets||{},r={};return _.each(Object.keys(a),(function(e){e&&a[e]&&(r[e]=_.handleUnfinedValue(a[e]))})),_.extend({element_content:_.handleUnfinedValue(e.$element_content),element_type:_.handleUnfinedValue(e.$element_type),click_id:_.handleUnfinedValue(a.id)},r)}if("custom"===t){var o=i&&i.originalProperties||{};return _.extend({custom_event_type:_.handleUnfinedValue(e.event_type),custom_event_value:_.handleUnfinedValue(e.event_value),custom_event_description:_.handleUnfinedValue(e.event_description)},o||{})}if("ABTestTrigger"===t){o=i&&i.originalProperties||{};return o}return n}catch(e){return n}}},zaWeMini.setOpenid=function(e){zaWeMini.store.set("openid",e),zaWeMini.store&&zaWeMini.store.storageInfo&&(zaWeMini.store.storageInfo.openid=e)},zaWeMini.setUnionId=function(e){zaWeMini.store.set("unionId",e),zaWeMini.store&&zaWeMini.store.storageInfo&&(zaWeMini.store.storageInfo.unionId=e),zaWeMini.registerApp({$unionId:e||void 0})},_.each(["setProfile","setOnceProfile","track","incrementProfile","appendProfile","deleteProfile","unsetProfile"],(function(e){var t=zaWeMini[e];zaWeMini[e]=function(){zaWeMini.initialState.isComplete?t.apply(zaWeMini,arguments):zaWeMini.initialState.queue.push([e,arguments])}})),_.setQuery=function(e,t){var i="";if(e&&_.isObject(e)&&!_.isEmptyObject(e)){var n=[];return _.each(e,(function(e,i){"q"===i&&_.isString(e)&&0===e.indexOf("http")||"scene"===i||"__key_"===i||(t?n.push(i+"="+e):n.push(i+"="+_.decodeURIComponent(e)))})),n.join("&")}return i},_.getUtmFromPage=function(){var e={};try{var t=getCurrentPages(),i=t[t.length-1].options;e=_.getCustomUtmFromQuery(i,"$","_","$")}catch(e){logger.info(e)}return e},zaWeMini.autoTrackCustom={trackCustom:function(e,t,i){var n=zaWeMini.para.autoTrack[e],a="";if(zaWeMini.para.autoTrack&&n){if("function"===typeof n){if(a=n(),!1===a)return;_.isObject(a)&&_.extend(t,a)}else _.isObject(n)&&(_.extend(t,n),zaWeMini.para.autoTrack[e]=!0);_.setPageRefData(t),zaWeMini.track(i,t)}},appLaunch:function(e,t){"object"!==typeof this||this["trackCustom"]||(this[zaWeMini.para.name]=zaWeMini);var i={};e&&e.path&&(i.$url_path=_.getPath(e.path));var n=_.setUtm(e,i);zaWeMini.setLatestChannel(n.pre2),e.scene=e.scene||"\u672a\u53d6\u5230\u503c",i.$scene=_.getMPScene(e.scene),i.$scene_id=e.scene,zaWeMini.registerApp({$latest_scene:i.$scene,$latest_scene_id:i.$scene_id,$url_query:_.setQuery(e.query)}),t?(i=_.extend(i,t),zaWeMini.track("$MPLaunch",i)):zaWeMini.para.autoTrack&&zaWeMini.para.autoTrack.appLaunch&&zaWeMini.autoTrackCustom.trackCustom("appLaunch",i,"$MPLaunch")},appShow:function(e,t){var i={};mpshow_time=(new Date).getTime(),e&&e.path&&(i.$url_path=_.getPath(e.path));var n=_.setUtm(e,i);zaWeMini.setLatestChannel(n.pre2),e.scene=e.scene||"\u672a\u53d6\u5230\u503c",i.$scene=_.getMPScene(e.scene),zaWeMini.registerApp({$latest_scene:i.$scene,$url_query:_.setQuery(e.query)}),t?(i=_.extend(i,t),zaWeMini.track("$MPShow",i)):zaWeMini.para.autoTrack&&zaWeMini.para.autoTrack.appShow&&zaWeMini.autoTrackCustom.trackCustom("appShow",i,"$MPShow")},appHide:function(e){var t=(new Date).getTime(),i={};i.$url_path=_.getCurrentPath(),mpshow_time&&t-mpshow_time>0&&(t-mpshow_time)/36e5<24&&(i.event_duration=(t-mpshow_time)/1e3),e?(i=_.extend(i,e),zaWeMini.track("$MPHide",i)):zaWeMini.para.autoTrack&&zaWeMini.para.autoTrack.appHide&&zaWeMini.autoTrackCustom.trackCustom("appHide",i,"$MPHide"),zaWeMini.sendStrategy.onAppHide()},pageLoad:function(e){e&&_.isObject(e)&&(this.za_mp_url_query=_.setQuery(e))},pageShow:function(){var e={},t=_.getCurrentPath(),i=_.getPageTitle(t);_.setRefPage(),e.$url_path=t;var n={$url_query:this.za_mp_url_query?this.za_mp_url_query:"",$title:i};_.setPageRefData(n),zaWeMini.registerApp(n),e=_.extend(e,_.getUtmFromPage()),zaWeMini.para.onshow?zaWeMini.para.onshow(zaWeMini,t,this):zaWeMini.para.autoTrack&&zaWeMini.para.autoTrack.pageShow&&zaWeMini.autoTrackCustom.trackCustom("pageShow",e,"$MPViewScreen")},pageShare:function(e){var t=e.onShareAppMessage;e.onShareAppMessage=function(e,i,n){var a=t.apply(this,arguments);return zaWeMini.para.autoTrack&&zaWeMini.para.autoTrack.pageShare&&zaWeMini.autoTrackCustom.trackCustom("pageShare",{$url_path:_.getCurrentPath(),$share_title:a&&a.title||void 0,$share_description:a&&a.description||void 0,$from:e.from,$target:e.target,$share_method:"\u8f6c\u53d1\u6d88\u606f\u5361\u7247"},"$MPShare"),a}},pageShareTimeline:function(e){var t=e.onShareTimeline;e.onShareTimeline=function(e,i,n){var a=t.apply(this,arguments);return zaWeMini.para.autoTrack&&zaWeMini.para.autoTrack.pageShare&&zaWeMini.autoTrackCustom.trackCustom("pageShare",{$url_path:_.getCurrentPath(),$share_title:a&&a.title||void 0,$share_description:a&&a.description||void 0,$from:e.from,$target:e.target,$share_method:"\u670b\u53cb\u5708\u5206\u4eab"},"$MPShare"),a}},pageAddFavorites:function(){var e={};e.$url_path=_.getCurrentPath(),zaWeMini.para.autoTrack&&zaWeMini.para.autoTrack.mpFavorite&&zaWeMini.autoTrackCustom.trackCustom("mpFavorite",e,"$MPAddFavorites")}};var oldApp=App;App=function(e){mp_proxy(e,"onLaunch","appLaunch"),mp_proxy(e,"onShow","appShow"),mp_proxy(e,"onHide","appHide"),oldApp.apply(this,arguments),zaWeMini.para&&zaWeMini.para.adaptIlog4&&adaptIlog4(zaWeMini.para)};var oldPage=Page;Page=function(e){var t=zaWeMini.para.autoTrack&&zaWeMini.para.autoTrack.mpClick&&_.getMethods(e);if(t)for(var i=0,n=t.length;i<n;i++)click_proxy(e,t[i]);zaWeMini.para.autoTrack&&zaWeMini.para.autoTrack.mpClick&&tabProxy(e),mp_proxy(e,"onLoad","pageLoad"),mp_proxy(e,"onShow","pageShow"),mp_proxy(e,"onAddToFavorites","pageAddFavorites"),"function"===typeof e.onShareAppMessage&&zaWeMini.autoTrackCustom.pageShare(e),"function"===typeof e.onShareTimeline&&zaWeMini.autoTrackCustom.pageShareTimeline(e),oldPage.apply(this,arguments)};var oldComponent=Component;Component=function(e){try{var t=zaWeMini.para.autoTrack&&zaWeMini.para.autoTrack.mpClick&&_.getMethods(e.methods);if(t)for(var i=0,n=t.length;i<n;i++)click_proxy(e.methods,t[i]);zaWeMini.para.autoTrack&&zaWeMini.para.autoTrack.mpClick&&tabProxy(e.methods),mp_proxy(e.methods,"onLoad","pageLoad"),mp_proxy(e.methods,"onShow","pageShow"),mp_proxy(e,"onAddToFavorites","pageAddFavorites"),"function"===typeof e.methods.onShareAppMessage&&zaWeMini.autoTrackCustom.pageShare(e.methods),"function"===typeof e.methods.onShareTimeline&&zaWeMini.autoTrackCustom.pageShareTimeline(e.methods),oldComponent.apply(this,arguments)}catch(e){oldComponent.apply(this,arguments)}},zaWeMini.initial(),module.exports=zaWeMini;