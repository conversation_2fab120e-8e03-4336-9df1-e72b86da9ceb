import getChannelInfo from './getChannelInfo';
import config, { __DEBUG__ } from './config';
import { CDN_PREFIX } from './staticData';
import storage from './storage';
import _login_ from './login';
import { setIsBlackUser } from "./getChannelInfo";
import ilog from './ilog';
import selfMiniIlog from './selfLog';
import { clearParams } from './tools';
import Taro from '@tarojs/taro';


var app = getApp();

const __login__ = new _login_();

export const fetch = (options) => {
  let { url } = options;
  const { method, data, needLoading = false, header, needLog = false } = options;
  needLoading && Taro.showLoading({
    title: '加载中',
    mask: true
  })
  const regExp = /\/(.*?)\//;
  const hostkey = url.match(regExp)[1];
  const baseUrl = config.baseAPI[hostkey].host;
  url = url.replace(regExp, '/');
  needLog && console.log('请求开始---', url, data);
  return Taro.request({
    url: `${baseUrl}${url}`,
    method: method,
    header: {
      'token': storage.get('Token') || '',
      'platform': 'wechatapp',
      'content-type': 'application/json',
      'channelOrigin': storage.get('channelOrigin') || '',
      ...header
    },
    data
  }).then(resData => resHandler(resData, options))
    .catch(error => errorHandler(error, options));
}

export async function fetchJson(options) {
  const token = storage.get('Token') || '';
  const { needLogin = true, data = {} } = options;

  if (needLogin && token === '') {
    if (!__login__.status) {
      __login__.init(__login__.status);//登录
    }
    __login__.queueList.push(() => {//保存请求队列
      return fetch(options);
    });
    return Promise.resolve({ code: 9999 });
  }
  return fetch(options);
}

// 请求成功处理
function resHandler(resData, options) {
  const { needLoading = false, payload = '', needLog = false } = options;
  /**
   * returnCode和payState是针对对接椋鸟健康两个奇葩接口添加的兼容字段。
   */
  const { data: { code = '', returnCode = '', payState = '', success = false } = {}, data = {}, content = {}, statusCode = 0, } = resData;
  needLog && console.log('请求完成---', resData, options);
  //针对是否注册接口单独判断
  if (payload.checkRegister) {
    options.success && options.success(data);
    needLoading && Taro.hideLoading()
    return Promise.resolve(data);
  }
  if (statusCode && statusCode !== 200) {
    return errorHandler(resData, options, statusCode);
  }
  if ((parseInt(code || returnCode, 10) !== 0) && !success) {
    return errorHandler(resData, options, statusCode);
  }
  options.success && options.success(data || content || payState);
  needLoading && Taro.hideLoading()
  return Promise.resolve(data || content || payState);
}

// 异常处理
function errorHandler(error, options) {
  const { needLoading = true, needToast = true, needLog = false } = options;
  const { data = {}, data: { message = '请求错误 !', code = '' } = {} } = error;
  selfMiniIlog.error({ ...(error || {}), options, msg: '微信小程序接口报错了' });
  needLog && console.log('请求报错---', options, error);
  options.error && options.error(data);
  needLoading && Taro.hideLoading()
  global._SERAPH_.sendCustomEvent({
    name: 'FETCH_ERROR',
    source: JSON.stringify({ alert: `接口请求异常 ${code}`, options, result: (error || {})}),
    level: 'error',
  });
  if (code === "100016") {
    Taro.navigateTo({
      url: './error'
    });
    return Promise.reject(error);
  }
  needToast && Taro.showToast({
    icon: 'none',
    title: message
  })
  return Promise.reject(error);
}
//特殊：车险渠道进入时提交url信息给后台
const getUrlSomeChannelBiz = () => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1] || {};
  let { options: { userId, sign } = {} } = currentPage;
  if (userId) {
    let option = {
      sign,
      thirdUserId: userId,
    };
    storage.set('someChannelbiz', option, 5);
    return option;
  }
  return {};
}
export async function login() {
  const openId = await getOpenId();
  if (!openId) {
    Taro.showToast({
      title: '登陆小程序失败，请稍后再试',
      icon: 'none',
    })
    return
  }
  const token = await getToken();
  if (!token) {
    return
  }
  return getAccountInfo();
}

export async function queryChannelByResourceCode(params = {}) {
  const { toggleAccountChannelResource } = params;
  const res = await fetchJson({
    url: '/zaApi/v1/patient/channel/queryChannelByResourceCode',
    method: 'GET',
    needLoading: true,
    needLogin: false,
    data: {
      channelResourceCode: toggleAccountChannelResource || getChannelInfo.channelResourceCode(),
    }
  });
  if(res.code === '0') {
    const { isWechatApplet } = res.result || {}
    if(isWechatApplet === 'N') {
      storage.set('channelResourceCode', 'HYXCX');
    }
  }
}

export async function bindUserInfo(data) {
  //eventType 2: 绑定用户信息
  const { encryptedData, iv, eventType = 2 } = data;
  const checkSession = await new Promise(resolve => Taro.checkSession({ complete(res) { resolve(res.errMsg) } }));
  if (checkSession !== 'checkSession:ok' || !storage.get('openId') || !storage.get('openCode')) {
    await getOpenId();
  }
  const specifyBusinessParameters = storage.get('someChannelbiz') || {};
  const res = await fetchJson({
    url: '/zaApi/v1/patient/user/bindThirdUser',
    method: 'POST',
    needLoading: true,
    needLogin: false,
    data: {
      channelResourceCode: getChannelInfo.channelResourceCode(),
      channelSource: getChannelInfo.channelSource(),
      requestData: JSON.stringify({
        eventType,
        encryptedData,
        iv,
      }),
      openId: storage.get('openId'),
      unionId: storage.get('unionId'),
      code: storage.get('openCode'),
      isUnitedLogin: false,
      ...specifyBusinessParameters,
    }
  });

  if (res && res.result && res.code === '0') {
    storage.remove('someChannelbiz');
    const { result: { userBlackFlag = false, patientDomain: { patientName = '', patientCertNo = '' } = {} } = {} } = res;
    storage.set('userRealInfo', {
      patientName,
      patientCertNo,
    });
    setIsBlackUser(userBlackFlag);
    const userInfo = await getAccountInfo();
    return Promise.resolve(userInfo);
  }
  return null;
}

export async function bindUserPhone(data) {
  //eventType 1 : 绑定手机号
  const { encryptedData = 1, iv, eventType, toggleAccountChannelResource } = data;
  const checkSession = await new Promise(resolve => Taro.checkSession({ complete(res) { resolve(res.errMsg) } }));

  if (checkSession !== 'checkSession:ok' || !storage.get('openId') || !storage.get('openCode')) {
    await getOpenId();
  }
  await queryChannelByResourceCode({ toggleAccountChannelResource });
  const specifyBusinessParameters = storage.get('someChannelbiz') || {};
  const res = await fetchJson({
    url: '/zaApi/v1/patient/user/bindThirdUser',
    method: 'POST',
    needLoading: true,
    needLogin: false,
    data: {
      channelResourceCode: toggleAccountChannelResource || getChannelInfo.channelResourceCode(),
      channelSource: getChannelInfo.channelSource(),
      requestData: JSON.stringify({
        eventType,
        encryptedData,
        iv,
      }),
      openId: storage.get('openId'),
      unionId: storage.get('unionId'),
      code: storage.get('openCode'),
      isUnitedLogin: false,
      ...specifyBusinessParameters
    }
  });
  if (res.code !== '0') {
    return;
  }
  const { result: { token = '', userBlackFlag = false, bizMap: { realName = '1', maskActId = "", maskusId = "" } = {}, patientDomain: { patientName = '', patientCertNo = '', accountId = '', userId = '' } = {} } = {} } = res;
  if (realName === '1') {
    storage.set('realName', 'N');
  } else if (realName === '2') {
    storage.set('realName', 'Y');
  } else if (realName === '3') {
    storage.set('realName', 'NY');  //存在多个渠道的实名信息，进入实名页面让用户选择，不需要填写
  }
  setIsBlackUser(userBlackFlag);
  if (token) {
    storage.remove('someChannelbiz');
    storage.set('userRealInfo', {
      patientName,
      patientCertNo,
    });
    toggleAccountChannelResource && storage.set('channelResourceCode', toggleAccountChannelResource);
    storage.set('Token', token, 90); //token设置有效时间为90分钟
    storage.set('Long_Token', token, 60 * 12); //long_token设置有效时间为12小时
    storage.set('maskActId', maskActId);
    storage.set('maskusId', maskusId);
    storage.set('accountId', accountId);
    storage.set('userId', userId);
    ilog.setConfig({
      ZAHLWYY_accountId: maskActId,
      ZAHLWYY_userId: maskusId,
      ZAHLWYY_channelSource: getChannelInfo.channelSource(),
      ZAHLWYY_channelResourceCode: getChannelInfo.channelResourceCode(),
      ZAHLWYY_openId: storage.get('openId'),
      ZAHLWYY_channelOrigin: storage.get('channelOrigin'),
      ZAHLWYY_channelCode: storage.get('channelCode'),
    });
    ilog.start();
    ilog.setLogin(maskusId);
    await getAccountInfo();
  }
}

//小程序存在不同渠道的概念，对于后端来说，不同渠道的登陆态是完全独立，所以checkSession
async function getOpenId(options = {}) {
  //联合登陆 【目前只有尊享，这个时候从options里再取一次参数】
  const { channelSource: _optionChannelSource = '', channelResourceCode: _optionChannelResourceCode = '' } = options;
  const code = await new Promise(resolve => Taro.login({ success(res) { resolve(res.code) } }));

  console.log('～～～～～～～～～获取openId了～～～～～～～');
  console.log(getChannelInfo.channelResourceCode(), getChannelInfo.channelSource());
  console.log(_optionChannelResourceCode, _optionChannelSource);

  if ((!_optionChannelResourceCode && _optionChannelSource) || !getChannelInfo.channelResourceCode()) {
    await getChannelResourceCode(_optionChannelSource);
  }

  const res = await fetchJson({
    url: '/zaApi/v1/patient/user/getThirdOpenId',
    method: 'POST',
    needLogin: false,
    needLog: true,
    data: {
      channelResourceCode: getChannelInfo.channelResourceCode() || '',
      channelSource: getChannelInfo.channelSource() || '',   //这里要保留channelSource的请求，因为app里面queryChannelResourceBySource是异步的。
      requestData: JSON.stringify({
        channelType: 'applet',
        code: code
      }),
    }
  });

  const { result: { openId = '', unionId = '' } = {} } = res;
  if (openId) {
    storage.set('openId', openId, 0);
    storage.set('unionId', unionId, 0);
    storage.set('openCode', code, 0);
    ilog.setOpenId(openId);
  }
  return openId;
}

async function getChannelResourceCode(optionChannelSource) {
  const res = await fetchJson({
    type: "GET",
    url: `/zaApi/v1/patient/channel/queryChannelResourceBySource?channelSource=${optionChannelSource || getChannelInfo.channelSource()}`,
    isloading: false,
    needLogin: false,
  });

  if (res.code === '0') {
    const { result: { resourceCode = '', isWechatApplet = '' } = {} } = res;
    if (resourceCode) {
      storage.set('channelResourceCode', isWechatApplet === 'N' ? 'HYXCX' : resourceCode);
    }
  }
}

//根据openId和渠道编号得到当前渠道的token 【时效：90分钟】
//尊享小程序联合免登录扩展此方法

async function getToken(options = {}) {
  const { channelSource = '', openId = '', token: oldtoken = '', thirdPlatformToken = '', channelResourceCode = '', authCode = '', wbAuthCode = '' } = options;
  // thirdPlatformToken 新渠道寿险&互医—小程序388权益接入 项目直接跳首页添加的字段 ，与autologin页的 token逻辑一致
  const specifyBusinessParameters = getUrlSomeChannelBiz();
  const res = await fetchJson({
    url: '/zaApi/v1/patient/user/bindThirdUser',
    method: 'POST',
    data: {
      isUnitedLogin: false,
      ...options,
      channelResourceCode: channelResourceCode || getChannelInfo.channelResourceCode(),
      channelSource: channelSource || getChannelInfo.channelSource(),
      openId: Taro.getSystemInfoSync().inFinChat ? authCode : (openId || storage.get('openId')),
      unionId: storage.get('unionId'),
      code: storage.get('openCode'),
      token: oldtoken || thirdPlatformToken,
      wbAuthCode,
      ...specifyBusinessParameters,
      t: Date.now()
    },
    needLogin: false,
    needLog: true,
    payload: {
      checkRegister: true
    }
  });
  console.log('/zaApi/v1/patient/user/bindThirdUser', res);
  if (res.code !== '0') {
    return;
  }
  const { result: { token = '', userBlackFlag = false, bizMap: { isFirstDrawTrialServpack = 'N', realName = '1', maskActId = "", maskusId = "" } = {}, patientDomain: { patientName = '', patientCertNo = '', userId = '', accountId = '' } = {} } = {} } = res;
  if (realName === '1') {
    storage.set('realName', 'N');
  } else if (realName === '2') {
    storage.set('realName', 'Y');
  } else if (realName === '3') {
    storage.set('realName', 'NY');  //存在多个渠道的实名信息，进入实名页面让用户选择，不需要填写
  }
  if (token) {
    storage.set('userRealInfo', {
      patientName,
      patientCertNo,
    });
    storage.set('isFirstDrawTrialServpack', isFirstDrawTrialServpack);
    storage.set('Token', token, 90); //token设置有效时间为90分钟
    storage.set('Long_Token', token, 60 * 12); //long_token设置有效时间为12小时
    storage.set('maskActId', maskActId);
    storage.set('maskusId', maskusId);
    storage.set('userId', userId);
    storage.set('accountId', accountId);
    setIsBlackUser(userBlackFlag);
    ilog.setConfig({
      ZAHLWYY_accountId: maskActId,
      ZAHLWYY_userId: maskusId,
      ZAHLWYY_channelSource: getChannelInfo.channelSource(),
      ZAHLWYY_channelResourceCode: getChannelInfo.channelResourceCode(),
      ZAHLWYY_openId: storage.get('openId'),
      ZAHLWYY_channelOrigin: storage.get('channelOrigin'),
      ZAHLWYY_channelCode: storage.get('channelCode'),
    });
    ilog.start();
    ilog.setLogin(maskusId);
    // 六翼上报接口只接收字符串类型的userId
    global._SERAPH_.setUser({ userId: `${userId}` });
  }
  return options.isUnitedLogin ? (res.result || { token }) : token;
}

//获取用户数据
export async function getAccountInfo() {
  const res = await fetchJson({
    url: '/zaApi/v1/patient/user/getAccountInfo',
    method: 'GET',
    needLoading: true,
    needLog: true,
  });
  const { result: { accountPhone = '', userDomain: { id, userHeadPortrait = `${CDN_PREFIX}default_avatar.png`, userNickname = '微信用户' } = {} } = {} } = res;
  storage.set('userInfo', { accountPhone, userHeadPortrait, userNickname, id }, 0);
  return accountPhone
}

//token过期的时候 刷新token
export async function refreshToken(long_token) {
  const res = await fetch({
    url: '/zaApi/v1/patient/user/refreshToken',
    method: 'GET',
    header: {
      token: long_token
    },
    data: {
      token: long_token
    },
  });

  if (res.code === '0' && res.result) {
    storage.set('Token', res.result, 90);
    storage.set('Long_Token', res.result, 60 * 12);
    return res.result
  } else {
    return ''
  }
}

//尊享小程序联合免登录
export async function unionLogin(options = {}) {
  const filterOptions = clearParams(options);
  if(!Taro.getSystemInfoSync().inFinChat) {
    const openId = await getOpenId(filterOptions);
    filterOptions.openId = openId;
  }
  const result = await getToken(filterOptions);
  await getAccountInfo();
  return result;
}

export default fetchJson;
