/*
 * @description： 专科中心首页 - 引导加企微页
 */

import { CDN_PREFIX } from '../staticData';

// homeType 1 -> 母婴，2 -> 专科
const titleprefixCls = '添加众安健康顾问微信 邀您进入';
const rules = [
  '1. 点击下方【去加众安健康顾问微信】',
  '2. 在对话页面点击右下方图片发送即可添加众安健康顾问微信',
];
const config = {
  1: {
    className: 'mother',
    pageTitle: '准妈妈微信群',
    title: titleprefixCls,
    rules,
  },
  /* 男科 */
  2: {
    className: 'andrology',
    pageTitle: '男性保健微信群',
    title: titleprefixCls,
    rules,
  },
  welfare: {
    rules,
    pageTitle:'专家视频问诊',
    modalTitle: '添加众安健康顾问 享三重福利',
    welfareList: [
      {
        title: '专业医生为您',
        title2: '免费预诊',
        desc: '分析评估病情',
        bg: 'welfare-bg01.png',
      },
      {
        title: '免费全程答疑',
        title2: '视频问诊',
        desc: '相关问题都能问',
        bg: 'welfare-bg02.png',
      },
      {
        title: '视频问诊前',
        title2: '10分钟提醒',
        desc: '让您省心放心',
        bg: 'welfare-bg03.png',
      },
    ],
  },
};

// mother -> 母婴，andrology -> 专科
export const homeComponentConfig = {
  1: {
    className: 'mother',
    bg: `${CDN_PREFIX}specialist/mother-bg.png`,
    sexIcon: `${CDN_PREFIX}specialist/mother-sex.png`,
    departmentTitle: '母婴健康',
    title: '美妈请进',
    desc: '您的专属孕育医管家',
    url: '/hospital/specialist/mother/home',
    frontColor:'#ffffff',
    bgColor:'#ff8a9c',
    xflow:{
      eventTag:'ZAHLWYY_SY',
      text:'首页',
      attributes:'首页_母婴健康'
    }
  },
  /* 男科 */
  2: {
    className: 'andrology',
    bg: `${CDN_PREFIX}specialist/andrology-bg.png`,
    sexIcon: `${CDN_PREFIX}specialist/andrology-sex.png`,
    departmentTitle: '男性健康',
    title: '解决难言之隐',
    desc: '做幸福男人',
    url: '/hospital/specialist/andrology/home',
    frontColor:'#ffffff',
    bgColor:'#8bb5f5',
    xflow:{
      eventTag:'ZAHLWYY_SY',
      text:'首页',
      attributes:'首页_男性健康'
    }
  },
};

export default config;
