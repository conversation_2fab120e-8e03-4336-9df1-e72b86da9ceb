/*
 * @description：静态数据 统一维护
 */
import config from "./config";

/**
 *  实名状态
 *  pending: 等待状态
 *  N: 未实名
 *  Y: 实名完成
 */
export const REAL_STATUS = {
  pending: 'pending',
  noAuth: 'N',
  auth: 'Y',
};


export const CDN_PREFIX = 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/applets/';

export const zajkAppid = 'wxbac45cc1588a5a75'; //众安健康小程序appid

/*第三方对接的小程序来源标识 统一管理*/
export const THIRD_RESOURCE_APPLET_CODE = {
  ZABX: 'ZABX', // 众安保险小程序
  ZAJKX: 'ZAJKX', // 众安健康险小程序
};

/*第三方对接的来源标识 统一管理*/
export const THIRD_PLATFORM_RESOURCECODE = {
  ZAHY: 'ZA', // 众安互医 、在药品订单内 platformCode = ZA 表示 开药方为国药
  MBJK: 'MB', // 妙保 健康
  LHYW: 'UMP', // UMP联合医务
  SYY_DRUGSTROE: 'SYY', // 上药云药房
  SYY_MB: 'SYYMB', // 上药云药房 - 慢病
  SYY_XB: 'SYYXB', // 上药云药房 - 小病0元治
  SYY_SPHCHINA: 'SPHCHINA', // 上药云药房 - 问诊开药
  GJSC: 'GJ', // 高济商城
  XLZY: 'XL', // 小鹿中医
  SSPS_PET: 'HCB', // 山水鹏狮 荟宠宝
  YDL_MENTALRELEASE: 'YDL', // 壹点灵 心理倾诉
  YDL_HEARTASSESS: 'YDL_HEARTASSESS', // 壹点灵 心理测评
  GLZ_VACCINE: 'GLZ', // 橄榄枝健康疫苗
  WEIYI_SERVICE: 'WEIYI', // 微医预约挂号服务
  UNION_DRUG: 'UNION_DRUG', //药联
  KYUSHU_POP: 'POP', // 九州通
  ZA_MALL: 'ZA_MALL', //众安生命药品商城
  ZA_HAOYAOSHI: 'ZA_HAOYAOSHI',
  SZ: 'SZ' //善诊
};

/**
 * @description 互医中间跳板页面referrer映射
 * 映射固化了页面的标题，跳转按钮文字，跳转路径等数据
 */
export const MiddleBridgePageReferrer = {
  /**
   * @description 健康险小程序618活动，跳板页面固化的参数
   */
  health618: {
    title: '做有温度的互联网医院',
    btnTxt: '进入活动主会场',
    thirdMiniAppletOptions: {
      appId: 'wxbac45cc1588a5a75',
      path: 'pages/activity/618/index',
      successExtUrl: ''
    }
  }
}

export const ZA_MALL_H5_URL = {
  dev: 'https://mall-test.za-doctor.com',
  uat: 'https://mall-uat.za-doctor.com',
  prd: 'https://mall.za-doctor.com',
}[config.ENV]
