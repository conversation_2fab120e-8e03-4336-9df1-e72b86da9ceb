export const TransformObject = (list = [], key = 'value', value = 'label') => {
  const res = {};
  return list.reduce((prev, elem) => {
    prev[elem[key]] = elem[value];
    return prev;
  }, res);
};

export const Deserialize = (value = '', firstCon = '&', secondCon = '=') => {
  let result = {};
  return value.replace('?', '').split(firstCon).reduce((prev, elem) => {
    const index = elem.indexOf(secondCon);
    if (index === -1) {
      prev[elem] = '';
    } else {
      prev[elem.slice(0, index)] = elem.slice(index + 1);
    }
    return prev;
  }, result);
};

export const Serialize = (data, firstCon = '&', secondCon = '=') => {
  return Object.keys(data).reduce((prev, key) => {
    const value = data[key]
    if (value != null) {
      prev += (prev && firstCon) + key + (String(value) && secondCon + value)
    }
    return prev
  }, '')
};

export const appendUrl = (url, str = '') => {
  if(!str) {
    return url
  }
  if (url.indexOf('?') === -1) {
    return url + '?' + str
  }
  return url + '&' + str
}


export const TransformEnumArray = (list = [], value = 'value', label = 'label') => {
  const res = [];
  return list.reduce((prev, elem) => {
    prev.push({ value: elem[value], label: elem[label] });
    return prev;
  }, res);
};

export const TransfromObjectToArray = (obj) => {
  if (typeof obj !== 'object') {
    return []
  }
  const res = [];
  Object.keys(obj).forEach(i => {
    res.push(obj[i]);
  })
  return res;
}

