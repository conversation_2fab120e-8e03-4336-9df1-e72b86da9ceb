import { fetchJson } from "./fetch";
import format from './format';

//获取用户数据
/**
 * 获取分享信息的异步函数
 * @param {string} path - 分享路径
 * @param {Object} defaultShare - 默认分享配置
 * @returns {Object} 分享信息对象
 */
export async function getShareInfo(path, defaultShare) {
    // 发送请求获取分享配置详情
    const res = await fetchJson({
        url: '/zaApi/v1/console/applet/share/config/detail',
        method: 'POST',
        needLoading: true,  // 显示加载状态
        needLogin: false,   // 不需要登录即可访问
        data: {
            path
        }
    });
    
    // 请求成功且有返回结果时处理数据
    if (res.code === '0' && res.result) {
        // 使用解构赋值获取需要的字段，并提供默认值防止空对象报错
        const {
            showName, attachmentDomain: { attachmentDownloadUrl } = {}
        } = res.result || {};
        
        return {
            __special__: true,  // 特殊标记，表示这是从API获取的分享信息
            path,
            title: showName || '与众同行，让服务更有温度！',  // 如果没有showName则使用默认标题
            imageUrl: attachmentDownloadUrl ? format.imageUrl(attachmentDownloadUrl) : 'https://static.za-doctor.com/common/share.png',  // 处理图片URL
        }
    }
    
    // 请求失败或无结果时返回默认分享配置
    return defaultShare ? defaultShare : {
        title: '与众同行，让服务更有温度！',
        imageUrl: 'https://static.za-doctor.com/common/share.png',
        path: '/pages/index'  // 默认首页路径
    };
}