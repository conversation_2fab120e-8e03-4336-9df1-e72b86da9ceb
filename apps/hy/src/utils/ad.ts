import { fetchJson } from './fetch';
import { Deserialize, } from './serialization';
import Taro from '@tarojs/taro';

const app = getApp();

export const onAdClick = ({ adRequestUrl, adType, adName, adPosition, adNo, appId, extraData }) => {
    pushXFlowEvent({ adPosition, adName })
    console.log('adPosition', adPosition, adNo)
    //检查该广告位下是否有自动发放的服务包
    fetchJson({
        url: '/zaApi/v1/patient/servpack/list',
        method: 'POST',
        data: {
            adPosition: adPosition,
            adNo: adNo
        },
        isloading: true,
    }).then(res => {
        if (res.code === '0' && res.result) {
            //如果有服务包,则去对应页面处理逻辑,该页面需要登录，以此确保发放服务包时为登录状态
            if (res.result.length > 0) {
                const servpackCodeList = res.result.map(item => item.servpackCode).join(',');
                Taro.navigateTo({
                    url: `/pages/webview/index?src=${encodeURIComponent(`/hospital/middlepage/autoservpack?adType=${adType}&adPosition=${adPosition}&adNo=${adNo}&servpackCodeList=${servpackCodeList}&adRequestUrl=${encodeURIComponent(adRequestUrl)}`)}`,
                });
            } else {
                pushUrl({ adType, adRequestUrl, appId, extraData })
            }
        }
    })
}
export const pushUrl = ({ adType, adRequestUrl, appId, extraData }) => {
    // 1：图片仅展示类、2：图片h5可跳转类、3：小程序跳转类
    if (adType.toString() === '2') {
        if (/za-doctor/.test(adRequestUrl)) {
            const [src = '', searchs = ''] = adRequestUrl.split('?');
            const param = searchs ? Deserialize(searchs) : {};
            Taro.navigateTo({
                url: `/pages/webview/index?src=${encodeURIComponent(src)}&param=${JSON.stringify(param)}`,
            });
            return;
        } else {
            Taro.navigateTo({
                url: `/pages/webview/index?src=${encodeURIComponent(adRequestUrl)}`,
            });
        }
    }
    if (adType.toString() === "3") {
        if (appId === 'wx3c564538ea8e3905') {
            Taro.navigateTo({
                url: adRequestUrl,
            });
        } else {
            Taro.navigateToMiniProgram({
                appId: appId,
                path: adRequestUrl,
                envVersion: 'trial',
                extraData: extraData,
            });
        }
    }
}
export const redirectUrl = ({ adType, adRequestUrl, appId, extraData }) => {
    // 1：图片仅展示类、2：图片h5可跳转类、3：小程序跳转类
    if (adType.toString() === '2') {
        if (/za-doctor/.test(adRequestUrl)) {
            const [src = '', searchs = ''] = adRequestUrl.split('?');
            const param = searchs ? Deserialize(searchs) : {};
            Taro.redirectTo({
                url: `/pages/webview/index?src=${encodeURIComponent(src)}&param=${JSON.stringify(param)}`,
            });
            return;
        } else {
            Taro.redirectTo({
                url: `/pages/webview/index?src=${encodeURIComponent(adRequestUrl)}`,
            });
        }
    }
    if (adType.toString() === "3") {
        if (appId === 'wx3c564538ea8e3905') {
            Taro.redirectTo({
                url: adRequestUrl,
            });
        } else {
            Taro.navigateToMiniProgram({
                appId: appId,
                path: adRequestUrl,
                envVersion: 'trial',
                extraData: extraData,
            });
        }
    }
}
// XFLOW埋点
export const pushXFlowEvent = ({ adPosition, adName }) => {
    if (adPosition === 'banner') {
        app.za.customTrack({
            event_type: 'click',
            event_value: 'ZAHLWYY_SY',
            event_description: '首页',
            ZAHLWYY_CLICK_CONTENT: `首页_banner${adName}`,
        });
    }
}
