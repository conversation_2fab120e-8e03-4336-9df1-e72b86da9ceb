import storage from './storage';

const getChannelInfo = {
  channelSource: () => {
    return storage.get('channelSource') || '';
  },
  channelResourceCode: () => {
    return storage.get('channelResourceCode') || '';
  }
}

// 处理用户是否为黑名单逻辑
export const getIsBlackUser = () => {
  const userBlackFlag = storage.get('userBlackFlag');
  return userBlackFlag === '1' ? true : false;
}

export const setIsBlackUser = (userBlackFlag = false) => {
  return storage.set('userBlackFlag', userBlackFlag ? '1' : '0', 90);
}

export default getChannelInfo;
