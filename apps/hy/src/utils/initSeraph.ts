import config from "./config";
import MonitorJS from "./seraph";
import storage from "./storage";

/**
 * 监控sdk初始化
 * @attention 一定要在app.js初始化前，完成监控方法的调用，不然js-error和promise错误无法监控
 */
export default function initSeraph (options) {
  // 非常莫名其妙，wx.getCurrentPages 变成了undefined，估计是那个三方sdk搞的鬼。
  wx.getCurrentPages = getCurrentPages;
  global._SERAPH_ = new MonitorJS({
    seraphId: '0f231f6f0f89e653',  // 站点ID，接入管理中申请的站点ID
    source: 'miniapp',  // 来源，默认miniapp
    env: config.ENV,  // 默认为prd。test-测试，pre-预发，prd-生产
    collectJsError: true,  // 默认开启js和promise 错误信息采集
    collectPerformance: true,  // 默认开启performance 错误信息采集
    collectRequestPref: true,  // 默认开启request\uploadFile\uploadFile信息采集
    debug: false,
    extendsInfo: {
      // 扩展信息 可选
      pageInfo: '众安互联网医院-微信小程序',
    },
    getDynamic: () => {
      return {
        ZAHLWYY_openId: storage.get('openId'),
        ZAHLWYY_userId: storage.get('userId'),
        ZAHLWYY_channelSource: storage.get('channelSource'),
        ZAHLWYY_channelResourceCode: storage.get('channelResourceCode'),
        ZAHLWYY_channelOrigin: storage.get('channelOrigin'),
        ZAHLWYY_channelCode: storage.get('channelCode'),
      }
    },
    ignoreUrls: [
      // 过滤所有xflow请求：https://zhongan-xflow-nginx.zhongan.com/cloud_xcx_sdk
      /^https:\/\/zhongan-xflow-nginx\.zhongan\.com\/cloud_xcx_sdk/,
    ]
  });

  const userId = storage.get('userId');
  if (userId) {
    // 六翼上报接口只接收字符串类型的userId
    global._SERAPH_.setUser({ userId: `${userId}` });
  }
}