export function throttle(fn, interval) {
  var enterTime = 0;//触发的时间
  var gapTime = interval || 600;//间隔时间，如果interval不传，则默认300ms
  return function (...rest) {
    var context = this;
    var backTime = new Date();//第一次函数return即触发的时间
    if (backTime - enterTime > gapTime) {
      // console.log(...arguments[0]);
      // typeof arguments[0] === 'string' ? fn.call(context, arguments[0]) : fn.call(context, { ...arguments[0] });
      fn.apply(context, rest);
      enterTime = backTime;//赋值给第一次触发的时间，这样就保存了第二次触发的时间
    }
  };
}
