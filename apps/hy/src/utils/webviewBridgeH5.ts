/* 小程序与h5的通信  */

const ACTION_STATUS = {
  SET_NAVIGATION_BAR_COLOR: 'SET_NAVIGATION_BAR_COLOR', //设置webview页的导航颜色
};

//小程序 webview页面 导航颜色设置
const setWebViewNavigationBarColor = ({ frontColor = '#000000', bgColor = '#ffffff' }) => {
  wx &&
    wx.setNavigationBarColor &&
    wx.setNavigationBarColor({
      frontColor: frontColor,
      backgroundColor: bgColor,
      success: (res) => {
        console.log(bgColor, frontColor);
      },
      fail: (err) => {
        console.log(err);
      },
    });
};

const webviewBridgeH5 = (e) => {
  let { action = '', data = {} } = e || {};
  switch (action) {
    case ACTION_STATUS.SET_NAVIGATION_BAR_COLOR:
      setWebViewNavigationBarColor(data);
      return;
  }
};
export default webviewBridgeH5;
