import config, { __DEBUG__ } from './config';
import storage from './storage';
import Taro from '@tarojs/taro';

class __selfMiniIlog__ {
  systemInfo = {};
  init() {
    try {
      this.systemInfo = Taro.getSystemInfoSync();
    } catch (error) {}
  }
  fetchLogReport(className, data) {
    const envKey = `${config.ENV}`;
    try {
      wx.request({
        url: `https://qhebvsiu.lc-cn-n1-shared.com/1.1/classes/${className}`,
        method: 'POST',
        header: {
          'platform': 'wechatapp',
          'content-type': 'application/json',
          'X-Avoscloud-Application-Id': 'qhEBvsIu636xMYnT4TleiyP7-gzGzoHsz',
          'X-Avoscloud-Application-Key': 'aPtW0V0GCa3eXmdFldtxRiOh',
        },
        data: {
          resourceCode: (storage.get('channelResourceCode')||'').substring(2),
          // isReal: cookies.getIsRealAuth(),
          envKey,
          token: (storage.get('Token')||'').slice(0,4),
          longToken: (storage.get('Long_Token')||'').slice(0,4),
          referer: '微信小程序',
          systemInfo:this.systemInfo,
          data,
        },
      });
    } catch (error) {}
  }
  error(data) {
    this.fetchLogReport('ERROR', {
      ...data,
    });
  }
  debug(data) {
    this.fetchLogReport('DEBUG', {
      ...data,
    });
  }
}
export default new __selfMiniIlog__();
