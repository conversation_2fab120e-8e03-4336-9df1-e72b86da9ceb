// 医生回复通知模板
export const DOCTOR_MSG_TEMPLATE_ID = 'pCh6OMGqyVssY3YX0hftrtfy2HcCYUcn-R4O8YL8hpM';
// 视频问诊
export const video_TEMPLATE_ID = 'dabL3sZ4i5QFvrKuA_qwKy4612S9-6VkgBy2A2ql16A';
// 图文问诊？
export const TEXT_TEMPLATE_ID = 'MKqlB4hLfWhm03A1j2FKUW5yxCz_8qFxYgbFGqtsi8U';

export const templateIdTips = {
  [DOCTOR_MSG_TEMPLATE_ID]: {
    title: '家庭医管家回复提醒',
    tip: '医管家回复后，您将收到提醒',
  },
  [video_TEMPLATE_ID]: {
    title: '处方开具提醒',
    tip: '医生开具处方后，您将收到提醒',
  },
  [TEXT_TEMPLATE_ID]: {
    title: '图文问诊提醒',
    tip: '图文问诊医生接诊后，您将收到提醒',
  },
}

const templateIds = [];

for (let templateId in templateIdTips) {
  templateIds.push({ id: templateId, ...templateIdTips[templateId]})
}

export { templateIds }
