import { login, refreshToken } from './fetch';
import config, { __DEBUG__ } from './config';
import storage from './storage';

class __login__ {
  loginTryNum = 0; //尝试登录的次数，
  maxTryNum = 3;//请求登录尝试的上限
  status = 0;//登录状态：0=否，1=登录中;
  queueList = []; //请求队列
  init(status) {//请求初始加载
    this.status = status;
    if (this.status == 1) {
      console.log(`正在登录中……，请求已加入队列`, status);
      return;
    };
    const token = storage.get('Token') || '';
    const long_token = storage.get('Long_Token') || '';
    if (!token && long_token) {
      this.status = 1;
      this.refresh(long_token);
    } else if (!token && !long_token) {
      this.status = 1;
      this.relogin();
    }
  }
  //重新登录
  relogin() {
    this.loginTryNum += 1;
    storage.remove('Token') || '';
    storage.remove('Long_Token') || '';
    login().then((accountPhone) => {
      accountPhone && this.queueTransfer();
    });
  }
  //刷新token 免登陆
  refresh(long_token) {
    this.loginTryNum += 1;
    refreshToken(long_token).then((token) => {
      token && this.queueTransfer();
    })
  }
  queueTransfer() {
    //登录完成后的请求队列处理
    console.log("处理请求队列", this.queueList.length);
    if (this.queueList && this.queueList.length > 0) {
      var itemQueue = this.queueList.shift();
      this.queueList.length && this.queueTransfer();
      if (typeof itemQueue === 'function') {
        itemQueue();
      };

      if (this.queueList.length <= 1) {
        this.status = 0;
      }
    }
  }

}

export default __login__;

