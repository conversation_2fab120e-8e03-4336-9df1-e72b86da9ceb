var za = require("./za-sdk.min.js");
class Ilog {
  isInit = false; //ilog如果初始化过一次之后，就不能重新init了
  config() { //初始化配置
    za.setPara({
      app_name: "众安互联网医院",
      appid: "wx3c564538ea8e3905",
      source_id: "z7oeetwqzs6jwvec",
      datasend_timeout: 6000,
      server_url: 'https://zhongan-xflow-nginx.zhongan.com/cloud_xcx_sdk',
    });
  }
  setConfig(data) {
    za.setPara({
      app_name: "众安互联网医院",
      appid: "wx3c564538ea8e3905",
      source_id: "z7oeetwqzs6jwvec",
      datasend_timeout: 6000,
      server_url: 'https://zhongan-xflow-nginx.zhongan.com/cloud_xcx_sdk',
      extendsInfo: {
        ...data
      }
    });
  }
  setLogin(userId) {
    var app = getApp();
    app.za.setLogin(userId);
  }
  setOpenId(openId){
    za.setOpenid(openId);
    this.setConfig({
      ZAHLWYY_openId: openId,
    });
  }
  start() {
    if (!this.init) {
      this.isInit = true;
      za.init();
    }
  }
}


export default new Ilog();
