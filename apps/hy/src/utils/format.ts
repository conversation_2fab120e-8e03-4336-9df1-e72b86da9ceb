import config from './config';
import Taro from '@tarojs/taro';


// 是否为心理咨询单问诊
export const isMentalConsultInquiry = (inquiryTimingType = '') => (/^mentalConsult$/i.test(inquiryTimingType));

const format = {
  diagText: (inquiryDiagnosis = {}, inquiryTimingType = '') => {
    const { staffDescription = '', diseases = '' } = inquiryDiagnosis;
    if (isMentalConsultInquiry(inquiryTimingType)) {
      return staffDescription;
    }
    const arr = [];

    // 根据产品，如果不存在staffDescription，再用diseases
    if (!staffDescription && diseases) {
      JSON.parse(diseases).forEach((d) => {
        const { resName = '' } = d;
        arr.push(resName);
      });
    }

    if (staffDescription) {
      arr.push(staffDescription);
    }
    return arr.length ? arr.join('；') : '无';
  },
  // 格式化日期
  date: function (date, fmt) {
    if (!date || !fmt) {
      return date;
    }
    if (date.length == 8) {
      date = date.substr(0, 4) + '-' + date.substr(4, 2) + '-' + date.substr(6, 2)
    }
    if (typeof date !== 'number' && date.length <= 20) { //小于20 ，时间格式为  “yyyy-MM-dd hh:mm:ss” 有时分秒
      date = date.toString().replace(/-/g, "/");
    };
    date = new Date(date);
    // interface oProps {
    //   "M+": number;
    //   "d+": number; //日
    //   "h+": number; //小时
    //   "m+": number; //分
    //   "s+": number; //秒
    //   "q+": any; //季度
    //   "S": number; //毫秒
    //   [k: string]: any;
    // };
    var o = {
      "M+": date.getMonth() + 1, //月份
      "d+": date.getDate(), //日
      "h+": date.getHours(), //小时
      "m+": date.getMinutes(), //分
      "s+": date.getSeconds(), //秒
      "q+": Math.floor((date.getMonth() + 3) / 3), //季度
      "S": date.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
      if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
  },
  isUrl: (url) => {
    return /^https?:\/\//.test(url);
  },
  imageUrl: function (url, isCompress) {
    if(format.isUrl(url)) {
      return url;
    }
    isCompress = isCompress ? '@s' : '';
    if (/^\/v1\/attchment\/downloadFile.*/.test(url)) {
      const baseUrl = config.baseAPI['zaApi'].host;
      return `${baseUrl}${url}${isCompress}`
    }
    return '';
  },
  deserialize: (value, firstCon = '&', secondCon = '=') => {
    let result = {};
    return value.replace('?', '').split(firstCon).reduce((prev, elem) => {
      const index = elem.indexOf(secondCon);
      if (index === -1) {
        prev[elem] = '';
      } else {
        prev[elem.slice(0, index)] = elem.slice(index + 1);
      }
      return prev;
    }, result);
  },
  serialize: (data, firstCon = '&', secondCon = '=') => {
    return Object.keys(data).reduce((prev, key) => {
      const value = data[key]
      if (value != null) {
        prev += (prev && firstCon) + key + (String(value) && secondCon + value)
      }
      return prev
    }, '')
  },
  patch: (num) => {
    return num < 10 ? '0' + num : '' + num;
  },
  noPassByMobile(str) {
    if (str) {
      var pat = /(\d{3})\d*(\d{4})/;
      return str.replace(pat, '$1****$2');
    } else {
      return "";
    }
  },
  GetAgeByBirthday(birthday) {
    function parseDate(str) {
      var date = new Date()
      var times = str.split("-")
      date.setFullYear(times[0])
      date.setMonth((times[1] - 1))
      date.setDate(times[2])
      return date
    }

    var age = -1;
    var today = new Date();
    var todayYear = today.getFullYear();
    var todayMonth = today.getMonth() + 1;
    var todayDay = today.getDate();
    var bday = parseDate(birthday);
    var birthdayYear = bday.getFullYear();
    var birthdayMonth = bday.getMonth() + 1;
    var birthdayDay = bday.getDate();

    if (todayYear - birthdayYear < 0) {
      Taro.showToast({
        title: '出生日期选择错误!',
        icon: 'none',
        duration: 4000,
      });
    } else {
      if (todayMonth - birthdayMonth > 0) {
        age = todayYear - birthdayYear;
      } else if (todayMonth - birthdayMonth < 0) {
        age = todayYear - birthdayYear - 1;
      } else if (todayMonth - birthdayMonth === 0) {
        if (todayDay - birthdayDay >= 0) {
          age = todayYear - birthdayYear;
        } else {
          age = todayYear - birthdayYear - 1;
        }
      }
    }
    return age == 0 ? 1 : age;
  }
};
export default format;
