// //本地环境
// module.exports = {
//   zaApi: {
//     // host:"http://za-asclepius-patient-bops.test.za-tech.net",   //测试
//     host: "https://patient-tst.za-doctor.com",
//     // TODO: 测试
//     // webview: "http://localhost:8080",  //h5地址
//     webview: "https://online-tst.za-doctor.com",
//     // webview: "localhost:8080",
//     useChannelSource: 'CSN13500106'
//   },
//   mallApi: {
//     host: "https://mall-test.zhonganbio.com"
//   },
//   mkApi: {
//     host: "https://www.fastmock.site",
//   },
//   lnjkPay: {
//     // host: "http://test.ssish.com",   //椋鸟健康支付接口
//     host: "https://wechatpay.yutai365.com",   //椋鸟健康支付接口
//   },
//   WEBSOCKET_API: {
//     host: 'wss://nireus-tst.za-doctor.com/ws',
//     // host: 'ws://za-nireus.test.za-tech.net/ws',
//     port: '',
//   },
// };


//本地环境
export default {
  zaApi: {
    // host:"http://za-asclepius-patient-bops.test.za-tech.net",   //测试
    host: "https://patient-tst.za-doctor.com",
    // TODO: 测试
    // webview: "http://localhost:8080",  //h5地址
    webview: "https://online-tst.za-doctor.com",
    // webview: "localhost:8080",
    useChannelSource: 'CSN13500106'
  },
  mallApi: {
    host: "https://mall-test.zhonganbio.com"
  },
  mkApi: {
    host: "https://www.fastmock.site",
  },
  lnjkPay: {
    // host: "http://test.ssish.com",   //椋鸟健康支付接口
    host: "https://wechatpay.yutai365.com",   //椋鸟健康支付接口
  },
  WEBSOCKET_API: {
    host: 'wss://nireus-tst.za-doctor.com/ws',
    // host: 'ws://za-nireus.test.za-tech.net/ws',
    port: '',
  },
};
