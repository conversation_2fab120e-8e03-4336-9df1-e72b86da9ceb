const MOBILE_REG = /^1[2-9][0-9]{9}$/;
// const NAME_REG = /^([\u4e00-\u9fa5][\u4e00-\u9fa5·]+|[a-zA-Z·]+)$/;
const NAME_REG = /^([\u4e00-\u9fa5][\u4e00-\u9fa5·' ']+|[a-zA-Z·' ']+)$/
const PASSPORT_REG = /^[a-zA-Z0-9/()]{5,20}$/;// 护照
const NORMAL_REG = /^[^~!@#$%^*+|}{"?/'\\=`\s+]*$/;
const MINI_TABBAR_PAGE_REG = /^\/pages\/((index|personalcenter) | \/(mall)\/index)$/i; //验证是否为小程序的 tabBar 页面；
function isRule(regText, value) {
  if (!value || value.length === 0) return true;

  const reg = new RegExp(regText);
  if (!reg.test(value)) {
    return false;
  }
  return true;
}

const validate = {
  isMobile: (mobile) => {
    return isRule(MOBILE_REG, mobile);
  },
  isUsername: (name) => {
    return isRule(NAME_REG, name);
  },
  isEmpty: (data) => {
    return !(data && (data.length > 0));
  },
  isSame: (data1, data2) => {
    return data1 === data2;
  },
  isPassport: (val) => {
    return isRule(PASSPORT_REG, val);
  },
  isText: (val) => {
    return isRule(NORMAL_REG, val);
  },
  isTabBarPage: (path) => {
    return isRule(MINI_TABBAR_PAGE_REG, path);
  },
  isIdCard: (card) => {
    if (!card) return false;
    // let num = card.toUpperCase();
    let num = card;
    // 身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X。
    if (!(/(^\d{15}$)|(^\d{17}([0-9]|X)$)/.test(num))) {
      return false;
    }
    // 校验位按照ISO 7064:1983.MOD 11-2的规定生成，X可以认为是数字10。
    // 下面分别分析出生日期和校验位
    let re;
    let birthday; let sex;
    const len = num.length;
    if (len === 15) {
      // 获取出生日期
      birthday = `19${card.substring(6, 8)}-${card.substring(8, 10)}-${card.substring(10, 12)}`;
      // 获取性别
      sex = parseInt(card.substr(14, 1), 10) % 2 === 1 ? 'M' : 'F';

      re = new RegExp(/^(\d{6})(\d{2})(\d{2})(\d{2})(\d{3})$/);
      const arrSplit = num.match(re);

      // 检查生日日期是否正确
      const dtmBirth = new Date(`19${arrSplit[2]}/${arrSplit[3]}/${arrSplit[4]}`);
      const bGoodDay = (dtmBirth.getFullYear() === Number(arrSplit[2])) && ((dtmBirth.getMonth() + 1) === Number(arrSplit[3])) && (dtmBirth.getDate() === Number(arrSplit[4]));
      if (!bGoodDay) {
        return false;
      }
      // 将15位身份证转成18位
      // 校验位按照ISO 7064:1983.MOD 11-2的规定生成，X可以认为是数字10。
      const arrInt = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
      const arrCh = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
      let nTemp = 0;
      let i;

      num = `${num.substr(0, 6)}19${num.substr(6, num.length - 6)}`;
      for (i = 0; i < 17; i++) {
        nTemp += num.substr(i, 1) * arrInt[i];
      }
      num += arrCh[nTemp % 11];
    } else if (len === 18) {
      // 获取出生日期
      birthday = `${card.substring(6, 10)}-${card.substring(10, 12)}-${card.substring(12, 14)}`;
      // 获取性别
      sex = parseInt(card.substr(16, 1), 10) % 2 === 1 ? 'M' : 'F';

      re = new RegExp(/^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X)$/);
      const arrSplit = num.match(re);

      // 检查生日日期是否正确
      const dtmBirth = new Date(`${arrSplit[2]}/${arrSplit[3]}/${arrSplit[4]}`);
      dtmBirth.setDate(arrSplit[4]);
      const bGoodDay = (dtmBirth.getFullYear() === Number(arrSplit[2])) && ((dtmBirth.getMonth() + 1) === Number(arrSplit[3])) && (dtmBirth.getDate() === Number(arrSplit[4]));
      if (!bGoodDay) {
        return false;
      }
      // 检验18位身份证的校验码是否正确。
      // 校验位按照ISO 7064:1983.MOD 11-2的规定生成，X可以认为是数字10。
      const arrInt = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
      const arrCh = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
      let nTemp = 0;
      let i;
      for (i = 0; i < 17; i++) {
        nTemp += num.substr(i, 1) * arrInt[i];
      }
      const valnum = arrCh[nTemp % 11];
      if (valnum !== num.substr(17, 1)) {
        return false;
      }
    }
    return {
      birthday,
      sex,
    };
  },

};


export default validate;
