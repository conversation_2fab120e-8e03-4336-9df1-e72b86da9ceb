import storage from "./storage";
import { login } from "../utils/fetch";
import { safeEncode } from "./safeBase64";


async function getSearch() {
  const needLogin = !storage.get('Token');
  if (needLogin && !wx.getSystemInfoSync().inFinChat) {
    console.log('needLogin', storage.get('Token'));
    await login();
  }
  const expire = Math.floor((storage.get('Token__expires__') - new Date().getTime()) / (60 * 1000));
  return {
    token: storage.get('Token'),
    expire: 6 * 60,
    channelResourceCode: storage.get('channelResourceCode'),
    channelSource: storage.get('channelSource'),
    isReal: storage.get('realName'),
    maskActId: safeEncode(storage.get('maskActId')),
    maskusId: encodeURIComponent(storage.get('userId')),
    maskusSafeId: safeEncode(storage.get('maskusId')),
    userId: encodeURIComponent(storage.get('userId')),
    openId: storage.get('openId'),
    userBlackFlag: storage.get('userBlackFlag'),
    ownAppletAppid: wx.getSystemInfoSync().inFinChat ? '' : storage.get('ownAppletAppid')||'wx3c564538ea8e3905', //用于H5内判断 是否在自有互医小程序内
    channelOrigin: storage.get('channelOrigin'),
    channelCode: storage.get('channelCode'),
  }
}

export default getSearch;
