import React, { useReducer } from 'react';

export const SessionContext = React.createContext<any>(null);

const initialState = {};

function reducer(state, action) {
  switch (action.type) {
    case 'SESSION_SET':
      return { ...state, [action.payload.key]: action.payload.value };
    default:
      throw new Error();
  }
}

const Provider = ({ children }) => {
  const [state, dispatch] = useReducer(reducer, initialState);

  const setter = (key) => {
    return (value) => {
      dispatch({ type: 'SESSION_SET', payload: { key, value } });
    };
  };

  return <SessionContext.Provider value={[state, setter]}>{children}</SessionContext.Provider>;
};

export default Provider;
