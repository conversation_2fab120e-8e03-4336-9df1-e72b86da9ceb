import React, { useCallback, useMemo, useContext } from 'react';
// import useConfig from '@/hooks/useConfig';
import { subscribeWxMessageByTmpls } from '@/utils/subscribe';

export const SubscribeMaskContext = React.createContext<any>(null);

export const useMask = () => {
  const [state, setState] = React.useState(false);
  const show = useCallback(() => {
    setState(true);
  }, [setState]);

  const hide = useCallback(() => {
    setState(false);
  }, [setState]);

  return useMemo(() => {
    return {
      show,
      hide,
      visible: state,
    };
  }, [hide, show, state]);
};

export const useSubscribeWxMessage = () => {
  const subscribeMask = useContext(SubscribeMaskContext);
  // const { data } = useConfig();
  const { data } = {};
  const subscribe = (key, cb) => {
    subscribeWxMessageByTmpls({
      tmplIds: data?.subscribe_tmplIds?.[key],
      onBeforeRequestSubscribeMessage: () => {
        subscribeMask?.show();
      },
      onCompleteRequestSubscribeMessage: () => {
        subscribeMask?.hide();
      },
    }, cb);
  };
  return subscribe;
};

const Provider = ({ children }) => {
  const mask = useMask();
  return <SubscribeMaskContext.Provider value={mask}>{children}</SubscribeMaskContext.Provider>;
};

export default Provider;
