// eslint-disable taro/manipulate-jsx-as-array
import React from 'react';
import SessionProvider from './session';

interface Props {
  components: Array<React.JSXElementConstructor<React.PropsWithChildren<any>>>;
  children: React.ReactNode;
}

function Compose(props: Props) {
  const { components = [], children } = props;

  return (
    <>
      {components?.reduceRight((acc, Comp) => {
        return <Comp>{acc}</Comp>;
      }, children)}
    </>
  );
}

const Global = ({ children }) => {
  return (
    <Compose components={[SessionProvider]}>
      {children}
    </Compose>
  );
};

export default Global;
