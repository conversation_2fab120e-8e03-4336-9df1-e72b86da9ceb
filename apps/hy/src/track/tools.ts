export const filterShareMessageXflowParam = (url) => {
  // const decodedURL = decodeURIComponent(url);
  const keys = ['xflow_d_t_wv', 'sampshare'];
  // Create a regular expression pattern to match the keys in the URL
  const pattern = new RegExp(`([&?])|(${keys.join('|')})=[^&]+(&|$)`, 'gi');

  // Remove the matched parameters from the URL
  const modifiedURL = url.replace(pattern, '$1').replace(/[?&]$/, '');
  return modifiedURL;
};
