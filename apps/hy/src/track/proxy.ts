import { document } from '@tarojs/runtime';
import track from 'shared/track';

const oldPage = Page;
Page = function (pageOption) {
  const oldEvent = pageOption.eh;
  pageOption.eh = (event) => {
    if (event.type === 'tap') {
      const dataSet = document.getElementById(event.currentTarget.id)?.dataset;
      if (dataSet?.ilog) {
        // const { CommonClick, ...reset } = dataSet.ilog;
        // if (CommonClick) {
        //   track.click(CommonClick, reset);
        // } else {
        //   track('click', 'XCXCustom', dataSet['ilog']);
        // }
        track.click(dataSet.ilog);
      }
    }
    oldEvent(event);
  };
  /* eslint-disable-next-line prefer-rest-params */
  oldPage.apply(this, arguments);
};
