import config from '@/config';
import track from 'shared/track';
import AppConfig from '../config';

track.setPara({
  ...AppConfig.track,
  name: 'zaMini',
  extendsInfo: {
    ENV: config.env,
  },
  server_url: 'https://zhongan-xflow-nginx.zhongan.com/cloud_xcx_sdk',
});

export const setExtendsInfo = (extendsInfo = {}) => {
  track.setPara({
    extendsInfo: {
      ...extendsInfo,
      ENV: config.env,
    },
  });
};

export * from './tools';
export default track;
