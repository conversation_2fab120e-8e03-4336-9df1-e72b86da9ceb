import path from 'path';
import UnoCSS from 'unocss/webpack';
import TsconfigPathsPlugin from 'tsconfig-paths-webpack-plugin';

const alias = {
  react: path.resolve(__dirname, '../', 'node_modules/react'),
  'react-dom': path.resolve(__dirname, '../', 'node_modules/@tarojs/react'),
  // 'react-dom/client': path.resolve(__dirname, '../', 'node_modules/react-dom'),
  '@tarojs/components': path.resolve(
    __dirname,
    '../',
    `node_modules/@tarojs/plugin-platform-${process.env.TARO_ENV}/dist/components-react`,
  ),
};

const config = {
  projectName: 'hfe-zunxiang-mp',
  date: '2020-7-1',
  designWidth: 750,
  deviceRatio: {
    640: 2.34 / 2,
    750: 1,
    828: 1.81 / 2,
  },
  sourceRoot: 'src',
  // outputRoot: 'dist',
  outputRoot: `dist/${process.env.TARO_ENV}`,
  compiler: {
    type: 'webpack5',
    prebundle: {
      enable: false,
    },
  },
  cache: {
    enable: false, // Webpack 持久化缓存配置，建议开启。默认配置请参考：https://docs.taro.zone/docs/config-detail#cache
  },
  defineConstants: {
    __CLIENT__: 'true',
    __SERVER__: 'false',
    __DEVELOPMENT__: "''",
    ENVIRONMENT_KEY: "''",
    RELEASE: "''",
  },
  sass: {
    data: '$env: mini;',
  },
  plugins: [
    '@tarojs/plugin-html',
    ['@tarojs/plugin-inject', {
      components: {
        Button: {
          bindgetidentityinfo: '',
        },
      },
    }],
  ],
  copy: {
    patterns: [],
    options: {},
  },
  alias: {
    ...alias,
    '@tarojs/runtime': require.resolve('@tarojs/runtime'),
    '@tarojs/shared': require.resolve('@tarojs/shared'),
    // '@/shared': path.resolve(__dirname, '../../../', '/packages/shared'),
    '@': path.resolve(__dirname, '..', 'src'),
    '@/package': path.resolve(__dirname, '..', 'package.json'),
    '@/project': path.resolve(__dirname, '..', 'project.config.json'),
  },
  framework: 'react',
  mini: {
    optimizeMainPackage: {
      enable: true,
    },
    postcss: {
      pxtransform: {
        enable: true,
        config: {},
      },
      url: {
        enable: true,
        config: {
          limit: 1024, // 设定转换尺寸上限
        },
      },
      cssModules: {
        enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[local]__[hash:7]',
        },
      },
    },
    miniCssExtractPluginOption: {
      ignoreOrder: true,
    },
    compile: {
      include: [(modulePath) => {
        if (/node_modules|shared/.test(modulePath)) {
          return true;
        }
        if (modulePath.indexOf('za-dplatform-h5-v2') >= 0) {
          // console.log('ggggggggggg')
          return true;
        }
        return false;
      }],
      exclude: [path.resolve(__dirname, '../../../', 'packages/shared/track/za/index.js')],
    },
    webpackChain(chain) {
      chain.resolve.plugin('tsconfig-paths').use(TsconfigPathsPlugin);
      chain.module.rule('script').test(/\.(mjs|[tj]sx?)$/i);
      chain.plugin('unocss').use(UnoCSS());
      chain.resolve.extensions
          .prepend('.mini.ts')
          .prepend('.mini.js')
          .prepend('.mini.jsx')
          .prepend('.mini.tsx')
          .add('.js')
          .add('.jsx')
          .add('.ts')
          .add('.tsx')
          .add('.json');

        chain.resolve.modules
          .add('node_modules')
          //.add(path.resolve(__dirname, '../../../packages'))
          .end();
    },
  },
  h5: {
    publicPath: '/',
    staticDirectory: 'static',
    postcss: {
      autoprefixer: {
        enable: true,
        config: {},
      },
      cssModules: {
        enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]',
        },
      },
    },
  },
};

module.exports = (merge) => {
  let copyPath = {};
  if (process.env.TARO_ENV === 'weapp') {
    copyPath = {
      copy: {
        patterns: [
        ],
      },
    };
  }

  if (process.env.NODE_ENV === 'development') {
    return merge({}, config, require('./dev'), copyPath);
  }
  return merge({}, config, require('./prod'), copyPath);
};
