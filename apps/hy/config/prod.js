module.exports = {
  env: {
    NODE_ENV: '"production"',
    DEPLOY_ENV: JSON.stringify(process.env.DEPLOY_ENV),
  },
  defineConstants: {},
  mini: {
    enableSourceMap: true,
    sourceMapType: 'source-map',
    webpackChain(chain) {
      chain.module
        .rule('source-map')
        .test(/\.(mjs|[tj]sx?)$/)
        .pre()
        .exclude
        .add(/@tarojs/)
        .end()
        .use()
        .loader('source-map-loader');

      // .options({
      //   filterSourceMappingUrl() {
      //     return false;
      //   }
      // });
      // chain.set('ignoreWarnings', [/Failed to parse source map/]);

      // console.log(chain.toConfig().module.rules)
      // console.log(chain.toString())
    },
  },
  h5: {
    /**
     * 如果h5端编译后体积过大，可以使用webpack-bundle-analyzer插件对打包体积进行分析。
     * 参考代码如下：
     * webpackChain (chain) {
     *   chain.plugin('analyzer')
     *     .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin, [])
     * }
     */
  },
};
