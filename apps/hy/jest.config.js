module.exports = {
  verbose: true,
  testEnvironment: 'jsdom',
  // preset: 'ts-jest',
  moduleNameMapper: {
    '@tarojs/components': '@tarojs/components/dist-h5/react',
  },
  moduleFileExtensions: ['js', 'jsx', 'ts', 'tsx', 'json'],
  rootDir: __dirname,
  testMatch: ['<rootDir>/src/test/**/*.test.js', '<rootDir>/src/test/**/test.js'],
  transform: {
    '^.+\\.js?$': 'babel-jest',
  },
  transformIgnorePatterns: ['<rootDir>/node_modules/'],
  // setupFiles: [
  //   '<rootDir>/src/test/setup',
  // ],
  collectCoverage: true,
  coverageReporters: ['html', 'text-summary', 'lcov'],
  coverageDirectory: '<rootDir>/src/test/coverage',

};
